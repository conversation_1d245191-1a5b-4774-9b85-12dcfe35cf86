{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\FloatingBrainwaveAI.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Tb<PERSON><PERSON>ot, TbMinus, TbMaximize, TbX } from 'react-icons/tb';\nimport { chatWithChatGPT, uploadImg } from '../apicalls/chat';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { useSelector } from 'react-redux';\nimport { useLocation } from 'react-router-dom';\nimport ContentRenderer from './ContentRenderer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FloatingBrainwaveAI = () => {\n  _s();\n  const {\n    isKiswahili\n  } = useLanguage();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const location = useLocation();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMaximized, setIsMaximized] = useState(false);\n  const [messages, setMessages] = useState([]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const inputRef = useRef(null);\n  const isMobile = window.innerWidth <= 768;\n\n  // Load saved chat history on component mount\n  useEffect(() => {\n    const savedMessages = localStorage.getItem('brainwave_chat_history');\n    if (savedMessages) {\n      try {\n        const parsedMessages = JSON.parse(savedMessages);\n        setMessages(parsedMessages);\n      } catch (error) {\n        console.error('Error loading chat history:', error);\n      }\n    }\n  }, []);\n\n  // Save chat history whenever messages change\n  useEffect(() => {\n    if (messages.length > 0) {\n      localStorage.setItem('brainwave_chat_history', JSON.stringify(messages));\n    }\n  }, [messages]);\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [messages]);\n  const handleImageSelect = event => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Memoize initial message to prevent unnecessary re-renders\n  const initialMessage = React.useMemo(() => {\n    const content = isKiswahili ? `Hujambo! Mimi ni Brainwave AI, msaidizi wako wa masomo. Niko hapa kukusaidia na maswali yoyote ya masomo. Je, una swali lolote?` : `Hello! I'm Brainwave AI, your educational assistant. I'm here to help you with any study questions you might have. What would you like to learn about today?`;\n    return {\n      role: \"assistant\",\n      content\n    };\n  }, [isKiswahili]);\n  const sendMessage = async () => {\n    if (!input.trim() && !selectedImage) return;\n    const userMessage = input.trim();\n    const imageFile = selectedImage;\n    setInput('');\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n    let imageUrl = null;\n    if (imageFile) {\n      try {\n        setIsLoading(true);\n        console.log('Uploading image:', imageFile.name, imageFile.size);\n\n        // Create FormData for image upload\n        const formData = new FormData();\n        formData.append('image', imageFile);\n        console.log('Sending image upload request...');\n        const uploadResponse = await uploadImg(formData);\n        console.log('Upload response:', uploadResponse);\n        if (uploadResponse.success) {\n          var _uploadResponse$data;\n          imageUrl = ((_uploadResponse$data = uploadResponse.data) === null || _uploadResponse$data === void 0 ? void 0 : _uploadResponse$data.url) || uploadResponse.url;\n          console.log('Image uploaded successfully:', imageUrl);\n        } else {\n          throw new Error(uploadResponse.message || 'Image upload failed');\n        }\n      } catch (error) {\n        console.error('Error uploading image:', error);\n        const errorMessage = isKiswahili ? \"Kuna tatizo la kupakia picha. Tafadhali jaribu tena.\" : \"There was an error uploading the image. Please try again.\";\n        setMessages(prev => [...prev, {\n          role: \"assistant\",\n          content: errorMessage\n        }]);\n        setIsLoading(false);\n        return;\n      }\n    }\n    const newUserMessage = imageUrl ? {\n      role: \"user\",\n      content: [{\n        type: \"text\",\n        text: userMessage || \"Please analyze this image\"\n      }, {\n        type: \"image_url\",\n        image_url: {\n          url: imageUrl\n        }\n      }]\n    } : {\n      role: \"user\",\n      content: userMessage\n    };\n    setMessages(prev => [...prev, newUserMessage]);\n\n    // Enhanced system prompt with formatting instructions\n    const systemPrompt = isKiswahili ? 'Jibu kwa lugha ya Kiswahili tu. Wewe ni msaidizi wa masomo wa Tanzania. Tumia lugha rahisi na ya kielimu. Ikiwa ni swali la picha, soma picha kwa makini na ueleze hatua kwa hatua. Tumia:\\n- Nukta za bullet (-) kwa orodha\\n- Nambari (1., 2., 3.) kwa hatua\\n- **Herufi nzito** kwa mambo muhimu\\n- ## kwa vichwa vikuu' : 'You are an educational assistant for Tanzanian students. Be helpful and provide clear, step-by-step explanations. If this is an image question, carefully analyze the image content and provide detailed solutions. Format your responses using:\\n- Bullet points (-) for lists\\n- Numbers (1., 2., 3.) for steps\\n- **Bold text** for important points\\n- ## for main headings\\n- Organize information clearly with proper spacing';\n    const chatPayload = {\n      messages: [{\n        role: \"system\",\n        content: systemPrompt\n      }, ...messages, newUserMessage]\n    };\n    try {\n      setIsLoading(true);\n      const response = await chatWithChatGPT(chatPayload);\n      if (response.success) {\n        setMessages(prev => [...prev, {\n          role: \"assistant\",\n          content: response.data\n        }]);\n      } else {\n        throw new Error(response.message || 'Failed to get response');\n      }\n    } catch (error) {\n      const errorMessage = isKiswahili ? \"Samahani, kuna tatizo la mtandao. Tafadhali jaribu tena.\" : \"Sorry, there was a network error. Please try again.\";\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: errorMessage\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleKeyDown = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n  const clearChatHistory = () => {\n    setMessages([]);\n    localStorage.removeItem('brainwave_chat_history');\n  };\n\n  // Hide on subscription page\n  if (!user || location.pathname.includes('/subscription')) return null;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [!isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => setIsOpen(true),\n      style: {\n        position: 'fixed',\n        bottom: isMobile ? '20px' : '30px',\n        right: isMobile ? '20px' : '30px',\n        width: isMobile ? '50px' : '60px',\n        height: isMobile ? '50px' : '60px',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        borderRadius: '50%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        cursor: 'pointer',\n        boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\n        zIndex: 1000,\n        transition: 'all 0.3s ease',\n        border: '3px solid rgba(255, 255, 255, 0.2)',\n        backdropFilter: 'blur(10px)'\n      },\n      children: /*#__PURE__*/_jsxDEV(TbRobot, {\n        style: {\n          color: 'white',\n          fontSize: isMobile ? '24px' : '28px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 9\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          bottom: isMaximized ? '20px' : isMobile ? '20px' : '30px',\n          right: isMaximized ? '20px' : isMobile ? '20px' : '30px',\n          left: isMaximized ? '20px' : 'auto',\n          top: isMaximized ? '20px' : 'auto',\n          width: isMaximized ? 'calc(100vw - 40px)' : isMobile ? '320px' : '380px',\n          height: isMaximized ? 'calc(100vh - 40px)' : isMobile ? '500px' : '600px',\n          background: 'rgba(255, 255, 255, 0.95)',\n          borderRadius: isMaximized ? '16px' : '20px',\n          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\n          zIndex: 1000,\n          display: 'flex',\n          flexDirection: 'column',\n          border: '1px solid rgba(255, 255, 255, 0.2)',\n          backdropFilter: 'blur(20px)',\n          overflow: 'hidden',\n          transition: 'all 0.3s ease'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: isMobile ? '16px 20px' : '20px 24px',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TbRobot, {\n              style: {\n                fontSize: isMobile ? '20px' : '24px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: 0,\n                  fontSize: isMobile ? '16px' : '18px',\n                  fontWeight: '600'\n                },\n                children: \"Brainwave AI\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: isMobile ? '11px' : '12px',\n                  opacity: 0.9\n                },\n                children: isKiswahili ? 'Msaidizi wako wa masomo' : 'Your study assistant'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '6px'\n            },\n            children: [messages.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: clearChatHistory,\n              style: {\n                background: 'rgba(255, 255, 255, 0.2)',\n                border: 'none',\n                borderRadius: isMobile ? '6px' : '8px',\n                width: isMobile ? '28px' : '32px',\n                height: isMobile ? '28px' : '32px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                backdropFilter: 'blur(10px)'\n              },\n              title: isKiswahili ? 'Futa mazungumzo' : 'Clear chat',\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'white',\n                  fontSize: isMobile ? '14px' : '16px'\n                },\n                children: \"\\uD83D\\uDDD1\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsMaximized(!isMaximized),\n              style: {\n                background: 'rgba(255, 255, 255, 0.2)',\n                border: 'none',\n                borderRadius: isMobile ? '6px' : '8px',\n                width: isMobile ? '28px' : '32px',\n                height: isMobile ? '28px' : '32px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                backdropFilter: 'blur(10px)'\n              },\n              title: isMaximized ? isKiswahili ? 'Punguza' : 'Minimize' : isKiswahili ? 'Kubwa' : 'Maximize',\n              children: isMaximized ? /*#__PURE__*/_jsxDEV(TbMinus, {\n                style: {\n                  color: 'white',\n                  fontSize: isMobile ? '16px' : '18px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(TbMaximize, {\n                style: {\n                  color: 'white',\n                  fontSize: isMobile ? '16px' : '18px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setIsOpen(false);\n                setIsMaximized(false);\n              },\n              style: {\n                background: 'rgba(255, 255, 255, 0.2)',\n                border: 'none',\n                borderRadius: isMobile ? '6px' : '8px',\n                width: isMobile ? '28px' : '32px',\n                height: isMobile ? '28px' : '32px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                backdropFilter: 'blur(10px)'\n              },\n              title: isKiswahili ? 'Funga' : 'Close',\n              children: /*#__PURE__*/_jsxDEV(TbX, {\n                style: {\n                  color: 'white',\n                  fontSize: isMobile ? '16px' : '18px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            padding: isMobile ? '16px' : '20px',\n            overflowY: 'auto',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '16px'\n          },\n          className: \"custom-scrollbar\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#ffffff',\n              padding: isMobile ? '12px 16px' : '16px 20px',\n              borderRadius: '16px',\n              border: '2px solid #e5e7eb',\n              alignSelf: 'flex-start',\n              maxWidth: '85%',\n              color: '#1f2937',\n              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#1f2937',\n                fontSize: '14px',\n                lineHeight: '1.6'\n              },\n              children: initialMessage.content || 'Hello! I\\'m Brainwave AI, your educational assistant.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              alignSelf: message.role === 'user' ? 'flex-end' : 'flex-start',\n              maxWidth: '85%'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: message.role === 'user' ? 'linear-gradient(135deg, #3b82f6, #1d4ed8)' : '#ffffff',\n                color: message.role === 'user' ? '#ffffff' : '#1f2937',\n                padding: isMobile ? '12px 16px' : '16px 20px',\n                borderRadius: '16px',\n                border: message.role === 'user' ? 'none' : '2px solid #e5e7eb',\n                fontSize: isMobile ? '13px' : '14px',\n                lineHeight: '1.6',\n                boxShadow: message.role === 'user' ? 'none' : '0 2px 8px rgba(0, 0, 0, 0.1)'\n              },\n              children: Array.isArray(message.content) ? message.content.map((item, itemIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [item.type === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: 'inherit',\n                    fontSize: 'inherit',\n                    lineHeight: 'inherit'\n                  },\n                  children: item.text || 'Message content'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 29\n                }, this), item.type === 'image_url' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '8px',\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: item.image_url.url,\n                    alt: \"User upload\",\n                    style: {\n                      maxWidth: '100%',\n                      height: 'auto',\n                      borderRadius: '12px',\n                      maxHeight: isMobile ? '180px' : '250px',\n                      objectFit: 'contain',\n                      border: '3px solid #e2e8f0',\n                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n                      background: '#f8fafc',\n                      cursor: 'pointer'\n                    },\n                    onClick: () => {\n                      window.open(item.image_url.url, '_blank');\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      position: 'absolute',\n                      bottom: '8px',\n                      right: '8px',\n                      background: 'rgba(0, 0, 0, 0.7)',\n                      color: 'white',\n                      padding: '4px 8px',\n                      borderRadius: '6px',\n                      fontSize: '10px',\n                      fontWeight: '500'\n                    },\n                    children: [\"\\uD83D\\uDCF8 \", isKiswahili ? 'Bonyeza kuona kikubwa' : 'Click to enlarge']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 29\n                }, this)]\n              }, itemIndex, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 25\n              }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: 'inherit',\n                  fontSize: 'inherit',\n                  lineHeight: 'inherit'\n                },\n                children: message.role === 'assistant' ? /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                  text: message.content || 'AI response'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 27\n                }, this) : message.content || 'Message content'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 17\n          }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              alignSelf: 'flex-start',\n              maxWidth: '85%'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#ffffff',\n                padding: isMobile ? '12px 16px' : '16px 20px',\n                borderRadius: '16px',\n                border: '2px solid #e5e7eb',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [1, 2, 3].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    background: '#3b82f6',\n                    borderRadius: '50%',\n                    animation: `bounce 1.4s infinite ease-in-out both`,\n                    animationDelay: `${(i - 1) * 0.16}s`\n                  }\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: isMobile ? '12px' : '13px',\n                  color: '#6b7280'\n                },\n                children: isKiswahili ? 'Inafikiri...' : 'Thinking...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px',\n            padding: '12px',\n            background: 'linear-gradient(135deg, #f0f9ff, #e0f2fe)',\n            borderRadius: '12px',\n            border: '2px solid #0ea5e9',\n            boxShadow: '0 4px 12px rgba(14, 165, 233, 0.15)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: imagePreview,\n                alt: \"Preview\",\n                style: {\n                  width: isMobile ? '80px' : '100px',\n                  height: isMobile ? '80px' : '100px',\n                  objectFit: 'cover',\n                  borderRadius: '8px',\n                  border: '2px solid #0ea5e9',\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: removeImage,\n                style: {\n                  position: 'absolute',\n                  top: '-6px',\n                  right: '-6px',\n                  width: '24px',\n                  height: '24px',\n                  background: '#ef4444',\n                  color: 'white',\n                  borderRadius: '50%',\n                  border: '2px solid white',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '12px',\n                  fontWeight: 'bold',\n                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'\n                },\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '14px',\n                  fontWeight: '600',\n                  color: '#0c4a6e',\n                  margin: '0 0 4px 0',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '6px'\n                },\n                children: [\"\\uD83D\\uDCF8 \", isKiswahili ? 'Picha Imepakiwa' : 'Image Attached']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '11px',\n                  color: '#0369a1',\n                  margin: 0\n                },\n                children: (selectedImage === null || selectedImage === void 0 ? void 0 : selectedImage.name) || 'image.png'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: isMobile ? '6px' : '8px',\n            background: '#f8fafc',\n            borderRadius: isMobile ? '12px' : '16px',\n            padding: isMobile ? '6px' : '8px',\n            border: '2px solid #e2e8f0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            style: {\n              background: '#3b82f6',\n              border: 'none',\n              borderRadius: '6px',\n              width: '24px',\n              height: '24px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              boxShadow: '0 1px 3px rgba(59, 130, 246, 0.3)'\n            },\n            title: isKiswahili ? \"Pakia picha\" : \"Upload image\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: 'white',\n                fontSize: '14px',\n                fontWeight: 'bold'\n              },\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            ref: inputRef,\n            value: input,\n            onChange: e => setInput(e.target.value),\n            onKeyDown: handleKeyDown,\n            placeholder: isKiswahili ? \"Uliza chochote...\" : \"Ask me anything...\",\n            rows: 1,\n            style: {\n              flex: 1,\n              border: 'none',\n              background: 'transparent',\n              outline: 'none',\n              fontSize: isMobile ? '12px' : '14px',\n              color: '#334155',\n              padding: isMobile ? '10px 12px' : '12px 16px',\n              fontFamily: 'inherit'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: sendMessage,\n            disabled: !input.trim() && !selectedImage,\n            style: {\n              background: input.trim() || selectedImage ? '#3b82f6' : '#e2e8f0',\n              border: 'none',\n              borderRadius: '6px',\n              width: '24px',\n              height: '24px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: input.trim() || selectedImage ? 'pointer' : 'not-allowed',\n              transition: 'all 0.2s ease'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: 'white',\n                fontSize: '12px'\n              },\n              children: \"\\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleImageSelect,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: isMobile ? '9px' : '11px',\n            color: '#94a3b8',\n            textAlign: 'center',\n            margin: isMobile ? '6px 0 0 0' : '8px 0 0 0'\n          },\n          children: isMobile ? isKiswahili ? 'Enter • + Pakia picha' : 'Enter • + Upload image' : isKiswahili ? 'Enter kusonga • + Pakia picha' : 'Press Enter to send • + Upload image'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this)\n    }, void 0, false), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes bounce {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n\n        .custom-scrollbar::-webkit-scrollbar {\n          width: 6px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-track {\n          background: transparent;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 3px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(FloatingBrainwaveAI, \"xFwfL9t3USdaRvMhBibo9OuPCho=\", false, function () {\n  return [useLanguage, useSelector, useLocation];\n});\n_c = FloatingBrainwaveAI;\nexport default FloatingBrainwaveAI;\nvar _c;\n$RefreshReg$(_c, \"FloatingBrainwaveAI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "TbRobot", "TbMinus", "TbMaximize", "TbX", "chatWithChatGPT", "uploadImg", "useLanguage", "useSelector", "useLocation", "Content<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FloatingBrainwaveAI", "_s", "isKiswahili", "user", "state", "location", "isOpen", "setIsOpen", "isMaximized", "setIsMaximized", "messages", "setMessages", "input", "setInput", "isLoading", "setIsLoading", "selectedImage", "setSelectedImage", "imagePreview", "setImagePreview", "messagesEndRef", "fileInputRef", "inputRef", "isMobile", "window", "innerWidth", "savedMessages", "localStorage", "getItem", "parsedMessages", "JSON", "parse", "error", "console", "length", "setItem", "stringify", "current", "scrollIntoView", "behavior", "handleImageSelect", "event", "file", "target", "files", "type", "startsWith", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "removeImage", "value", "initialMessage", "useMemo", "content", "role", "sendMessage", "trim", "userMessage", "imageFile", "imageUrl", "log", "name", "size", "formData", "FormData", "append", "uploadResponse", "success", "_uploadResponse$data", "data", "url", "Error", "message", "errorMessage", "prev", "newUserMessage", "text", "image_url", "systemPrompt", "chatPayload", "response", "handleKeyDown", "key", "shift<PERSON>ey", "preventDefault", "clearChatHistory", "removeItem", "pathname", "includes", "children", "onClick", "style", "position", "bottom", "right", "width", "height", "background", "borderRadius", "display", "alignItems", "justifyContent", "cursor", "boxShadow", "zIndex", "transition", "border", "<PERSON><PERSON>ilter", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "left", "top", "flexDirection", "overflow", "padding", "gap", "margin", "fontWeight", "opacity", "title", "flex", "overflowY", "className", "alignSelf", "max<PERSON><PERSON><PERSON>", "lineHeight", "map", "index", "Array", "isArray", "item", "itemIndex", "marginTop", "src", "alt", "maxHeight", "objectFit", "open", "i", "animation", "animationDelay", "ref", "marginBottom", "_fileInputRef$current", "click", "onChange", "onKeyDown", "placeholder", "rows", "outline", "fontFamily", "disabled", "accept", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/FloatingBrainwaveAI.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { Tb<PERSON><PERSON><PERSON>, Tb<PERSON>inus, TbMaximize, TbX } from 'react-icons/tb';\nimport { chatWithChatGPT, uploadImg } from '../apicalls/chat';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { useSelector } from 'react-redux';\nimport { useLocation } from 'react-router-dom';\nimport ContentRenderer from './ContentRenderer';\n\nconst FloatingBrainwaveAI = () => {\n  const { isKiswahili } = useLanguage();\n  const { user } = useSelector(state => state.user);\n  const location = useLocation();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMaximized, setIsMaximized] = useState(false);\n  const [messages, setMessages] = useState([]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const inputRef = useRef(null);\n  const isMobile = window.innerWidth <= 768;\n\n  // Load saved chat history on component mount\n  useEffect(() => {\n    const savedMessages = localStorage.getItem('brainwave_chat_history');\n    if (savedMessages) {\n      try {\n        const parsedMessages = JSON.parse(savedMessages);\n        setMessages(parsedMessages);\n      } catch (error) {\n        console.error('Error loading chat history:', error);\n      }\n    }\n  }, []);\n\n  // Save chat history whenever messages change\n  useEffect(() => {\n    if (messages.length > 0) {\n      localStorage.setItem('brainwave_chat_history', JSON.stringify(messages));\n    }\n  }, [messages]);\n\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  const handleImageSelect = (event) => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = (e) => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n\n\n  // Memoize initial message to prevent unnecessary re-renders\n  const initialMessage = React.useMemo(() => {\n    const content = isKiswahili\n      ? `Hujambo! Mimi ni Brainwave AI, msaidizi wako wa masomo. Niko hapa kukusaidia na maswali yoyote ya masomo. Je, una swali lolote?`\n      : `Hello! I'm Brainwave AI, your educational assistant. I'm here to help you with any study questions you might have. What would you like to learn about today?`;\n\n    return { role: \"assistant\", content };\n  }, [isKiswahili]);\n\n  const sendMessage = async () => {\n    if (!input.trim() && !selectedImage) return;\n\n    const userMessage = input.trim();\n    const imageFile = selectedImage;\n\n    setInput('');\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n\n    let imageUrl = null;\n    if (imageFile) {\n      try {\n        setIsLoading(true);\n        console.log('Uploading image:', imageFile.name, imageFile.size);\n\n        // Create FormData for image upload\n        const formData = new FormData();\n        formData.append('image', imageFile);\n\n        console.log('Sending image upload request...');\n        const uploadResponse = await uploadImg(formData);\n        console.log('Upload response:', uploadResponse);\n\n        if (uploadResponse.success) {\n          imageUrl = uploadResponse.data?.url || uploadResponse.url;\n          console.log('Image uploaded successfully:', imageUrl);\n        } else {\n          throw new Error(uploadResponse.message || 'Image upload failed');\n        }\n      } catch (error) {\n        console.error('Error uploading image:', error);\n        const errorMessage = isKiswahili\n          ? \"Kuna tatizo la kupakia picha. Tafadhali jaribu tena.\"\n          : \"There was an error uploading the image. Please try again.\";\n        setMessages(prev => [...prev, { role: \"assistant\", content: errorMessage }]);\n        setIsLoading(false);\n        return;\n      }\n    }\n\n    const newUserMessage = imageUrl\n      ? {\n          role: \"user\",\n          content: [\n            { type: \"text\", text: userMessage || \"Please analyze this image\" },\n            { type: \"image_url\", image_url: { url: imageUrl } }\n          ]\n        }\n      : { role: \"user\", content: userMessage };\n\n    setMessages(prev => [...prev, newUserMessage]);\n\n    // Enhanced system prompt with formatting instructions\n    const systemPrompt = isKiswahili\n      ? 'Jibu kwa lugha ya Kiswahili tu. Wewe ni msaidizi wa masomo wa Tanzania. Tumia lugha rahisi na ya kielimu. Ikiwa ni swali la picha, soma picha kwa makini na ueleze hatua kwa hatua. Tumia:\\n- Nukta za bullet (-) kwa orodha\\n- Nambari (1., 2., 3.) kwa hatua\\n- **Herufi nzito** kwa mambo muhimu\\n- ## kwa vichwa vikuu'\n      : 'You are an educational assistant for Tanzanian students. Be helpful and provide clear, step-by-step explanations. If this is an image question, carefully analyze the image content and provide detailed solutions. Format your responses using:\\n- Bullet points (-) for lists\\n- Numbers (1., 2., 3.) for steps\\n- **Bold text** for important points\\n- ## for main headings\\n- Organize information clearly with proper spacing';\n\n    const chatPayload = {\n      messages: [\n        { role: \"system\", content: systemPrompt },\n        ...messages,\n        newUserMessage\n      ]\n    };\n\n    try {\n      setIsLoading(true);\n      const response = await chatWithChatGPT(chatPayload);\n\n      if (response.success) {\n        setMessages(prev => [...prev, { role: \"assistant\", content: response.data }]);\n      } else {\n        throw new Error(response.message || 'Failed to get response');\n      }\n    } catch (error) {\n      const errorMessage = isKiswahili\n        ? \"Samahani, kuna tatizo la mtandao. Tafadhali jaribu tena.\"\n        : \"Sorry, there was a network error. Please try again.\";\n      setMessages(prev => [...prev, { role: \"assistant\", content: errorMessage }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyDown = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  const clearChatHistory = () => {\n    setMessages([]);\n    localStorage.removeItem('brainwave_chat_history');\n  };\n\n  // Hide on subscription page\n  if (!user || location.pathname.includes('/subscription')) return null;\n\n  return (\n    <>\n      {/* Floating Button */}\n      {!isOpen && (\n        <div\n          onClick={() => setIsOpen(true)}\n          style={{\n            position: 'fixed',\n            bottom: isMobile ? '20px' : '30px',\n            right: isMobile ? '20px' : '30px',\n            width: isMobile ? '50px' : '60px',\n            height: isMobile ? '50px' : '60px',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\n            zIndex: 1000,\n            transition: 'all 0.3s ease',\n            border: '3px solid rgba(255, 255, 255, 0.2)',\n            backdropFilter: 'blur(10px)'\n          }}\n        >\n          <TbRobot style={{ color: 'white', fontSize: isMobile ? '24px' : '28px' }} />\n        </div>\n      )}\n\n      {/* Chat Window */}\n      {isOpen && (\n        <>\n          <div style={{\n            position: 'fixed',\n            bottom: isMaximized ? '20px' : (isMobile ? '20px' : '30px'),\n            right: isMaximized ? '20px' : (isMobile ? '20px' : '30px'),\n            left: isMaximized ? '20px' : 'auto',\n            top: isMaximized ? '20px' : 'auto',\n            width: isMaximized ? 'calc(100vw - 40px)' : (isMobile ? '320px' : '380px'),\n            height: isMaximized ? 'calc(100vh - 40px)' : (isMobile ? '500px' : '600px'),\n            background: 'rgba(255, 255, 255, 0.95)',\n            borderRadius: isMaximized ? '16px' : '20px',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\n            zIndex: 1000,\n            display: 'flex',\n            flexDirection: 'column',\n            border: '1px solid rgba(255, 255, 255, 0.2)',\n            backdropFilter: 'blur(20px)',\n            overflow: 'hidden',\n            transition: 'all 0.3s ease'\n          }}>\n            {/* Header */}\n            <div style={{\n              padding: isMobile ? '16px 20px' : '20px 24px',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n                <TbRobot style={{ fontSize: isMobile ? '20px' : '24px' }} />\n                <div>\n                  <h3 style={{ margin: 0, fontSize: isMobile ? '16px' : '18px', fontWeight: '600' }}>\n                    Brainwave AI\n                  </h3>\n                  <p style={{ margin: 0, fontSize: isMobile ? '11px' : '12px', opacity: 0.9 }}>\n                    {isKiswahili ? 'Msaidizi wako wa masomo' : 'Your study assistant'}\n                  </p>\n                </div>\n              </div>\n              <div style={{ display: 'flex', gap: '6px' }}>\n                {messages.length > 0 && (\n                  <button\n                    onClick={clearChatHistory}\n                    style={{\n                      background: 'rgba(255, 255, 255, 0.2)',\n                      border: 'none',\n                      borderRadius: isMobile ? '6px' : '8px',\n                      width: isMobile ? '28px' : '32px',\n                      height: isMobile ? '28px' : '32px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease',\n                      backdropFilter: 'blur(10px)'\n                    }}\n                    title={isKiswahili ? 'Futa mazungumzo' : 'Clear chat'}\n                  >\n                    <span style={{ color: 'white', fontSize: isMobile ? '14px' : '16px' }}>🗑️</span>\n                  </button>\n                )}\n                <button\n                  onClick={() => setIsMaximized(!isMaximized)}\n                  style={{\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    border: 'none',\n                    borderRadius: isMobile ? '6px' : '8px',\n                    width: isMobile ? '28px' : '32px',\n                    height: isMobile ? '28px' : '32px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease',\n                    backdropFilter: 'blur(10px)'\n                  }}\n                  title={isMaximized ? (isKiswahili ? 'Punguza' : 'Minimize') : (isKiswahili ? 'Kubwa' : 'Maximize')}\n                >\n                  {isMaximized ? (\n                    <TbMinus style={{ color: 'white', fontSize: isMobile ? '16px' : '18px' }} />\n                  ) : (\n                    <TbMaximize style={{ color: 'white', fontSize: isMobile ? '16px' : '18px' }} />\n                  )}\n                </button>\n                <button\n                  onClick={() => {\n                    setIsOpen(false);\n                    setIsMaximized(false);\n                  }}\n                  style={{\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    border: 'none',\n                    borderRadius: isMobile ? '6px' : '8px',\n                    width: isMobile ? '28px' : '32px',\n                    height: isMobile ? '28px' : '32px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease',\n                    backdropFilter: 'blur(10px)'\n                  }}\n                  title={isKiswahili ? 'Funga' : 'Close'}\n                >\n                  <TbX style={{ color: 'white', fontSize: isMobile ? '16px' : '18px' }} />\n                </button>\n              </div>\n            </div>\n\n            {/* Messages */}\n            <div style={{\n              flex: 1,\n              padding: isMobile ? '16px' : '20px',\n              overflowY: 'auto',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '16px'\n            }} className=\"custom-scrollbar\">\n              {/* Initial Message */}\n              <div style={{\n                background: '#ffffff',\n                padding: isMobile ? '12px 16px' : '16px 20px',\n                borderRadius: '16px',\n                border: '2px solid #e5e7eb',\n                alignSelf: 'flex-start',\n                maxWidth: '85%',\n                color: '#1f2937',\n                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n              }}>\n                <div style={{ color: '#1f2937', fontSize: '14px', lineHeight: '1.6' }}>\n                  {initialMessage.content || 'Hello! I\\'m Brainwave AI, your educational assistant.'}\n                </div>\n              </div>\n\n              {/* Chat Messages */}\n              {messages.map((message, index) => (\n                <div key={index} style={{\n                  alignSelf: message.role === 'user' ? 'flex-end' : 'flex-start',\n                  maxWidth: '85%'\n                }}>\n                  <div style={{\n                    background: message.role === 'user'\n                      ? 'linear-gradient(135deg, #3b82f6, #1d4ed8)'\n                      : '#ffffff',\n                    color: message.role === 'user' ? '#ffffff' : '#1f2937',\n                    padding: isMobile ? '12px 16px' : '16px 20px',\n                    borderRadius: '16px',\n                    border: message.role === 'user' ? 'none' : '2px solid #e5e7eb',\n                    fontSize: isMobile ? '13px' : '14px',\n                    lineHeight: '1.6',\n                    boxShadow: message.role === 'user' ? 'none' : '0 2px 8px rgba(0, 0, 0, 0.1)'\n                  }}>\n                    {Array.isArray(message.content) ? (\n                      message.content.map((item, itemIndex) => (\n                        <div key={itemIndex}>\n                          {item.type === 'text' && (\n                            <div style={{ color: 'inherit', fontSize: 'inherit', lineHeight: 'inherit' }}>\n                              {item.text || 'Message content'}\n                            </div>\n                          )}\n                          {item.type === 'image_url' && (\n                            <div style={{ marginTop: '8px', position: 'relative' }}>\n                              <img \n                                src={item.image_url.url} \n                                alt=\"User upload\" \n                                style={{ \n                                  maxWidth: '100%', \n                                  height: 'auto', \n                                  borderRadius: '12px', \n                                  maxHeight: isMobile ? '180px' : '250px', \n                                  objectFit: 'contain', \n                                  border: '3px solid #e2e8f0', \n                                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n                                  background: '#f8fafc',\n                                  cursor: 'pointer'\n                                }}\n                                onClick={() => {\n                                  window.open(item.image_url.url, '_blank');\n                                }}\n                              />\n                              <div style={{\n                                position: 'absolute',\n                                bottom: '8px',\n                                right: '8px',\n                                background: 'rgba(0, 0, 0, 0.7)',\n                                color: 'white',\n                                padding: '4px 8px',\n                                borderRadius: '6px',\n                                fontSize: '10px',\n                                fontWeight: '500'\n                              }}>\n                                📸 {isKiswahili ? 'Bonyeza kuona kikubwa' : 'Click to enlarge'}\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      ))\n                    ) : (\n                      <div style={{ color: 'inherit', fontSize: 'inherit', lineHeight: 'inherit' }}>\n                        {message.role === 'assistant' ? (\n                          <ContentRenderer text={message.content || 'AI response'} />\n                        ) : (\n                          message.content || 'Message content'\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n\n              {/* Loading indicator */}\n              {isLoading && (\n                <div style={{\n                  alignSelf: 'flex-start',\n                  maxWidth: '85%'\n                }}>\n                  <div style={{\n                    background: '#ffffff',\n                    padding: isMobile ? '12px 16px' : '16px 20px',\n                    borderRadius: '16px',\n                    border: '2px solid #e5e7eb',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n                  }}>\n                    <div style={{ display: 'flex', gap: '4px' }}>\n                      {[1, 2, 3].map(i => (\n                        <div\n                          key={i}\n                          style={{\n                            width: '8px',\n                            height: '8px',\n                            background: '#3b82f6',\n                            borderRadius: '50%',\n                            animation: `bounce 1.4s infinite ease-in-out both`,\n                            animationDelay: `${(i - 1) * 0.16}s`\n                          }}\n                        />\n                      ))}\n                    </div>\n                    <span style={{ fontSize: isMobile ? '12px' : '13px', color: '#6b7280' }}>\n                      {isKiswahili ? 'Inafikiri...' : 'Thinking...'}\n                    </span>\n                  </div>\n                </div>\n              )}\n\n              <div ref={messagesEndRef} />\n            </div>\n\n            {/* Image Preview */}\n            {imagePreview && (\n              <div style={{ marginBottom: '12px', padding: '12px', background: 'linear-gradient(135deg, #f0f9ff, #e0f2fe)', borderRadius: '12px', border: '2px solid #0ea5e9', boxShadow: '0 4px 12px rgba(14, 165, 233, 0.15)' }}>\n                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n                  <div style={{ position: 'relative' }}>\n                    <img src={imagePreview} alt=\"Preview\" style={{ width: isMobile ? '80px' : '100px', height: isMobile ? '80px' : '100px', objectFit: 'cover', borderRadius: '8px', border: '2px solid #0ea5e9', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)' }} />\n                    <button onClick={removeImage} style={{ position: 'absolute', top: '-6px', right: '-6px', width: '24px', height: '24px', background: '#ef4444', color: 'white', borderRadius: '50%', border: '2px solid white', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px', fontWeight: 'bold', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)' }}>×</button>\n                  </div>\n                  <div style={{ flex: 1 }}>\n                    <p style={{ fontSize: '14px', fontWeight: '600', color: '#0c4a6e', margin: '0 0 4px 0', display: 'flex', alignItems: 'center', gap: '6px' }}>\n                      📸 {isKiswahili ? 'Picha Imepakiwa' : 'Image Attached'}\n                    </p>\n                    <p style={{ fontSize: '11px', color: '#0369a1', margin: 0 }}>\n                      {selectedImage?.name || 'image.png'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Input Area */}\n            <div style={{ display: 'flex', gap: isMobile ? '6px' : '8px', background: '#f8fafc', borderRadius: isMobile ? '12px' : '16px', padding: isMobile ? '6px' : '8px', border: '2px solid #e2e8f0' }}>\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                style={{\n                  background: '#3b82f6',\n                  border: 'none',\n                  borderRadius: '6px',\n                  width: '24px',\n                  height: '24px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease',\n                  boxShadow: '0 1px 3px rgba(59, 130, 246, 0.3)'\n                }}\n                title={isKiswahili ? \"Pakia picha\" : \"Upload image\"}\n              >\n                <span style={{ color: 'white', fontSize: '14px', fontWeight: 'bold' }}>+</span>\n              </button>\n\n\n\n              <textarea\n                ref={inputRef}\n                value={input}\n                onChange={(e) => setInput(e.target.value)}\n                onKeyDown={handleKeyDown}\n                placeholder={isKiswahili ? \"Uliza chochote...\" : \"Ask me anything...\"}\n                rows={1}\n                style={{\n                  flex: 1,\n                  border: 'none',\n                  background: 'transparent',\n                  outline: 'none',\n                  fontSize: isMobile ? '12px' : '14px',\n                  color: '#334155',\n                  padding: isMobile ? '10px 12px' : '12px 16px',\n                  fontFamily: 'inherit'\n                }}\n              />\n\n              <button\n                onClick={sendMessage}\n                disabled={!input.trim() && !selectedImage}\n                style={{\n                  background: (input.trim() || selectedImage) ? '#3b82f6' : '#e2e8f0',\n                  border: 'none',\n                  borderRadius: '6px',\n                  width: '24px',\n                  height: '24px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: (input.trim() || selectedImage) ? 'pointer' : 'not-allowed',\n                  transition: 'all 0.2s ease'\n                }}\n              >\n                <span style={{ color: 'white', fontSize: '12px' }}>→</span>\n              </button>\n            </div>\n\n            <input ref={fileInputRef} type=\"file\" accept=\"image/*\" onChange={handleImageSelect} style={{ display: 'none' }} />\n\n            <p style={{ fontSize: isMobile ? '9px' : '11px', color: '#94a3b8', textAlign: 'center', margin: isMobile ? '6px 0 0 0' : '8px 0 0 0' }}>\n              {isMobile\n                ? (isKiswahili ? 'Enter • + Pakia picha' : 'Enter • + Upload image')\n                : (isKiswahili ? 'Enter kusonga • + Pakia picha' : 'Press Enter to send • + Upload image')\n              }\n            </p>\n          </div>\n        </>\n      )}\n\n      <style>{`\n        @keyframes bounce {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n\n        .custom-scrollbar::-webkit-scrollbar {\n          width: 6px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-track {\n          background: transparent;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 3px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n      `}</style>\n    </>\n  );\n};\n\nexport default FloatingBrainwaveAI;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAClE,SAASC,eAAe,EAAEC,SAAS,QAAQ,kBAAkB;AAC7D,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEhD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAY,CAAC,GAAGV,WAAW,CAAC,CAAC;EACrC,MAAM;IAAEW;EAAK,CAAC,GAAGV,WAAW,CAACW,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EACjD,MAAME,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMqC,cAAc,GAAGpC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMqC,YAAY,GAAGrC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMsC,QAAQ,GAAGtC,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMuC,QAAQ,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG;;EAEzC;EACAxC,SAAS,CAAC,MAAM;IACd,MAAMyC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IACpE,IAAIF,aAAa,EAAE;MACjB,IAAI;QACF,MAAMG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACL,aAAa,CAAC;QAChDf,WAAW,CAACkB,cAAc,CAAC;MAC7B,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/C,SAAS,CAAC,MAAM;IACd,IAAIyB,QAAQ,CAACwB,MAAM,GAAG,CAAC,EAAE;MACvBP,YAAY,CAACQ,OAAO,CAAC,wBAAwB,EAAEL,IAAI,CAACM,SAAS,CAAC1B,QAAQ,CAAC,CAAC;IAC1E;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEdzB,SAAS,CAAC,MAAM;IACd,IAAImC,cAAc,CAACiB,OAAO,EAAE;MAC1BjB,cAAc,CAACiB,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAAC7B,QAAQ,CAAC,CAAC;EAEd,MAAM8B,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,IAAIA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1C7B,gBAAgB,CAACyB,IAAI,CAAC;MACtB,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK/B,eAAe,CAAC+B,CAAC,CAACP,MAAM,CAACQ,MAAM,CAAC;MACvDJ,MAAM,CAACK,aAAa,CAACV,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBpC,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIE,YAAY,CAACgB,OAAO,EAAE;MACxBhB,YAAY,CAACgB,OAAO,CAACiB,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAID;EACA,MAAMC,cAAc,GAAGzE,KAAK,CAAC0E,OAAO,CAAC,MAAM;IACzC,MAAMC,OAAO,GAAGvD,WAAW,GACtB,iIAAgI,GAChI,8JAA6J;IAElK,OAAO;MAAEwD,IAAI,EAAE,WAAW;MAAED;IAAQ,CAAC;EACvC,CAAC,EAAE,CAACvD,WAAW,CAAC,CAAC;EAEjB,MAAMyD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC/C,KAAK,CAACgD,IAAI,CAAC,CAAC,IAAI,CAAC5C,aAAa,EAAE;IAErC,MAAM6C,WAAW,GAAGjD,KAAK,CAACgD,IAAI,CAAC,CAAC;IAChC,MAAME,SAAS,GAAG9C,aAAa;IAE/BH,QAAQ,CAAC,EAAE,CAAC;IACZI,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIE,YAAY,CAACgB,OAAO,EAAE;MACxBhB,YAAY,CAACgB,OAAO,CAACiB,KAAK,GAAG,EAAE;IACjC;IAEA,IAAIS,QAAQ,GAAG,IAAI;IACnB,IAAID,SAAS,EAAE;MACb,IAAI;QACF/C,YAAY,CAAC,IAAI,CAAC;QAClBkB,OAAO,CAAC+B,GAAG,CAAC,kBAAkB,EAAEF,SAAS,CAACG,IAAI,EAAEH,SAAS,CAACI,IAAI,CAAC;;QAE/D;QACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEP,SAAS,CAAC;QAEnC7B,OAAO,CAAC+B,GAAG,CAAC,iCAAiC,CAAC;QAC9C,MAAMM,cAAc,GAAG,MAAM/E,SAAS,CAAC4E,QAAQ,CAAC;QAChDlC,OAAO,CAAC+B,GAAG,CAAC,kBAAkB,EAAEM,cAAc,CAAC;QAE/C,IAAIA,cAAc,CAACC,OAAO,EAAE;UAAA,IAAAC,oBAAA;UAC1BT,QAAQ,GAAG,EAAAS,oBAAA,GAAAF,cAAc,CAACG,IAAI,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBE,GAAG,KAAIJ,cAAc,CAACI,GAAG;UACzDzC,OAAO,CAAC+B,GAAG,CAAC,8BAA8B,EAAED,QAAQ,CAAC;QACvD,CAAC,MAAM;UACL,MAAM,IAAIY,KAAK,CAACL,cAAc,CAACM,OAAO,IAAI,qBAAqB,CAAC;QAClE;MACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAM6C,YAAY,GAAG3E,WAAW,GAC5B,sDAAsD,GACtD,2DAA2D;QAC/DS,WAAW,CAACmE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAEpB,IAAI,EAAE,WAAW;UAAED,OAAO,EAAEoB;QAAa,CAAC,CAAC,CAAC;QAC5E9D,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;IACF;IAEA,MAAMgE,cAAc,GAAGhB,QAAQ,GAC3B;MACEL,IAAI,EAAE,MAAM;MACZD,OAAO,EAAE,CACP;QAAEZ,IAAI,EAAE,MAAM;QAAEmC,IAAI,EAAEnB,WAAW,IAAI;MAA4B,CAAC,EAClE;QAAEhB,IAAI,EAAE,WAAW;QAAEoC,SAAS,EAAE;UAAEP,GAAG,EAAEX;QAAS;MAAE,CAAC;IAEvD,CAAC,GACD;MAAEL,IAAI,EAAE,MAAM;MAAED,OAAO,EAAEI;IAAY,CAAC;IAE1ClD,WAAW,CAACmE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEC,cAAc,CAAC,CAAC;;IAE9C;IACA,MAAMG,YAAY,GAAGhF,WAAW,GAC5B,4TAA4T,GAC5T,qaAAqa;IAEza,MAAMiF,WAAW,GAAG;MAClBzE,QAAQ,EAAE,CACR;QAAEgD,IAAI,EAAE,QAAQ;QAAED,OAAO,EAAEyB;MAAa,CAAC,EACzC,GAAGxE,QAAQ,EACXqE,cAAc;IAElB,CAAC;IAED,IAAI;MACFhE,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMqE,QAAQ,GAAG,MAAM9F,eAAe,CAAC6F,WAAW,CAAC;MAEnD,IAAIC,QAAQ,CAACb,OAAO,EAAE;QACpB5D,WAAW,CAACmE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAEpB,IAAI,EAAE,WAAW;UAAED,OAAO,EAAE2B,QAAQ,CAACX;QAAK,CAAC,CAAC,CAAC;MAC/E,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAACS,QAAQ,CAACR,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACd,MAAM6C,YAAY,GAAG3E,WAAW,GAC5B,0DAA0D,GAC1D,qDAAqD;MACzDS,WAAW,CAACmE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAEpB,IAAI,EAAE,WAAW;QAAED,OAAO,EAAEoB;MAAa,CAAC,CAAC,CAAC;IAC9E,CAAC,SAAS;MACR9D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMsE,aAAa,GAAInC,CAAC,IAAK;IAC3B,IAAIA,CAAC,CAACoC,GAAG,KAAK,OAAO,IAAI,CAACpC,CAAC,CAACqC,QAAQ,EAAE;MACpCrC,CAAC,CAACsC,cAAc,CAAC,CAAC;MAClB7B,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAM8B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9E,WAAW,CAAC,EAAE,CAAC;IACfgB,YAAY,CAAC+D,UAAU,CAAC,wBAAwB,CAAC;EACnD,CAAC;;EAED;EACA,IAAI,CAACvF,IAAI,IAAIE,QAAQ,CAACsF,QAAQ,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE,OAAO,IAAI;EAErE,oBACE/F,OAAA,CAAAE,SAAA;IAAA8F,QAAA,GAEG,CAACvF,MAAM,iBACNT,OAAA;MACEiG,OAAO,EAAEA,CAAA,KAAMvF,SAAS,CAAC,IAAI,CAAE;MAC/BwF,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE1E,QAAQ,GAAG,MAAM,GAAG,MAAM;QAClC2E,KAAK,EAAE3E,QAAQ,GAAG,MAAM,GAAG,MAAM;QACjC4E,KAAK,EAAE5E,QAAQ,GAAG,MAAM,GAAG,MAAM;QACjC6E,MAAM,EAAE7E,QAAQ,GAAG,MAAM,GAAG,MAAM;QAClC8E,UAAU,EAAE,mDAAmD;QAC/DC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,qCAAqC;QAChDC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,eAAe;QAC3BC,MAAM,EAAE,oCAAoC;QAC5CC,cAAc,EAAE;MAClB,CAAE;MAAAlB,QAAA,eAEFhG,OAAA,CAACX,OAAO;QAAC6G,KAAK,EAAE;UAAEiB,KAAK,EAAE,OAAO;UAAEC,QAAQ,EAAE1F,QAAQ,GAAG,MAAM,GAAG;QAAO;MAAE;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CACN,EAGA/G,MAAM,iBACLT,OAAA,CAAAE,SAAA;MAAA8F,QAAA,eACEhG,OAAA;QAAKkG,KAAK,EAAE;UACVC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAEzF,WAAW,GAAG,MAAM,GAAIe,QAAQ,GAAG,MAAM,GAAG,MAAO;UAC3D2E,KAAK,EAAE1F,WAAW,GAAG,MAAM,GAAIe,QAAQ,GAAG,MAAM,GAAG,MAAO;UAC1D+F,IAAI,EAAE9G,WAAW,GAAG,MAAM,GAAG,MAAM;UACnC+G,GAAG,EAAE/G,WAAW,GAAG,MAAM,GAAG,MAAM;UAClC2F,KAAK,EAAE3F,WAAW,GAAG,oBAAoB,GAAIe,QAAQ,GAAG,OAAO,GAAG,OAAQ;UAC1E6E,MAAM,EAAE5F,WAAW,GAAG,oBAAoB,GAAIe,QAAQ,GAAG,OAAO,GAAG,OAAQ;UAC3E8E,UAAU,EAAE,2BAA2B;UACvCC,YAAY,EAAE9F,WAAW,GAAG,MAAM,GAAG,MAAM;UAC3CmG,SAAS,EAAE,iCAAiC;UAC5CC,MAAM,EAAE,IAAI;UACZL,OAAO,EAAE,MAAM;UACfiB,aAAa,EAAE,QAAQ;UACvBV,MAAM,EAAE,oCAAoC;UAC5CC,cAAc,EAAE,YAAY;UAC5BU,QAAQ,EAAE,QAAQ;UAClBZ,UAAU,EAAE;QACd,CAAE;QAAAhB,QAAA,gBAEAhG,OAAA;UAAKkG,KAAK,EAAE;YACV2B,OAAO,EAAEnG,QAAQ,GAAG,WAAW,GAAG,WAAW;YAC7C8E,UAAU,EAAE,mDAAmD;YAC/DW,KAAK,EAAE,OAAO;YACdT,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAZ,QAAA,gBACAhG,OAAA;YAAKkG,KAAK,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEmB,GAAG,EAAE;YAAO,CAAE;YAAA9B,QAAA,gBACjEhG,OAAA,CAACX,OAAO;cAAC6G,KAAK,EAAE;gBAAEkB,QAAQ,EAAE1F,QAAQ,GAAG,MAAM,GAAG;cAAO;YAAE;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DxH,OAAA;cAAAgG,QAAA,gBACEhG,OAAA;gBAAIkG,KAAK,EAAE;kBAAE6B,MAAM,EAAE,CAAC;kBAAEX,QAAQ,EAAE1F,QAAQ,GAAG,MAAM,GAAG,MAAM;kBAAEsG,UAAU,EAAE;gBAAM,CAAE;gBAAAhC,QAAA,EAAC;cAEnF;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxH,OAAA;gBAAGkG,KAAK,EAAE;kBAAE6B,MAAM,EAAE,CAAC;kBAAEX,QAAQ,EAAE1F,QAAQ,GAAG,MAAM,GAAG,MAAM;kBAAEuG,OAAO,EAAE;gBAAI,CAAE;gBAAAjC,QAAA,EACzE3F,WAAW,GAAG,yBAAyB,GAAG;cAAsB;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxH,OAAA;YAAKkG,KAAK,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEoB,GAAG,EAAE;YAAM,CAAE;YAAA9B,QAAA,GACzCnF,QAAQ,CAACwB,MAAM,GAAG,CAAC,iBAClBrC,OAAA;cACEiG,OAAO,EAAEL,gBAAiB;cAC1BM,KAAK,EAAE;gBACLM,UAAU,EAAE,0BAA0B;gBACtCS,MAAM,EAAE,MAAM;gBACdR,YAAY,EAAE/E,QAAQ,GAAG,KAAK,GAAG,KAAK;gBACtC4E,KAAK,EAAE5E,QAAQ,GAAG,MAAM,GAAG,MAAM;gBACjC6E,MAAM,EAAE7E,QAAQ,GAAG,MAAM,GAAG,MAAM;gBAClCgF,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBC,MAAM,EAAE,SAAS;gBACjBG,UAAU,EAAE,eAAe;gBAC3BE,cAAc,EAAE;cAClB,CAAE;cACFgB,KAAK,EAAE7H,WAAW,GAAG,iBAAiB,GAAG,YAAa;cAAA2F,QAAA,eAEtDhG,OAAA;gBAAMkG,KAAK,EAAE;kBAAEiB,KAAK,EAAE,OAAO;kBAAEC,QAAQ,EAAE1F,QAAQ,GAAG,MAAM,GAAG;gBAAO,CAAE;gBAAAsE,QAAA,EAAC;cAAG;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CACT,eACDxH,OAAA;cACEiG,OAAO,EAAEA,CAAA,KAAMrF,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5CuF,KAAK,EAAE;gBACLM,UAAU,EAAE,0BAA0B;gBACtCS,MAAM,EAAE,MAAM;gBACdR,YAAY,EAAE/E,QAAQ,GAAG,KAAK,GAAG,KAAK;gBACtC4E,KAAK,EAAE5E,QAAQ,GAAG,MAAM,GAAG,MAAM;gBACjC6E,MAAM,EAAE7E,QAAQ,GAAG,MAAM,GAAG,MAAM;gBAClCgF,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBC,MAAM,EAAE,SAAS;gBACjBG,UAAU,EAAE,eAAe;gBAC3BE,cAAc,EAAE;cAClB,CAAE;cACFgB,KAAK,EAAEvH,WAAW,GAAIN,WAAW,GAAG,SAAS,GAAG,UAAU,GAAKA,WAAW,GAAG,OAAO,GAAG,UAAY;cAAA2F,QAAA,EAElGrF,WAAW,gBACVX,OAAA,CAACV,OAAO;gBAAC4G,KAAK,EAAE;kBAAEiB,KAAK,EAAE,OAAO;kBAAEC,QAAQ,EAAE1F,QAAQ,GAAG,MAAM,GAAG;gBAAO;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE5ExH,OAAA,CAACT,UAAU;gBAAC2G,KAAK,EAAE;kBAAEiB,KAAK,EAAE,OAAO;kBAAEC,QAAQ,EAAE1F,QAAQ,GAAG,MAAM,GAAG;gBAAO;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC/E;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACTxH,OAAA;cACEiG,OAAO,EAAEA,CAAA,KAAM;gBACbvF,SAAS,CAAC,KAAK,CAAC;gBAChBE,cAAc,CAAC,KAAK,CAAC;cACvB,CAAE;cACFsF,KAAK,EAAE;gBACLM,UAAU,EAAE,0BAA0B;gBACtCS,MAAM,EAAE,MAAM;gBACdR,YAAY,EAAE/E,QAAQ,GAAG,KAAK,GAAG,KAAK;gBACtC4E,KAAK,EAAE5E,QAAQ,GAAG,MAAM,GAAG,MAAM;gBACjC6E,MAAM,EAAE7E,QAAQ,GAAG,MAAM,GAAG,MAAM;gBAClCgF,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBC,MAAM,EAAE,SAAS;gBACjBG,UAAU,EAAE,eAAe;gBAC3BE,cAAc,EAAE;cAClB,CAAE;cACFgB,KAAK,EAAE7H,WAAW,GAAG,OAAO,GAAG,OAAQ;cAAA2F,QAAA,eAEvChG,OAAA,CAACR,GAAG;gBAAC0G,KAAK,EAAE;kBAAEiB,KAAK,EAAE,OAAO;kBAAEC,QAAQ,EAAE1F,QAAQ,GAAG,MAAM,GAAG;gBAAO;cAAE;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxH,OAAA;UAAKkG,KAAK,EAAE;YACViC,IAAI,EAAE,CAAC;YACPN,OAAO,EAAEnG,QAAQ,GAAG,MAAM,GAAG,MAAM;YACnC0G,SAAS,EAAE,MAAM;YACjB1B,OAAO,EAAE,MAAM;YACfiB,aAAa,EAAE,QAAQ;YACvBG,GAAG,EAAE;UACP,CAAE;UAACO,SAAS,EAAC,kBAAkB;UAAArC,QAAA,gBAE7BhG,OAAA;YAAKkG,KAAK,EAAE;cACVM,UAAU,EAAE,SAAS;cACrBqB,OAAO,EAAEnG,QAAQ,GAAG,WAAW,GAAG,WAAW;cAC7C+E,YAAY,EAAE,MAAM;cACpBQ,MAAM,EAAE,mBAAmB;cAC3BqB,SAAS,EAAE,YAAY;cACvBC,QAAQ,EAAE,KAAK;cACfpB,KAAK,EAAE,SAAS;cAChBL,SAAS,EAAE;YACb,CAAE;YAAAd,QAAA,eACAhG,OAAA;cAAKkG,KAAK,EAAE;gBAAEiB,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE,MAAM;gBAAEoB,UAAU,EAAE;cAAM,CAAE;cAAAxC,QAAA,EACnEtC,cAAc,CAACE,OAAO,IAAI;YAAuD;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL3G,QAAQ,CAAC4H,GAAG,CAAC,CAAC1D,OAAO,EAAE2D,KAAK,kBAC3B1I,OAAA;YAAiBkG,KAAK,EAAE;cACtBoC,SAAS,EAAEvD,OAAO,CAAClB,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,YAAY;cAC9D0E,QAAQ,EAAE;YACZ,CAAE;YAAAvC,QAAA,eACAhG,OAAA;cAAKkG,KAAK,EAAE;gBACVM,UAAU,EAAEzB,OAAO,CAAClB,IAAI,KAAK,MAAM,GAC/B,2CAA2C,GAC3C,SAAS;gBACbsD,KAAK,EAAEpC,OAAO,CAAClB,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;gBACtDgE,OAAO,EAAEnG,QAAQ,GAAG,WAAW,GAAG,WAAW;gBAC7C+E,YAAY,EAAE,MAAM;gBACpBQ,MAAM,EAAElC,OAAO,CAAClB,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,mBAAmB;gBAC9DuD,QAAQ,EAAE1F,QAAQ,GAAG,MAAM,GAAG,MAAM;gBACpC8G,UAAU,EAAE,KAAK;gBACjB1B,SAAS,EAAE/B,OAAO,CAAClB,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG;cAChD,CAAE;cAAAmC,QAAA,EACC2C,KAAK,CAACC,OAAO,CAAC7D,OAAO,CAACnB,OAAO,CAAC,GAC7BmB,OAAO,CAACnB,OAAO,CAAC6E,GAAG,CAAC,CAACI,IAAI,EAAEC,SAAS,kBAClC9I,OAAA;gBAAAgG,QAAA,GACG6C,IAAI,CAAC7F,IAAI,KAAK,MAAM,iBACnBhD,OAAA;kBAAKkG,KAAK,EAAE;oBAAEiB,KAAK,EAAE,SAAS;oBAAEC,QAAQ,EAAE,SAAS;oBAAEoB,UAAU,EAAE;kBAAU,CAAE;kBAAAxC,QAAA,EAC1E6C,IAAI,CAAC1D,IAAI,IAAI;gBAAiB;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CACN,EACAqB,IAAI,CAAC7F,IAAI,KAAK,WAAW,iBACxBhD,OAAA;kBAAKkG,KAAK,EAAE;oBAAE6C,SAAS,EAAE,KAAK;oBAAE5C,QAAQ,EAAE;kBAAW,CAAE;kBAAAH,QAAA,gBACrDhG,OAAA;oBACEgJ,GAAG,EAAEH,IAAI,CAACzD,SAAS,CAACP,GAAI;oBACxBoE,GAAG,EAAC,aAAa;oBACjB/C,KAAK,EAAE;sBACLqC,QAAQ,EAAE,MAAM;sBAChBhC,MAAM,EAAE,MAAM;sBACdE,YAAY,EAAE,MAAM;sBACpByC,SAAS,EAAExH,QAAQ,GAAG,OAAO,GAAG,OAAO;sBACvCyH,SAAS,EAAE,SAAS;sBACpBlC,MAAM,EAAE,mBAAmB;sBAC3BH,SAAS,EAAE,gCAAgC;sBAC3CN,UAAU,EAAE,SAAS;sBACrBK,MAAM,EAAE;oBACV,CAAE;oBACFZ,OAAO,EAAEA,CAAA,KAAM;sBACbtE,MAAM,CAACyH,IAAI,CAACP,IAAI,CAACzD,SAAS,CAACP,GAAG,EAAE,QAAQ,CAAC;oBAC3C;kBAAE;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFxH,OAAA;oBAAKkG,KAAK,EAAE;sBACVC,QAAQ,EAAE,UAAU;sBACpBC,MAAM,EAAE,KAAK;sBACbC,KAAK,EAAE,KAAK;sBACZG,UAAU,EAAE,oBAAoB;sBAChCW,KAAK,EAAE,OAAO;sBACdU,OAAO,EAAE,SAAS;sBAClBpB,YAAY,EAAE,KAAK;sBACnBW,QAAQ,EAAE,MAAM;sBAChBY,UAAU,EAAE;oBACd,CAAE;oBAAAhC,QAAA,GAAC,eACE,EAAC3F,WAAW,GAAG,uBAAuB,GAAG,kBAAkB;kBAAA;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA,GAxCOsB,SAAS;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyCd,CACN,CAAC,gBAEFxH,OAAA;gBAAKkG,KAAK,EAAE;kBAAEiB,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,SAAS;kBAAEoB,UAAU,EAAE;gBAAU,CAAE;gBAAAxC,QAAA,EAC1EjB,OAAO,CAAClB,IAAI,KAAK,WAAW,gBAC3B7D,OAAA,CAACF,eAAe;kBAACqF,IAAI,EAAEJ,OAAO,CAACnB,OAAO,IAAI;gBAAc;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAE3DzC,OAAO,CAACnB,OAAO,IAAI;cACpB;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GAtEEkB,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuEV,CACN,CAAC,EAGDvG,SAAS,iBACRjB,OAAA;YAAKkG,KAAK,EAAE;cACVoC,SAAS,EAAE,YAAY;cACvBC,QAAQ,EAAE;YACZ,CAAE;YAAAvC,QAAA,eACAhG,OAAA;cAAKkG,KAAK,EAAE;gBACVM,UAAU,EAAE,SAAS;gBACrBqB,OAAO,EAAEnG,QAAQ,GAAG,WAAW,GAAG,WAAW;gBAC7C+E,YAAY,EAAE,MAAM;gBACpBQ,MAAM,EAAE,mBAAmB;gBAC3BP,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBmB,GAAG,EAAE,KAAK;gBACVhB,SAAS,EAAE;cACb,CAAE;cAAAd,QAAA,gBACAhG,OAAA;gBAAKkG,KAAK,EAAE;kBAAEQ,OAAO,EAAE,MAAM;kBAAEoB,GAAG,EAAE;gBAAM,CAAE;gBAAA9B,QAAA,EACzC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACyC,GAAG,CAACY,CAAC,iBACdrJ,OAAA;kBAEEkG,KAAK,EAAE;oBACLI,KAAK,EAAE,KAAK;oBACZC,MAAM,EAAE,KAAK;oBACbC,UAAU,EAAE,SAAS;oBACrBC,YAAY,EAAE,KAAK;oBACnB6C,SAAS,EAAG,uCAAsC;oBAClDC,cAAc,EAAG,GAAE,CAACF,CAAC,GAAG,CAAC,IAAI,IAAK;kBACpC;gBAAE,GARGA,CAAC;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASP,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxH,OAAA;gBAAMkG,KAAK,EAAE;kBAAEkB,QAAQ,EAAE1F,QAAQ,GAAG,MAAM,GAAG,MAAM;kBAAEyF,KAAK,EAAE;gBAAU,CAAE;gBAAAnB,QAAA,EACrE3F,WAAW,GAAG,cAAc,GAAG;cAAa;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDxH,OAAA;YAAKwJ,GAAG,EAAEjI;UAAe;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,EAGLnG,YAAY,iBACXrB,OAAA;UAAKkG,KAAK,EAAE;YAAEuD,YAAY,EAAE,MAAM;YAAE5B,OAAO,EAAE,MAAM;YAAErB,UAAU,EAAE,2CAA2C;YAAEC,YAAY,EAAE,MAAM;YAAEQ,MAAM,EAAE,mBAAmB;YAAEH,SAAS,EAAE;UAAsC,CAAE;UAAAd,QAAA,eAClNhG,OAAA;YAAKkG,KAAK,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEmB,GAAG,EAAE;YAAO,CAAE;YAAA9B,QAAA,gBACjEhG,OAAA;cAAKkG,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAAH,QAAA,gBACnChG,OAAA;gBAAKgJ,GAAG,EAAE3H,YAAa;gBAAC4H,GAAG,EAAC,SAAS;gBAAC/C,KAAK,EAAE;kBAAEI,KAAK,EAAE5E,QAAQ,GAAG,MAAM,GAAG,OAAO;kBAAE6E,MAAM,EAAE7E,QAAQ,GAAG,MAAM,GAAG,OAAO;kBAAEyH,SAAS,EAAE,OAAO;kBAAE1C,YAAY,EAAE,KAAK;kBAAEQ,MAAM,EAAE,mBAAmB;kBAAEH,SAAS,EAAE;gBAA+B;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7OxH,OAAA;gBAAQiG,OAAO,EAAEzC,WAAY;gBAAC0C,KAAK,EAAE;kBAAEC,QAAQ,EAAE,UAAU;kBAAEuB,GAAG,EAAE,MAAM;kBAAErB,KAAK,EAAE,MAAM;kBAAEC,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE,MAAM;kBAAEC,UAAU,EAAE,SAAS;kBAAEW,KAAK,EAAE,OAAO;kBAAEV,YAAY,EAAE,KAAK;kBAAEQ,MAAM,EAAE,iBAAiB;kBAAEJ,MAAM,EAAE,SAAS;kBAAEH,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,cAAc,EAAE,QAAQ;kBAAEQ,QAAQ,EAAE,MAAM;kBAAEY,UAAU,EAAE,MAAM;kBAAElB,SAAS,EAAE;gBAA+B,CAAE;gBAAAd,QAAA,EAAC;cAAC;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7X,CAAC,eACNxH,OAAA;cAAKkG,KAAK,EAAE;gBAAEiC,IAAI,EAAE;cAAE,CAAE;cAAAnC,QAAA,gBACtBhG,OAAA;gBAAGkG,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAEY,UAAU,EAAE,KAAK;kBAAEb,KAAK,EAAE,SAAS;kBAAEY,MAAM,EAAE,WAAW;kBAAErB,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEmB,GAAG,EAAE;gBAAM,CAAE;gBAAA9B,QAAA,GAAC,eACxI,EAAC3F,WAAW,GAAG,iBAAiB,GAAG,gBAAgB;cAAA;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACJxH,OAAA;gBAAGkG,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAED,KAAK,EAAE,SAAS;kBAAEY,MAAM,EAAE;gBAAE,CAAE;gBAAA/B,QAAA,EACzD,CAAA7E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiD,IAAI,KAAI;cAAW;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDxH,OAAA;UAAKkG,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEoB,GAAG,EAAEpG,QAAQ,GAAG,KAAK,GAAG,KAAK;YAAE8E,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAE/E,QAAQ,GAAG,MAAM,GAAG,MAAM;YAAEmG,OAAO,EAAEnG,QAAQ,GAAG,KAAK,GAAG,KAAK;YAAEuF,MAAM,EAAE;UAAoB,CAAE;UAAAjB,QAAA,gBAC9LhG,OAAA;YACEiG,OAAO,EAAEA,CAAA;cAAA,IAAAyD,qBAAA;cAAA,QAAAA,qBAAA,GAAMlI,YAAY,CAACgB,OAAO,cAAAkH,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CzD,KAAK,EAAE;cACLM,UAAU,EAAE,SAAS;cACrBS,MAAM,EAAE,MAAM;cACdR,YAAY,EAAE,KAAK;cACnBH,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAE,SAAS;cACjBG,UAAU,EAAE,eAAe;cAC3BF,SAAS,EAAE;YACb,CAAE;YACFoB,KAAK,EAAE7H,WAAW,GAAG,aAAa,GAAG,cAAe;YAAA2F,QAAA,eAEpDhG,OAAA;cAAMkG,KAAK,EAAE;gBAAEiB,KAAK,EAAE,OAAO;gBAAEC,QAAQ,EAAE,MAAM;gBAAEY,UAAU,EAAE;cAAO,CAAE;cAAAhC,QAAA,EAAC;YAAC;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAITxH,OAAA;YACEwJ,GAAG,EAAE/H,QAAS;YACdgC,KAAK,EAAE1C,KAAM;YACb6I,QAAQ,EAAGvG,CAAC,IAAKrC,QAAQ,CAACqC,CAAC,CAACP,MAAM,CAACW,KAAK,CAAE;YAC1CoG,SAAS,EAAErE,aAAc;YACzBsE,WAAW,EAAEzJ,WAAW,GAAG,mBAAmB,GAAG,oBAAqB;YACtE0J,IAAI,EAAE,CAAE;YACR7D,KAAK,EAAE;cACLiC,IAAI,EAAE,CAAC;cACPlB,MAAM,EAAE,MAAM;cACdT,UAAU,EAAE,aAAa;cACzBwD,OAAO,EAAE,MAAM;cACf5C,QAAQ,EAAE1F,QAAQ,GAAG,MAAM,GAAG,MAAM;cACpCyF,KAAK,EAAE,SAAS;cAChBU,OAAO,EAAEnG,QAAQ,GAAG,WAAW,GAAG,WAAW;cAC7CuI,UAAU,EAAE;YACd;UAAE;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFxH,OAAA;YACEiG,OAAO,EAAEnC,WAAY;YACrBoG,QAAQ,EAAE,CAACnJ,KAAK,CAACgD,IAAI,CAAC,CAAC,IAAI,CAAC5C,aAAc;YAC1C+E,KAAK,EAAE;cACLM,UAAU,EAAGzF,KAAK,CAACgD,IAAI,CAAC,CAAC,IAAI5C,aAAa,GAAI,SAAS,GAAG,SAAS;cACnE8F,MAAM,EAAE,MAAM;cACdR,YAAY,EAAE,KAAK;cACnBH,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAG9F,KAAK,CAACgD,IAAI,CAAC,CAAC,IAAI5C,aAAa,GAAI,SAAS,GAAG,aAAa;cACnE6F,UAAU,EAAE;YACd,CAAE;YAAAhB,QAAA,eAEFhG,OAAA;cAAMkG,KAAK,EAAE;gBAAEiB,KAAK,EAAE,OAAO;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAApB,QAAA,EAAC;YAAC;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENxH,OAAA;UAAOwJ,GAAG,EAAEhI,YAAa;UAACwB,IAAI,EAAC,MAAM;UAACmH,MAAM,EAAC,SAAS;UAACP,QAAQ,EAAEjH,iBAAkB;UAACuD,KAAK,EAAE;YAAEQ,OAAO,EAAE;UAAO;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAElHxH,OAAA;UAAGkG,KAAK,EAAE;YAAEkB,QAAQ,EAAE1F,QAAQ,GAAG,KAAK,GAAG,MAAM;YAAEyF,KAAK,EAAE,SAAS;YAAEiD,SAAS,EAAE,QAAQ;YAAErC,MAAM,EAAErG,QAAQ,GAAG,WAAW,GAAG;UAAY,CAAE;UAAAsE,QAAA,EACpItE,QAAQ,GACJrB,WAAW,GAAG,uBAAuB,GAAG,wBAAwB,GAChEA,WAAW,GAAG,+BAA+B,GAAG;QAAuC;UAAAgH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE3F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC,gBACN,CACH,eAEDxH,OAAA;MAAAgG,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA,eACV,CAAC;AAEP,CAAC;AAACpH,EAAA,CAlkBID,mBAAmB;EAAA,QACCR,WAAW,EAClBC,WAAW,EACXC,WAAW;AAAA;AAAAwK,EAAA,GAHxBlK,mBAAmB;AAokBzB,eAAeA,mBAAmB;AAAC,IAAAkK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}