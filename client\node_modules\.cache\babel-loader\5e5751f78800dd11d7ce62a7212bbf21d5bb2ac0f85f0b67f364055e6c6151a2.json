{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoLessons = () => {\n  _s();\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.user) {\n        console.log('✅ Found user in state.user');\n        return state.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState({});\n  const [newComment, setNewComment] = useState('');\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [replyingTo, setReplyingTo] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = video => {\n    // Priority 1: Use actual thumbnail from database\n    if (video.thumbnailUrl && !video.thumbnailUrl.includes('youtube.com')) {\n      return video.thumbnailUrl;\n    }\n\n    // Priority 2: Use video poster/preview image from database\n    if (video.posterUrl) {\n      return video.posterUrl;\n    }\n\n    // Priority 3: Use image field from database\n    if (video.image) {\n      return video.image;\n    }\n\n    // Priority 4: For YouTube videos only, use YouTube thumbnail as fallback\n    if (video.videoID && !video.videoID.includes('amazonaws.com') && !video.videoUrl) {\n      let videoId = video.videoID;\n      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n      videoId = match ? match[1] : videoId;\n      return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n    }\n\n    // Priority 5: Use a clean placeholder for uploaded videos\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n  };\n  const getSubjectName = subject => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Comment helper functions\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    return comments[video === null || video === void 0 ? void 0 : video.id] || [];\n  };\n  const formatTimeAgo = timestamp => {\n    if (!timestamp) return 'Just now';\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  };\n\n  // Comment handlers\n  const handleAddComment = async () => {\n    if (!newComment.trim() || currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const comment = {\n      id: Date.now().toString(),\n      text: newComment.trim(),\n      author: (user === null || user === void 0 ? void 0 : user.name) || 'Anonymous',\n      user: user === null || user === void 0 ? void 0 : user._id,\n      userRole: user === null || user === void 0 ? void 0 : user.role,\n      isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin',\n      timestamp: new Date().toISOString(),\n      createdAt: new Date().toISOString(),\n      likes: 0,\n      likedBy: []\n    };\n    setComments(prev => ({\n      ...prev,\n      [video.id]: [...(prev[video.id] || []), comment]\n    }));\n    setNewComment('');\n  };\n  const handleLikeComment = commentId => {\n    if (!(user !== null && user !== void 0 && user._id) || currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    setComments(prev => ({\n      ...prev,\n      [video.id]: (prev[video.id] || []).map(comment => {\n        if (comment.id === commentId) {\n          var _comment$likedBy;\n          const isLiked = (_comment$likedBy = comment.likedBy) === null || _comment$likedBy === void 0 ? void 0 : _comment$likedBy.includes(user._id);\n          return {\n            ...comment,\n            likes: isLiked ? comment.likes - 1 : comment.likes + 1,\n            likedBy: isLiked ? comment.likedBy.filter(id => id !== user._id) : [...(comment.likedBy || []), user._id]\n          };\n        }\n        return comment;\n      })\n    }));\n  };\n  const handleDeleteComment = commentId => {\n    if (currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    setComments(prev => ({\n      ...prev,\n      [video.id]: (prev[video.id] || []).filter(comment => comment.id !== commentId)\n    }));\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('🎥 Attempting to fetch videos from database...');\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        var _response, _response2;\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n        if ((_response = response) !== null && _response !== void 0 && _response.success && (_response2 = response) !== null && _response2 !== void 0 && _response2.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          var _response3, _response3$data, _response4, _response4$data;\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n          if ((_response3 = response) !== null && _response3 !== void 0 && (_response3$data = _response3.data) !== null && _response3$data !== void 0 && _response3$data.success && (_response4 = response) !== null && _response4 !== void 0 && (_response4$data = _response4.data) !== null && _response4$data !== void 0 && _response4$data.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          var _response5, _response5$data, _response6, _response6$data;\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n          if ((_response5 = response) !== null && _response5 !== void 0 && (_response5$data = _response5.data) !== null && _response5$data !== void 0 && _response5$data.success && (_response6 = response) !== null && _response6 !== void 0 && (_response6$data = _response6.data) !== null && _response6$data !== void 0 && _response6$data.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item => {\n              var _item$title;\n              return item.type === 'video' || item.videoUrl || item.videoID || ((_item$title = item.title) === null || _item$title === void 0 ? void 0 : _item$title.toLowerCase().includes('video'));\n            });\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel || !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass || !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject || !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      var _video$title, _video$subject, _video$topic;\n      const matchesSearch = !searchTerm || ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isKiswahili ? 'Masomo ya Video' : 'Video Lessons'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: isKiswahili ? 'Tafuta video...' : 'Search videos...',\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLevel,\n          onChange: e => setSelectedLevel(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"primary\",\n            children: isKiswahili ? 'Msingi' : 'Primary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"secondary\",\n            children: isKiswahili ? 'Sekondari' : 'Secondary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advance\",\n            children: isKiswahili ? 'Juu' : 'Advanced'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedClass,\n          onChange: e => setSelectedClass(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedSubject,\n          onChange: e => setSelectedSubject(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => {\n          var _user$name, _user$name$charAt;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card\",\n              onClick: () => handleShowVideo(index),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-thumbnail\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getThumbnailUrl(video),\n                  alt: video.title,\n                  className: \"thumbnail-image\",\n                  loading: \"lazy\",\n                  onError: e => {\n                    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                      let videoId = video.videoID;\n                      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                      videoId = match ? match[1] : videoId;\n                      const fallbacks = [`https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`, `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4='];\n                      const currentSrc = e.target.src;\n                      const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop().split('.')[0]));\n                      if (currentIndex < fallbacks.length - 1) {\n                        e.target.src = fallbacks[currentIndex + 1];\n                      }\n                    } else {\n                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"play-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                    className: \"play-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-duration\",\n                  children: video.duration || \"Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 25\n                  }, this), \"CC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-tags\",\n                  children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"topic-tag\",\n                    children: video.topic\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 39\n                  }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"shared-tag\",\n                    children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this), currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-video-player\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-style-layout\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-player\",\n                  children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: ref => setVideoRef(ref),\n                    controls: true,\n                    autoPlay: true,\n                    playsInline: true,\n                    preload: \"metadata\",\n                    width: \"100%\",\n                    height: \"100%\",\n                    poster: getThumbnailUrl(video),\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      backgroundColor: '#000',\n                      objectFit: 'contain'\n                    },\n                    onError: e => setVideoError(`Failed to load video: ${video.title}`),\n                    onCanPlay: () => setVideoError(null),\n                    crossOrigin: \"anonymous\",\n                    children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                      src: video.signedVideoUrl || video.videoUrl,\n                      type: \"video/mp4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 521,\n                      columnNumber: 29\n                    }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                      kind: \"subtitles\",\n                      src: subtitle.url,\n                      srcLang: subtitle.language,\n                      label: subtitle.languageName,\n                      default: subtitle.isDefault || index === 0\n                    }, `${subtitle.language}-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 523,\n                      columnNumber: 31\n                    }, this)), \"Your browser does not support the video tag.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 27\n                  }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                    src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                    title: video.title,\n                    frameBorder: \"0\",\n                    allowFullScreen: true,\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      border: 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"video-error\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"error-icon\",\n                      children: \"\\u26A0\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 544,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      children: \"Video Unavailable\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 545,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: videoError || \"This video cannot be played at the moment.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"youtube-video-title\",\n                    children: video.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-video-meta\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: getSubjectName(video.subject)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 554,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 555,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Class \", video.className || video.class]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 556,\n                      columnNumber: 27\n                    }, this), video.level && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 559,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: video.level\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 560,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-video-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `youtube-action-btn ${commentsExpanded ? 'active' : ''}`,\n                      onClick: () => setCommentsExpanded(!commentsExpanded),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 569,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Comments\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 570,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"youtube-action-btn\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83D\\uDC4D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 573,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Like\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 574,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"youtube-action-btn\",\n                      onClick: () => setCurrentVideoIndex(null),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2715\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 580,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Close\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 581,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 576,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 23\n                }, this), commentsExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comments-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comments-header\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [getCurrentVideoComments().length, \" Comments\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comment-input\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-comment-avatar\",\n                      children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || \"A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                        className: \"youtube-comment-input-field\",\n                        value: newComment,\n                        onChange: e => setNewComment(e.target.value),\n                        placeholder: \"Add a comment...\",\n                        rows: \"1\",\n                        style: {\n                          minHeight: '20px',\n                          resize: 'none',\n                          overflow: 'hidden'\n                        },\n                        onInput: e => {\n                          e.target.style.height = 'auto';\n                          e.target.style.height = e.target.scrollHeight + 'px';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 599,\n                        columnNumber: 31\n                      }, this), newComment.trim() && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment-actions\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"youtube-comment-btn cancel\",\n                          onClick: () => setNewComment(''),\n                          children: \"Cancel\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 617,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"youtube-comment-btn submit\",\n                          onClick: handleAddComment,\n                          disabled: !newComment.trim(),\n                          children: \"Comment\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 623,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 616,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 598,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comments-list\",\n                    children: getCurrentVideoComments().length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        textAlign: 'center',\n                        padding: '40px 0',\n                        color: '#606060'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '48px',\n                          marginBottom: '16px'\n                        },\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 639,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No comments yet. Be the first to share your thoughts!\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 640,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 31\n                    }, this) : getCurrentVideoComments().map(comment => {\n                      var _comment$author, _comment$author$charA, _comment$likedBy2, _comment$likedBy3;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-avatar\",\n                          children: comment.avatar || ((_comment$author = comment.author) === null || _comment$author === void 0 ? void 0 : (_comment$author$charA = _comment$author.charAt(0)) === null || _comment$author$charA === void 0 ? void 0 : _comment$author$charA.toUpperCase()) || \"A\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 645,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-content\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-header\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"youtube-comment-author\",\n                              children: comment.author\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 650,\n                              columnNumber: 39\n                            }, this), (comment.userRole === 'admin' || comment.isAdmin) && /*#__PURE__*/_jsxDEV(MdVerified, {\n                              style: {\n                                color: '#1d9bf0',\n                                fontSize: '12px',\n                                marginLeft: '4px'\n                              },\n                              title: \"Verified Admin\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 652,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"youtube-comment-time\",\n                              children: formatTimeAgo(comment.createdAt || comment.timestamp)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 654,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 649,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-text\",\n                            children: comment.text\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 658,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-actions\",\n                            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => handleLikeComment(comment._id || comment.id),\n                              className: `youtube-comment-action ${(_comment$likedBy2 = comment.likedBy) !== null && _comment$likedBy2 !== void 0 && _comment$likedBy2.includes(user === null || user === void 0 ? void 0 : user._id) ? 'liked' : ''}`,\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                children: (_comment$likedBy3 = comment.likedBy) !== null && _comment$likedBy3 !== void 0 && _comment$likedBy3.includes(user === null || user === void 0 ? void 0 : user._id) ? '👍' : '👍'\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 666,\n                                columnNumber: 41\n                              }, this), comment.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: comment.likes\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 667,\n                                columnNumber: 63\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 662,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"youtube-comment-action\",\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: \"\\uD83D\\uDC4E\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 670,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 669,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"youtube-comment-action\",\n                              children: \"Reply\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 672,\n                              columnNumber: 39\n                            }, this), comment.user === (user === null || user === void 0 ? void 0 : user._id) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-action\",\n                                children: \"Edit\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 677,\n                                columnNumber: 43\n                              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-action\",\n                                onClick: () => {\n                                  if (window.confirm('Are you sure you want to delete this comment?')) {\n                                    handleDeleteComment(comment._id || comment.id);\n                                  }\n                                },\n                                children: \"Delete\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 680,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 661,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 648,\n                          columnNumber: 35\n                        }, this)]\n                      }, comment._id || comment.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 644,\n                        columnNumber: 33\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 711,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 707,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 359,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoLessons, \"sUl5UJEZU46h2aSoRLty1l9iUvM=\", false, function () {\n  return [useSelector];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useSelector", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "TbAlertTriangle", "MdVerified", "getStudyMaterial", "getAllVideos", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoLessons", "_s", "user", "state", "console", "log", "Object", "keys", "users", "auth", "storedUser", "localStorage", "getItem", "JSON", "parse", "e", "error", "videos", "setVideos", "loading", "setLoading", "setError", "searchTerm", "setSearchTerm", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "currentVideoIndex", "setCurrentVideoIndex", "videoRef", "setVideoRef", "videoError", "setVideoError", "comments", "setComments", "newComment", "setNewComment", "commentsExpanded", "setCommentsExpanded", "replyingTo", "setReplyingTo", "isKiswahili", "getThumbnailUrl", "video", "thumbnailUrl", "includes", "posterUrl", "image", "videoID", "videoUrl", "videoId", "match", "getSubjectName", "subject", "subjectMap", "getCurrentVideoComments", "filteredAndSortedVideos", "id", "formatTimeAgo", "timestamp", "now", "Date", "time", "diffInSeconds", "Math", "floor", "handleAddComment", "trim", "comment", "toString", "text", "author", "name", "_id", "userRole", "role", "isAdmin", "toISOString", "createdAt", "likes", "<PERSON><PERSON><PERSON>", "prev", "handleLikeComment", "commentId", "map", "_comment$likedBy", "isLiked", "filter", "handleDeleteComment", "handleShowVideo", "index", "signedVideoUrl", "warn", "fetchVideos", "response", "_response", "_response2", "success", "data", "length", "message", "_response3", "_response3$data", "_response4", "_response4$data", "filters", "level", "type", "_response5", "_response5$data", "_response6", "_response6$data", "allData", "item", "_item$title", "title", "toLowerCase", "filtered", "matchesLevel", "matchesClass", "className", "class", "matchesSubject", "err", "_video$title", "_video$subject", "_video$topic", "matchesSearch", "topic", "sort", "a", "b", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "target", "onClick", "_user$name", "_user$name$charAt", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "duration", "subtitles", "sharedFromClass", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "style", "backgroundColor", "objectFit", "onCanPlay", "crossOrigin", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "char<PERSON>t", "toUpperCase", "flex", "rows", "minHeight", "resize", "overflow", "onInput", "scrollHeight", "disabled", "textAlign", "padding", "color", "fontSize", "marginBottom", "_comment$author", "_comment$author$charA", "_comment$likedBy2", "_comment$likedBy3", "avatar", "marginLeft", "window", "confirm", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\n\nconst VideoLessons = () => {\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.user) {\n        console.log('✅ Found user in state.user');\n        return state.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState({});\n  const [newComment, setNewComment] = useState('');\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [replyingTo, setReplyingTo] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = (video) => {\n    // Priority 1: Use actual thumbnail from database\n    if (video.thumbnailUrl && !video.thumbnailUrl.includes('youtube.com')) {\n      return video.thumbnailUrl;\n    }\n\n    // Priority 2: Use video poster/preview image from database\n    if (video.posterUrl) {\n      return video.posterUrl;\n    }\n\n    // Priority 3: Use image field from database\n    if (video.image) {\n      return video.image;\n    }\n\n    // Priority 4: For YouTube videos only, use YouTube thumbnail as fallback\n    if (video.videoID && !video.videoID.includes('amazonaws.com') && !video.videoUrl) {\n      let videoId = video.videoID;\n      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n      videoId = match ? match[1] : videoId;\n      return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n    }\n\n    // Priority 5: Use a clean placeholder for uploaded videos\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n  };\n\n  const getSubjectName = (subject) => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Comment helper functions\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    return comments[video?.id] || [];\n  };\n\n  const formatTimeAgo = (timestamp) => {\n    if (!timestamp) return 'Just now';\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  };\n\n  // Comment handlers\n  const handleAddComment = async () => {\n    if (!newComment.trim() || currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const comment = {\n      id: Date.now().toString(),\n      text: newComment.trim(),\n      author: user?.name || 'Anonymous',\n      user: user?._id,\n      userRole: user?.role,\n      isAdmin: user?.role === 'admin',\n      timestamp: new Date().toISOString(),\n      createdAt: new Date().toISOString(),\n      likes: 0,\n      likedBy: []\n    };\n\n    setComments(prev => ({\n      ...prev,\n      [video.id]: [...(prev[video.id] || []), comment]\n    }));\n\n    setNewComment('');\n  };\n\n  const handleLikeComment = (commentId) => {\n    if (!user?._id || currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    setComments(prev => ({\n      ...prev,\n      [video.id]: (prev[video.id] || []).map(comment => {\n        if (comment.id === commentId) {\n          const isLiked = comment.likedBy?.includes(user._id);\n          return {\n            ...comment,\n            likes: isLiked ? comment.likes - 1 : comment.likes + 1,\n            likedBy: isLiked\n              ? comment.likedBy.filter(id => id !== user._id)\n              : [...(comment.likedBy || []), user._id]\n          };\n        }\n        return comment;\n      })\n    }));\n  };\n\n  const handleDeleteComment = (commentId) => {\n    if (currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    setComments(prev => ({\n      ...prev,\n      [video.id]: (prev[video.id] || []).filter(comment => comment.id !== commentId)\n    }));\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🎥 Attempting to fetch videos from database...');\n\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n\n        if (response?.success && response?.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item =>\n              item.type === 'video' ||\n              item.videoUrl ||\n              item.videoID ||\n              item.title?.toLowerCase().includes('video')\n            );\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' ||\n                              video.level === selectedLevel ||\n                              !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' ||\n                              video.className === selectedClass ||\n                              video.class === selectedClass ||\n                              !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' ||\n                                video.subject === selectedSubject ||\n                                !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      const matchesSearch = !searchTerm ||\n        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());\n\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n\n  return (\n    <div className=\"video-lessons-container\">\n      <div className=\"video-lessons-header\">\n        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>\n        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"video-controls\">\n        <div className=\"search-section\">\n          <input\n            type=\"text\"\n            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n\n        <div className=\"filter-section\">\n          <select\n            value={selectedLevel}\n            onChange={(e) => setSelectedLevel(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"primary\">{isKiswahili ? 'Msingi' : 'Primary'}</option>\n            <option value=\"secondary\">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>\n            <option value=\"advance\">{isKiswahili ? 'Juu' : 'Advanced'}</option>\n          </select>\n\n          <select\n            value={selectedClass}\n            onChange={(e) => setSelectedClass(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n            {/* Add class options based on selected level */}\n          </select>\n\n          <select\n            value={selectedSubject}\n            onChange={(e) => setSelectedSubject(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n            {/* Add subject options */}\n          </select>\n        </div>\n      </div>\n\n      {/* Video Content */}\n      <div className=\"video-content\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-item\">\n                {/* Video Card */}\n                <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                  <div className=\"video-card-thumbnail\">\n                    <img\n                      src={getThumbnailUrl(video)}\n                      alt={video.title}\n                      className=\"thumbnail-image\"\n                      loading=\"lazy\"\n                      onError={(e) => {\n                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                          let videoId = video.videoID;\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                          const fallbacks = [\n                            `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                            'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4='\n                          ];\n                          const currentSrc = e.target.src;\n                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop().split('.')[0]));\n                          if (currentIndex < fallbacks.length - 1) {\n                            e.target.src = fallbacks[currentIndex + 1];\n                          }\n                        } else {\n                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                    </div>\n                    <div className=\"video-duration\">\n                      {video.duration || \"Video\"}\n                    </div>\n                    {video.subtitles && video.subtitles.length > 0 && (\n                      <div className=\"subtitle-badge\">\n                        <TbInfoCircle />\n                        CC\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"video-card-content\">\n                    <h3 className=\"video-title\">{video.title}</h3>\n                    <div className=\"video-meta\">\n                      <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                      <span className=\"video-class\">\n                        {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                          ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)\n                          : `Form ${video.className || video.class}`}\n                      </span>\n                    </div>\n                    <div className=\"video-tags\">\n                      {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                      {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                        <span className=\"shared-tag\">\n                          {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}\n                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                            ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)\n                            : `Form ${video.sharedFromClass}`}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Inline Video Player */}\n                {currentVideoIndex === index && (\n                  <div className=\"inline-video-player\">\n                    <div className=\"youtube-style-layout\">\n                      <div className=\"youtube-video-player\">\n                        {video.videoUrl ? (\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"100%\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            }}\n                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}\n                            onCanPlay={() => setVideoError(null)}\n                            crossOrigin=\"anonymous\"\n                          >\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n                            Your browser does not support the video tag.\n                          </video>\n                        ) : video.videoID ? (\n                          <iframe\n                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                            title={video.title}\n                            frameBorder=\"0\"\n                            allowFullScreen\n                            style={{ width: '100%', height: '100%', border: 'none' }}\n                          ></iframe>\n                        ) : (\n                          <div className=\"video-error\">\n                            <div className=\"error-icon\">⚠️</div>\n                            <h3>Video Unavailable</h3>\n                            <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"youtube-video-info\">\n                        <h1 className=\"youtube-video-title\">{video.title}</h1>\n                        <div className=\"youtube-video-meta\">\n                          <span>{getSubjectName(video.subject)}</span>\n                          <span>•</span>\n                          <span>Class {video.className || video.class}</span>\n                          {video.level && (\n                            <>\n                              <span>•</span>\n                              <span>{video.level}</span>\n                            </>\n                          )}\n                        </div>\n                        <div className=\"youtube-video-actions\">\n                          <button\n                            className={`youtube-action-btn ${commentsExpanded ? 'active' : ''}`}\n                            onClick={() => setCommentsExpanded(!commentsExpanded)}\n                          >\n                            <span>💬</span>\n                            <span>Comments</span>\n                          </button>\n                          <button className=\"youtube-action-btn\">\n                            <span>👍</span>\n                            <span>Like</span>\n                          </button>\n                          <button\n                            className=\"youtube-action-btn\"\n                            onClick={() => setCurrentVideoIndex(null)}\n                          >\n                            <span>✕</span>\n                            <span>Close</span>\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {commentsExpanded && (\n                        <div className=\"youtube-comments-section\">\n                          <div className=\"youtube-comments-header\">\n                            <span>{getCurrentVideoComments().length} Comments</span>\n                          </div>\n\n                          {/* Add Comment */}\n                          <div className=\"youtube-comment-input\">\n                            <div className=\"youtube-comment-avatar\">\n                              {user?.name?.charAt(0)?.toUpperCase() || \"A\"}\n                            </div>\n                            <div style={{ flex: 1 }}>\n                              <textarea\n                                className=\"youtube-comment-input-field\"\n                                value={newComment}\n                                onChange={(e) => setNewComment(e.target.value)}\n                                placeholder=\"Add a comment...\"\n                                rows=\"1\"\n                                style={{\n                                  minHeight: '20px',\n                                  resize: 'none',\n                                  overflow: 'hidden'\n                                }}\n                                onInput={(e) => {\n                                  e.target.style.height = 'auto';\n                                  e.target.style.height = e.target.scrollHeight + 'px';\n                                }}\n                              />\n                              {newComment.trim() && (\n                                <div className=\"youtube-comment-actions\">\n                                  <button\n                                    className=\"youtube-comment-btn cancel\"\n                                    onClick={() => setNewComment('')}\n                                  >\n                                    Cancel\n                                  </button>\n                                  <button\n                                    className=\"youtube-comment-btn submit\"\n                                    onClick={handleAddComment}\n                                    disabled={!newComment.trim()}\n                                  >\n                                    Comment\n                                  </button>\n                                </div>\n                              )}\n                            </div>\n                          </div>\n\n                          {/* Comments List */}\n                          <div className=\"youtube-comments-list\">\n                            {getCurrentVideoComments().length === 0 ? (\n                              <div style={{ textAlign: 'center', padding: '40px 0', color: '#606060' }}>\n                                <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                                <p>No comments yet. Be the first to share your thoughts!</p>\n                              </div>\n                            ) : (\n                              getCurrentVideoComments().map((comment) => (\n                                <div key={comment._id || comment.id} className=\"youtube-comment\">\n                                  <div className=\"youtube-comment-avatar\">\n                                    {comment.avatar || comment.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                  </div>\n                                  <div className=\"youtube-comment-content\">\n                                    <div className=\"youtube-comment-header\">\n                                      <span className=\"youtube-comment-author\">{comment.author}</span>\n                                      {(comment.userRole === 'admin' || comment.isAdmin) && (\n                                        <MdVerified style={{ color: '#1d9bf0', fontSize: '12px', marginLeft: '4px' }} title=\"Verified Admin\" />\n                                      )}\n                                      <span className=\"youtube-comment-time\">\n                                        {formatTimeAgo(comment.createdAt || comment.timestamp)}\n                                      </span>\n                                    </div>\n                                    <div className=\"youtube-comment-text\">\n                                      {comment.text}\n                                    </div>\n                                    <div className=\"youtube-comment-actions\">\n                                      <button\n                                        onClick={() => handleLikeComment(comment._id || comment.id)}\n                                        className={`youtube-comment-action ${comment.likedBy?.includes(user?._id) ? 'liked' : ''}`}\n                                      >\n                                        <span>{comment.likedBy?.includes(user?._id) ? '👍' : '👍'}</span>\n                                        {comment.likes > 0 && <span>{comment.likes}</span>}\n                                      </button>\n                                      <button className=\"youtube-comment-action\">\n                                        <span>👎</span>\n                                      </button>\n                                      <button className=\"youtube-comment-action\">\n                                        Reply\n                                      </button>\n                                      {comment.user === user?._id && (\n                                        <>\n                                          <button className=\"youtube-comment-action\">\n                                            Edit\n                                          </button>\n                                          <button\n                                            className=\"youtube-comment-action\"\n                                            onClick={() => {\n                                              if (window.confirm('Are you sure you want to delete this comment?')) {\n                                                handleDeleteComment(comment._id || comment.id);\n                                              }\n                                            }}\n                                          >\n                                            Delete\n                                          </button>\n                                        </>\n                                      )}\n                                    </div>\n                                  </div>\n                                </div>\n                              ))\n                            )}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAMC,IAAI,GAAGd,WAAW,CAACe,KAAK,IAAI;IAChC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,KAAK,CAAC;MACpDC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,MAAM,CAACC,IAAI,CAACJ,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;;MAEjE;MACA,IAAIA,KAAK,IAAIA,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK,CAACN,IAAI,EAAE;QAC5CE,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,OAAOF,KAAK,CAACK,KAAK,CAACN,IAAI;MACzB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACD,IAAI,EAAE;QACvBE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,OAAOF,KAAK,CAACD,IAAI;MACnB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACM,IAAI,CAACP,IAAI,EAAE;QAC1CE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAOF,KAAK,CAACM,IAAI,CAACP,IAAI;MACxB;MAEAE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMK,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIF,UAAU,EAAE;QACd,IAAI;UACFN,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,OAAOQ,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;QAC/B,CAAC,CAAC,OAAOK,CAAC,EAAE;UACVX,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C;MACF;MAEAD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,OAAO,IAAI;IACb,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,KAAK,CAAC;MACrD,OAAO,IAAI;IACb;EACF,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,KAAK,EAAEK,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4C,eAAe,EAAEC,kBAAkB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM4D,WAAW,GAAGpB,aAAa,KAAK,mBAAmB;;EAEzD;EACA,MAAMqB,eAAe,GAAIC,KAAK,IAAK;IACjC;IACA,IAAIA,KAAK,CAACC,YAAY,IAAI,CAACD,KAAK,CAACC,YAAY,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;MACrE,OAAOF,KAAK,CAACC,YAAY;IAC3B;;IAEA;IACA,IAAID,KAAK,CAACG,SAAS,EAAE;MACnB,OAAOH,KAAK,CAACG,SAAS;IACxB;;IAEA;IACA,IAAIH,KAAK,CAACI,KAAK,EAAE;MACf,OAAOJ,KAAK,CAACI,KAAK;IACpB;;IAEA;IACA,IAAIJ,KAAK,CAACK,OAAO,IAAI,CAACL,KAAK,CAACK,OAAO,CAACH,QAAQ,CAAC,eAAe,CAAC,IAAI,CAACF,KAAK,CAACM,QAAQ,EAAE;MAChF,IAAIC,OAAO,GAAGP,KAAK,CAACK,OAAO;MAC3B,MAAMG,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;MACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACpC,OAAQ,8BAA6BA,OAAQ,gBAAe;IAC9D;;IAEA;IACA,OAAO,4cAA4c;EACrd,CAAC;EAED,MAAME,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,UAAU,GAAG;MACjB,aAAa,EAAEb,WAAW,GAAG,UAAU,GAAG,aAAa;MACvD,SAAS,EAAEA,WAAW,GAAG,YAAY,GAAG,SAAS;MACjD,WAAW,EAAE,WAAW;MACxB,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG,SAAS;MAC9C,gBAAgB,EAAEA,WAAW,GAAG,kBAAkB,GAAG,gBAAgB;MACrE,QAAQ,EAAEA,WAAW,GAAG,OAAO,GAAG,QAAQ;MAC1C,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,WAAW,GAAG,WAAW;MACpD,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,OAAO,GAAG,WAAW;MAChD,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG;IACvC,CAAC;IACD,OAAOa,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO;EACvC,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI5B,iBAAiB,KAAK,IAAI,EAAE,OAAO,EAAE;IACzC,MAAMgB,KAAK,GAAGa,uBAAuB,CAAC7B,iBAAiB,CAAC;IACxD,OAAOM,QAAQ,CAACU,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEc,EAAE,CAAC,IAAI,EAAE;EAClC,CAAC;EAED,MAAMC,aAAa,GAAIC,SAAS,IAAK;IACnC,IAAI,CAACA,SAAS,EAAE,OAAO,UAAU;IACjC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,SAAS,CAAC;IAChC,MAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,GAAGE,IAAI,IAAI,IAAI,CAAC;IAErD,IAAIC,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAE,cAAa;IAChF,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAE,YAAW;IACjF,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAE,WAAU;EACxD,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC/B,UAAU,CAACgC,IAAI,CAAC,CAAC,IAAIxC,iBAAiB,KAAK,IAAI,EAAE;IAEtD,MAAMgB,KAAK,GAAGa,uBAAuB,CAAC7B,iBAAiB,CAAC;IACxD,MAAMyC,OAAO,GAAG;MACdX,EAAE,EAAEI,IAAI,CAACD,GAAG,CAAC,CAAC,CAACS,QAAQ,CAAC,CAAC;MACzBC,IAAI,EAAEnC,UAAU,CAACgC,IAAI,CAAC,CAAC;MACvBI,MAAM,EAAE,CAAAxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,IAAI,KAAI,WAAW;MACjCzE,IAAI,EAAEA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,GAAG;MACfC,QAAQ,EAAE3E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,IAAI;MACpBC,OAAO,EAAE,CAAA7E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,IAAI,MAAK,OAAO;MAC/BhB,SAAS,EAAE,IAAIE,IAAI,CAAC,CAAC,CAACgB,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIjB,IAAI,CAAC,CAAC,CAACgB,WAAW,CAAC,CAAC;MACnCE,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE;IACX,CAAC;IAED9C,WAAW,CAAC+C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACtC,KAAK,CAACc,EAAE,GAAG,CAAC,IAAIwB,IAAI,CAACtC,KAAK,CAACc,EAAE,CAAC,IAAI,EAAE,CAAC,EAAEW,OAAO;IACjD,CAAC,CAAC,CAAC;IAEHhC,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAM8C,iBAAiB,GAAIC,SAAS,IAAK;IACvC,IAAI,EAACpF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0E,GAAG,KAAI9C,iBAAiB,KAAK,IAAI,EAAE;IAE9C,MAAMgB,KAAK,GAAGa,uBAAuB,CAAC7B,iBAAiB,CAAC;IACxDO,WAAW,CAAC+C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACtC,KAAK,CAACc,EAAE,GAAG,CAACwB,IAAI,CAACtC,KAAK,CAACc,EAAE,CAAC,IAAI,EAAE,EAAE2B,GAAG,CAAChB,OAAO,IAAI;QAChD,IAAIA,OAAO,CAACX,EAAE,KAAK0B,SAAS,EAAE;UAAA,IAAAE,gBAAA;UAC5B,MAAMC,OAAO,IAAAD,gBAAA,GAAGjB,OAAO,CAACY,OAAO,cAAAK,gBAAA,uBAAfA,gBAAA,CAAiBxC,QAAQ,CAAC9C,IAAI,CAAC0E,GAAG,CAAC;UACnD,OAAO;YACL,GAAGL,OAAO;YACVW,KAAK,EAAEO,OAAO,GAAGlB,OAAO,CAACW,KAAK,GAAG,CAAC,GAAGX,OAAO,CAACW,KAAK,GAAG,CAAC;YACtDC,OAAO,EAAEM,OAAO,GACZlB,OAAO,CAACY,OAAO,CAACO,MAAM,CAAC9B,EAAE,IAAIA,EAAE,KAAK1D,IAAI,CAAC0E,GAAG,CAAC,GAC7C,CAAC,IAAIL,OAAO,CAACY,OAAO,IAAI,EAAE,CAAC,EAAEjF,IAAI,CAAC0E,GAAG;UAC3C,CAAC;QACH;QACA,OAAOL,OAAO;MAChB,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMoB,mBAAmB,GAAIL,SAAS,IAAK;IACzC,IAAIxD,iBAAiB,KAAK,IAAI,EAAE;IAEhC,MAAMgB,KAAK,GAAGa,uBAAuB,CAAC7B,iBAAiB,CAAC;IACxDO,WAAW,CAAC+C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACtC,KAAK,CAACc,EAAE,GAAG,CAACwB,IAAI,CAACtC,KAAK,CAACc,EAAE,CAAC,IAAI,EAAE,EAAE8B,MAAM,CAACnB,OAAO,IAAIA,OAAO,CAACX,EAAE,KAAK0B,SAAS;IAC/E,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMM,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAM/C,KAAK,GAAGa,uBAAuB,CAACkC,KAAK,CAAC;IAC5C9D,oBAAoB,CAAC8D,KAAK,CAAC;IAC3B1D,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIW,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEM,QAAQ,KAAKN,KAAK,CAACM,QAAQ,CAACJ,QAAQ,CAAC,eAAe,CAAC,IAAIF,KAAK,CAACM,QAAQ,CAACJ,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF;QACA;QACA;QACAF,KAAK,CAACgD,cAAc,GAAGhD,KAAK,CAACM,QAAQ;MACvC,CAAC,CAAC,OAAOpC,KAAK,EAAE;QACdZ,OAAO,CAAC2F,IAAI,CAAC,8CAA8C,CAAC;QAC5DjD,KAAK,CAACgD,cAAc,GAAGhD,KAAK,CAACM,QAAQ;MACvC;IACF;EACF,CAAC;;EAED;EACA,MAAM4C,WAAW,GAAG7G,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFiC,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC;MAEdjB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI4F,QAAQ,GAAG,IAAI;MACnB,IAAIhF,MAAM,GAAG,EAAE;;MAEf;MACA,IAAI;QAAA,IAAAiF,SAAA,EAAAC,UAAA;QACF/F,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD4F,QAAQ,GAAG,MAAMtG,YAAY,CAAC,CAAC;QAC/BS,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE4F,QAAQ,CAAC;QAE/C,IAAI,CAAAC,SAAA,GAAAD,QAAQ,cAAAC,SAAA,eAARA,SAAA,CAAUE,OAAO,KAAAD,UAAA,GAAIF,QAAQ,cAAAE,UAAA,eAARA,UAAA,CAAUE,IAAI,EAAE;UACvCpF,MAAM,GAAGgF,QAAQ,CAACI,IAAI;UACtBjG,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEY,MAAM,CAACqF,MAAM,CAAC;QAC/E;MACF,CAAC,CAAC,OAAOtF,KAAK,EAAE;QACdZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEW,KAAK,CAACuF,OAAO,CAAC;MACtD;;MAEA;MACA,IAAItF,MAAM,CAACqF,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAE,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACFvG,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,MAAMuG,OAAO,GAAG;YACdC,KAAK,EAAErF,aAAa;YACpBsF,IAAI,EAAE;UACR,CAAC;UAEDb,QAAQ,GAAG,MAAMvG,gBAAgB,CAACkH,OAAO,CAAC;UAC1CxG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4F,QAAQ,CAAC;UAEnD,IAAI,CAAAO,UAAA,GAAAP,QAAQ,cAAAO,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUH,IAAI,cAAAI,eAAA,eAAdA,eAAA,CAAgBL,OAAO,KAAAM,UAAA,GAAIT,QAAQ,cAAAS,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUL,IAAI,cAAAM,eAAA,eAAdA,eAAA,CAAgBN,IAAI,EAAE;YACnDpF,MAAM,GAAGgF,QAAQ,CAACI,IAAI,CAACA,IAAI;YAC3BjG,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEY,MAAM,CAACqF,MAAM,CAAC;UACnF;QACF,CAAC,CAAC,OAAOtF,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,KAAK,CAACuF,OAAO,CAAC;QAC1D;MACF;;MAEA;MACA,IAAItF,MAAM,CAACqF,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAS,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACF9G,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5D4F,QAAQ,GAAG,MAAMvG,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACrCU,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE4F,QAAQ,CAAC;UAEhE,IAAI,CAAAc,UAAA,GAAAd,QAAQ,cAAAc,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUV,IAAI,cAAAW,eAAA,eAAdA,eAAA,CAAgBZ,OAAO,KAAAa,UAAA,GAAIhB,QAAQ,cAAAgB,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUZ,IAAI,cAAAa,eAAA,eAAdA,eAAA,CAAgBb,IAAI,EAAE;YACnD;YACA,MAAMc,OAAO,GAAGlB,QAAQ,CAACI,IAAI,CAACA,IAAI;YAClCpF,MAAM,GAAGkG,OAAO,CAACzB,MAAM,CAAC0B,IAAI;cAAA,IAAAC,WAAA;cAAA,OAC1BD,IAAI,CAACN,IAAI,KAAK,OAAO,IACrBM,IAAI,CAAChE,QAAQ,IACbgE,IAAI,CAACjE,OAAO,MAAAkE,WAAA,GACZD,IAAI,CAACE,KAAK,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,WAAW,CAAC,CAAC,CAACvE,QAAQ,CAAC,OAAO,CAAC;YAAA,CAC7C,CAAC;YACD5C,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,MAAM,CAACqF,MAAM,CAAC;UAC1E;QACF,CAAC,CAAC,OAAOtF,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEW,KAAK,CAACuF,OAAO,CAAC;QACvE;MACF;;MAEA;MACA,IAAItF,MAAM,CAACqF,MAAM,GAAG,CAAC,EAAE;QACrB,MAAMkB,QAAQ,GAAGvG,MAAM,CAACyE,MAAM,CAAC5C,KAAK,IAAI;UACtC,MAAM2E,YAAY,GAAGjG,aAAa,KAAK,KAAK,IACxBsB,KAAK,CAAC+D,KAAK,KAAKrF,aAAa,IAC7B,CAACsB,KAAK,CAAC+D,KAAK,CAAC,CAAC;;UAElC,MAAMa,YAAY,GAAGhG,aAAa,KAAK,KAAK,IACxBoB,KAAK,CAAC6E,SAAS,KAAKjG,aAAa,IACjCoB,KAAK,CAAC8E,KAAK,KAAKlG,aAAa,IAC7B,CAACoB,KAAK,CAAC6E,SAAS,CAAC,CAAC;;UAEtC,MAAME,cAAc,GAAGjG,eAAe,KAAK,KAAK,IAC1BkB,KAAK,CAACU,OAAO,KAAK5B,eAAe,IACjC,CAACkB,KAAK,CAACU,OAAO,CAAC,CAAC;;UAEtC,OAAOiE,YAAY,IAAIC,YAAY,IAAIG,cAAc;QACvD,CAAC,CAAC;QAEF3G,SAAS,CAACsG,QAAQ,CAAC;QACnBpH,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEmH,QAAQ,CAAClB,MAAM,EAAE,QAAQ,CAAC;QAErE,IAAIkB,QAAQ,CAAClB,MAAM,KAAK,CAAC,EAAE;UACzBjF,QAAQ,CAAC,wEAAwE,CAAC;QACpF;MACF,CAAC,MAAM;QACL;QACAjB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpED,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrCD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAElCgB,QAAQ,CAAC,yFAAyF,CAAC;QACnGH,SAAS,CAAC,EAAE,CAAC;MACf;IAEF,CAAC,CAAC,OAAO4G,GAAG,EAAE;MACZ1H,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAE8G,GAAG,CAAC;MACvDzG,QAAQ,CAAC,0DAA0D,CAAC;MACpEH,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACI,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEnD;EACA,MAAM+B,uBAAuB,GAAGzE,OAAO,CAAC,MAAM;IAC5C,IAAIsI,QAAQ,GAAGvG,MAAM,CAACyE,MAAM,CAAC5C,KAAK,IAAI;MAAA,IAAAiF,YAAA,EAAAC,cAAA,EAAAC,YAAA;MACpC,MAAMC,aAAa,GAAG,CAAC5G,UAAU,MAAAyG,YAAA,GAC/BjF,KAAK,CAACwE,KAAK,cAAAS,YAAA,uBAAXA,YAAA,CAAaR,WAAW,CAAC,CAAC,CAACvE,QAAQ,CAAC1B,UAAU,CAACiG,WAAW,CAAC,CAAC,CAAC,OAAAS,cAAA,GAC7DlF,KAAK,CAACU,OAAO,cAAAwE,cAAA,uBAAbA,cAAA,CAAeT,WAAW,CAAC,CAAC,CAACvE,QAAQ,CAAC1B,UAAU,CAACiG,WAAW,CAAC,CAAC,CAAC,OAAAU,YAAA,GAC/DnF,KAAK,CAACqF,KAAK,cAAAF,YAAA,uBAAXA,YAAA,CAAaV,WAAW,CAAC,CAAC,CAACvE,QAAQ,CAAC1B,UAAU,CAACiG,WAAW,CAAC,CAAC,CAAC;MAE/D,MAAME,YAAY,GAAGjG,aAAa,KAAK,KAAK,IAAIsB,KAAK,CAAC+D,KAAK,KAAKrF,aAAa;MAC7E,MAAMkG,YAAY,GAAGhG,aAAa,KAAK,KAAK,IAAIoB,KAAK,CAAC6E,SAAS,KAAKjG,aAAa,IAAIoB,KAAK,CAAC8E,KAAK,KAAKlG,aAAa;MAClH,MAAMmG,cAAc,GAAGjG,eAAe,KAAK,KAAK,IAAIkB,KAAK,CAACU,OAAO,KAAK5B,eAAe;MAErF,OAAOsG,aAAa,IAAIT,YAAY,IAAIC,YAAY,IAAIG,cAAc;IACxE,CAAC,CAAC;IAEF,OAAOL,QAAQ,CAACY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAItE,IAAI,CAACsE,CAAC,CAACrD,SAAS,CAAC,GAAG,IAAIjB,IAAI,CAACqE,CAAC,CAACpD,SAAS,CAAC,CAAC;EAC/E,CAAC,EAAE,CAAChE,MAAM,EAAEK,UAAU,EAAEE,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEvE;EACA3C,SAAS,CAAC,MAAM;IACd+G,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA/G,SAAS,CAAC,MAAM;IACd+G,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACxE,aAAa,EAAEE,aAAa,EAAEE,eAAe,EAAEoE,WAAW,CAAC,CAAC;EAEhE,oBACEnG,OAAA;IAAK8H,SAAS,EAAC,yBAAyB;IAAAY,QAAA,gBACtC1I,OAAA;MAAK8H,SAAS,EAAC,sBAAsB;MAAAY,QAAA,gBACnC1I,OAAA;QAAA0I,QAAA,EAAK3F,WAAW,GAAG,iBAAiB,GAAG;MAAe;QAAA4F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5D9I,OAAA;QAAA0I,QAAA,EAAI3F,WAAW,GAAG,iCAAiC,GAAG;MAAoC;QAAA4F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAGN9I,OAAA;MAAK8H,SAAS,EAAC,gBAAgB;MAAAY,QAAA,gBAC7B1I,OAAA;QAAK8H,SAAS,EAAC,gBAAgB;QAAAY,QAAA,eAC7B1I,OAAA;UACEiH,IAAI,EAAC,MAAM;UACX8B,WAAW,EAAEhG,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;UAClEiG,KAAK,EAAEvH,UAAW;UAClBwH,QAAQ,EAAG/H,CAAC,IAAKQ,aAAa,CAACR,CAAC,CAACgI,MAAM,CAACF,KAAK,CAAE;UAC/ClB,SAAS,EAAC;QAAc;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN9I,OAAA;QAAK8H,SAAS,EAAC,gBAAgB;QAAAY,QAAA,gBAC7B1I,OAAA;UACEgJ,KAAK,EAAErH,aAAc;UACrBsH,QAAQ,EAAG/H,CAAC,IAAKU,gBAAgB,CAACV,CAAC,CAACgI,MAAM,CAACF,KAAK,CAAE;UAClDlB,SAAS,EAAC,eAAe;UAAAY,QAAA,gBAEzB1I,OAAA;YAAQgJ,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAE3F,WAAW,GAAG,QAAQ,GAAG;UAAS;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrE9I,OAAA;YAAQgJ,KAAK,EAAC,WAAW;YAAAN,QAAA,EAAE3F,WAAW,GAAG,WAAW,GAAG;UAAW;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5E9I,OAAA;YAAQgJ,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAE3F,WAAW,GAAG,KAAK,GAAG;UAAU;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAET9I,OAAA;UACEgJ,KAAK,EAAEnH,aAAc;UACrBoH,QAAQ,EAAG/H,CAAC,IAAKY,gBAAgB,CAACZ,CAAC,CAACgI,MAAM,CAACF,KAAK,CAAE;UAClDlB,SAAS,EAAC,eAAe;UAAAY,QAAA,eAEzB1I,OAAA;YAAQgJ,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAE3F,WAAW,GAAG,eAAe,GAAG;UAAa;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtE,CAAC,eAET9I,OAAA;UACEgJ,KAAK,EAAEjH,eAAgB;UACvBkH,QAAQ,EAAG/H,CAAC,IAAKc,kBAAkB,CAACd,CAAC,CAACgI,MAAM,CAACF,KAAK,CAAE;UACpDlB,SAAS,EAAC,eAAe;UAAAY,QAAA,eAEzB1I,OAAA;YAAQgJ,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAE3F,WAAW,GAAG,aAAa,GAAG;UAAc;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9I,OAAA;MAAK8H,SAAS,EAAC,eAAe;MAAAY,QAAA,EAC3BpH,OAAO,gBACNtB,OAAA;QAAK8H,SAAS,EAAC,eAAe;QAAAY,QAAA,gBAC5B1I,OAAA;UAAK8H,SAAS,EAAC;QAAiB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC9I,OAAA;UAAA0I,QAAA,EAAI3F,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJ3H,KAAK,gBACPnB,OAAA;QAAK8H,SAAS,EAAC,aAAa;QAAAY,QAAA,gBAC1B1I,OAAA,CAACL,eAAe;UAACmI,SAAS,EAAC;QAAY;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1C9I,OAAA;UAAA0I,QAAA,EAAK3F,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7E9I,OAAA;UAAA0I,QAAA,EAAIvH;QAAK;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd9I,OAAA;UAAQmJ,OAAO,EAAEhD,WAAY;UAAC2B,SAAS,EAAC,WAAW;UAAAY,QAAA,EAChD3F,WAAW,GAAG,aAAa,GAAG;QAAW;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJhF,uBAAuB,CAAC2C,MAAM,GAAG,CAAC,gBACpCzG,OAAA;QAAK8H,SAAS,EAAC,aAAa;QAAAY,QAAA,EACzB5E,uBAAuB,CAAC4B,GAAG,CAAC,CAACzC,KAAK,EAAE+C,KAAK;UAAA,IAAAoD,UAAA,EAAAC,iBAAA;UAAA,oBACxCrJ,OAAA;YAAiB8H,SAAS,EAAC,YAAY;YAAAY,QAAA,gBAErC1I,OAAA;cAAK8H,SAAS,EAAC,YAAY;cAACqB,OAAO,EAAEA,CAAA,KAAMpD,eAAe,CAACC,KAAK,CAAE;cAAA0C,QAAA,gBAChE1I,OAAA;gBAAK8H,SAAS,EAAC,sBAAsB;gBAAAY,QAAA,gBACnC1I,OAAA;kBACEsJ,GAAG,EAAEtG,eAAe,CAACC,KAAK,CAAE;kBAC5BsG,GAAG,EAAEtG,KAAK,CAACwE,KAAM;kBACjBK,SAAS,EAAC,iBAAiB;kBAC3BxG,OAAO,EAAC,MAAM;kBACdkI,OAAO,EAAGtI,CAAC,IAAK;oBACd,IAAI+B,KAAK,CAACK,OAAO,IAAI,CAACL,KAAK,CAACK,OAAO,CAACH,QAAQ,CAAC,eAAe,CAAC,EAAE;sBAC7D,IAAIK,OAAO,GAAGP,KAAK,CAACK,OAAO;sBAC3B,MAAMG,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;sBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;sBACpC,MAAMiG,SAAS,GAAG,CACf,8BAA6BjG,OAAQ,oBAAmB,EACxD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,4cAA4c,CAC7c;sBACD,MAAMkG,UAAU,GAAGxI,CAAC,CAACgI,MAAM,CAACI,GAAG;sBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACC,GAAG,IAAIH,UAAU,CAACvG,QAAQ,CAAC0G,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACxG,IAAIH,YAAY,GAAGF,SAAS,CAAChD,MAAM,GAAG,CAAC,EAAE;wBACvCvF,CAAC,CAACgI,MAAM,CAACI,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;sBAC5C;oBACF,CAAC,MAAM;sBACLzI,CAAC,CAACgI,MAAM,CAACI,GAAG,GAAG,4cAA4c;oBAC7d;kBACF;gBAAE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF9I,OAAA;kBAAK8H,SAAS,EAAC,cAAc;kBAAAY,QAAA,eAC3B1I,OAAA,CAACR,YAAY;oBAACsI,SAAS,EAAC;kBAAW;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACN9I,OAAA;kBAAK8H,SAAS,EAAC,gBAAgB;kBAAAY,QAAA,EAC5BzF,KAAK,CAAC+G,QAAQ,IAAI;gBAAO;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EACL7F,KAAK,CAACgH,SAAS,IAAIhH,KAAK,CAACgH,SAAS,CAACxD,MAAM,GAAG,CAAC,iBAC5CzG,OAAA;kBAAK8H,SAAS,EAAC,gBAAgB;kBAAAY,QAAA,gBAC7B1I,OAAA,CAACN,YAAY;oBAAAiJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,MAElB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN9I,OAAA;gBAAK8H,SAAS,EAAC,oBAAoB;gBAAAY,QAAA,gBACjC1I,OAAA;kBAAI8H,SAAS,EAAC,aAAa;kBAAAY,QAAA,EAAEzF,KAAK,CAACwE;gBAAK;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9C9I,OAAA;kBAAK8H,SAAS,EAAC,YAAY;kBAAAY,QAAA,gBACzB1I,OAAA;oBAAM8H,SAAS,EAAC,eAAe;oBAAAY,QAAA,EAAEhF,cAAc,CAACT,KAAK,CAACU,OAAO;kBAAC;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtE9I,OAAA;oBAAM8H,SAAS,EAAC,aAAa;oBAAAY,QAAA,EAC1B/G,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEoB,WAAW,GAAI,aAAYE,KAAK,CAAC6E,SAAS,IAAI7E,KAAK,CAAC8E,KAAM,EAAC,GAAI,SAAQ9E,KAAK,CAAC6E,SAAS,IAAI7E,KAAK,CAAC8E,KAAM,EAAC,GACvG,QAAO9E,KAAK,CAAC6E,SAAS,IAAI7E,KAAK,CAAC8E,KAAM;kBAAC;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN9I,OAAA;kBAAK8H,SAAS,EAAC,YAAY;kBAAAY,QAAA,GACxBzF,KAAK,CAACqF,KAAK,iBAAItI,OAAA;oBAAM8H,SAAS,EAAC,WAAW;oBAAAY,QAAA,EAAEzF,KAAK,CAACqF;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/D7F,KAAK,CAACiH,eAAe,IAAIjH,KAAK,CAACiH,eAAe,MAAMjH,KAAK,CAAC6E,SAAS,IAAI7E,KAAK,CAAC8E,KAAK,CAAC,iBAClF/H,OAAA;oBAAM8H,SAAS,EAAC,YAAY;oBAAAY,QAAA,GACzB3F,WAAW,GAAG,qBAAqB,GAAG,cAAc,EACpDpB,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEoB,WAAW,GAAI,aAAYE,KAAK,CAACiH,eAAgB,EAAC,GAAI,SAAQjH,KAAK,CAACiH,eAAgB,EAAC,GACrF,QAAOjH,KAAK,CAACiH,eAAgB,EAAC;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL7G,iBAAiB,KAAK+D,KAAK,iBAC1BhG,OAAA;cAAK8H,SAAS,EAAC,qBAAqB;cAAAY,QAAA,eAClC1I,OAAA;gBAAK8H,SAAS,EAAC,sBAAsB;gBAAAY,QAAA,gBACnC1I,OAAA;kBAAK8H,SAAS,EAAC,sBAAsB;kBAAAY,QAAA,EAClCzF,KAAK,CAACM,QAAQ,gBACbvD,OAAA;oBACEmK,GAAG,EAAGA,GAAG,IAAK/H,WAAW,CAAC+H,GAAG,CAAE;oBAC/BC,QAAQ;oBACRC,QAAQ;oBACRC,WAAW;oBACXC,OAAO,EAAC,UAAU;oBAClBC,KAAK,EAAC,MAAM;oBACZC,MAAM,EAAC,MAAM;oBACbC,MAAM,EAAE1H,eAAe,CAACC,KAAK,CAAE;oBAC/B0H,KAAK,EAAE;sBACLH,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdG,eAAe,EAAE,MAAM;sBACvBC,SAAS,EAAE;oBACb,CAAE;oBACFrB,OAAO,EAAGtI,CAAC,IAAKoB,aAAa,CAAE,yBAAwBW,KAAK,CAACwE,KAAM,EAAC,CAAE;oBACtEqD,SAAS,EAAEA,CAAA,KAAMxI,aAAa,CAAC,IAAI,CAAE;oBACrCyI,WAAW,EAAC,WAAW;oBAAArC,QAAA,gBAEvB1I,OAAA;sBAAQsJ,GAAG,EAAErG,KAAK,CAACgD,cAAc,IAAIhD,KAAK,CAACM,QAAS;sBAAC0D,IAAI,EAAC;oBAAW;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACvE7F,KAAK,CAACgH,SAAS,IAAIhH,KAAK,CAACgH,SAAS,CAACxD,MAAM,GAAG,CAAC,IAAIxD,KAAK,CAACgH,SAAS,CAACvE,GAAG,CAAC,CAACsF,QAAQ,EAAEhF,KAAK,kBACpFhG,OAAA;sBAEEiL,IAAI,EAAC,WAAW;sBAChB3B,GAAG,EAAE0B,QAAQ,CAACnB,GAAI;sBAClBqB,OAAO,EAAEF,QAAQ,CAACG,QAAS;sBAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;sBAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAIvF,KAAK,KAAK;oBAAE,GALrC,GAAEgF,QAAQ,CAACG,QAAS,IAAGnF,KAAM,EAAC;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,GACN7F,KAAK,CAACK,OAAO,gBACftD,OAAA;oBACEsJ,GAAG,EAAG,iCAAgCrG,KAAK,CAACK,OAAQ,mBAAmB;oBACvEmE,KAAK,EAAExE,KAAK,CAACwE,KAAM;oBACnB+D,WAAW,EAAC,GAAG;oBACfC,eAAe;oBACfd,KAAK,EAAE;sBAAEH,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE,MAAM;sBAAEiB,MAAM,EAAE;oBAAO;kBAAE;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,gBAEV9I,OAAA;oBAAK8H,SAAS,EAAC,aAAa;oBAAAY,QAAA,gBAC1B1I,OAAA;sBAAK8H,SAAS,EAAC,YAAY;sBAAAY,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpC9I,OAAA;sBAAA0I,QAAA,EAAI;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1B9I,OAAA;sBAAA0I,QAAA,EAAIrG,UAAU,IAAI;oBAA4C;sBAAAsG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEN9I,OAAA;kBAAK8H,SAAS,EAAC,oBAAoB;kBAAAY,QAAA,gBACjC1I,OAAA;oBAAI8H,SAAS,EAAC,qBAAqB;oBAAAY,QAAA,EAAEzF,KAAK,CAACwE;kBAAK;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtD9I,OAAA;oBAAK8H,SAAS,EAAC,oBAAoB;oBAAAY,QAAA,gBACjC1I,OAAA;sBAAA0I,QAAA,EAAOhF,cAAc,CAACT,KAAK,CAACU,OAAO;oBAAC;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5C9I,OAAA;sBAAA0I,QAAA,EAAM;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACd9I,OAAA;sBAAA0I,QAAA,GAAM,QAAM,EAACzF,KAAK,CAAC6E,SAAS,IAAI7E,KAAK,CAAC8E,KAAK;oBAAA;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAClD7F,KAAK,CAAC+D,KAAK,iBACVhH,OAAA,CAAAE,SAAA;sBAAAwI,QAAA,gBACE1I,OAAA;wBAAA0I,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACd9I,OAAA;wBAAA0I,QAAA,EAAOzF,KAAK,CAAC+D;sBAAK;wBAAA2B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,eAC1B,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN9I,OAAA;oBAAK8H,SAAS,EAAC,uBAAuB;oBAAAY,QAAA,gBACpC1I,OAAA;sBACE8H,SAAS,EAAG,sBAAqBnF,gBAAgB,GAAG,QAAQ,GAAG,EAAG,EAAE;sBACpEwG,OAAO,EAAEA,CAAA,KAAMvG,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;sBAAA+F,QAAA,gBAEtD1I,OAAA;wBAAA0I,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACf9I,OAAA;wBAAA0I,QAAA,EAAM;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACT9I,OAAA;sBAAQ8H,SAAS,EAAC,oBAAoB;sBAAAY,QAAA,gBACpC1I,OAAA;wBAAA0I,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACf9I,OAAA;wBAAA0I,QAAA,EAAM;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACT9I,OAAA;sBACE8H,SAAS,EAAC,oBAAoB;sBAC9BqB,OAAO,EAAEA,CAAA,KAAMjH,oBAAoB,CAAC,IAAI,CAAE;sBAAAwG,QAAA,gBAE1C1I,OAAA;wBAAA0I,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACd9I,OAAA;wBAAA0I,QAAA,EAAM;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLnG,gBAAgB,iBACf3C,OAAA;kBAAK8H,SAAS,EAAC,0BAA0B;kBAAAY,QAAA,gBACvC1I,OAAA;oBAAK8H,SAAS,EAAC,yBAAyB;oBAAAY,QAAA,eACtC1I,OAAA;sBAAA0I,QAAA,GAAO7E,uBAAuB,CAAC,CAAC,CAAC4C,MAAM,EAAC,WAAS;oBAAA;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC,eAGN9I,OAAA;oBAAK8H,SAAS,EAAC,uBAAuB;oBAAAY,QAAA,gBACpC1I,OAAA;sBAAK8H,SAAS,EAAC,wBAAwB;sBAAAY,QAAA,EACpC,CAAArI,IAAI,aAAJA,IAAI,wBAAA+I,UAAA,GAAJ/I,IAAI,CAAEyE,IAAI,cAAAsE,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYuC,MAAM,CAAC,CAAC,CAAC,cAAAtC,iBAAA,uBAArBA,iBAAA,CAAuBuC,WAAW,CAAC,CAAC,KAAI;oBAAG;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC,eACN9I,OAAA;sBAAK2K,KAAK,EAAE;wBAAEkB,IAAI,EAAE;sBAAE,CAAE;sBAAAnD,QAAA,gBACtB1I,OAAA;wBACE8H,SAAS,EAAC,6BAA6B;wBACvCkB,KAAK,EAAEvG,UAAW;wBAClBwG,QAAQ,EAAG/H,CAAC,IAAKwB,aAAa,CAACxB,CAAC,CAACgI,MAAM,CAACF,KAAK,CAAE;wBAC/CD,WAAW,EAAC,kBAAkB;wBAC9B+C,IAAI,EAAC,GAAG;wBACRnB,KAAK,EAAE;0BACLoB,SAAS,EAAE,MAAM;0BACjBC,MAAM,EAAE,MAAM;0BACdC,QAAQ,EAAE;wBACZ,CAAE;wBACFC,OAAO,EAAGhL,CAAC,IAAK;0BACdA,CAAC,CAACgI,MAAM,CAACyB,KAAK,CAACF,MAAM,GAAG,MAAM;0BAC9BvJ,CAAC,CAACgI,MAAM,CAACyB,KAAK,CAACF,MAAM,GAAGvJ,CAAC,CAACgI,MAAM,CAACiD,YAAY,GAAG,IAAI;wBACtD;sBAAE;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EACDrG,UAAU,CAACgC,IAAI,CAAC,CAAC,iBAChBzE,OAAA;wBAAK8H,SAAS,EAAC,yBAAyB;wBAAAY,QAAA,gBACtC1I,OAAA;0BACE8H,SAAS,EAAC,4BAA4B;0BACtCqB,OAAO,EAAEA,CAAA,KAAMzG,aAAa,CAAC,EAAE,CAAE;0BAAAgG,QAAA,EAClC;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACT9I,OAAA;0BACE8H,SAAS,EAAC,4BAA4B;0BACtCqB,OAAO,EAAE3E,gBAAiB;0BAC1B4H,QAAQ,EAAE,CAAC3J,UAAU,CAACgC,IAAI,CAAC,CAAE;0BAAAiE,QAAA,EAC9B;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN9I,OAAA;oBAAK8H,SAAS,EAAC,uBAAuB;oBAAAY,QAAA,EACnC7E,uBAAuB,CAAC,CAAC,CAAC4C,MAAM,KAAK,CAAC,gBACrCzG,OAAA;sBAAK2K,KAAK,EAAE;wBAAE0B,SAAS,EAAE,QAAQ;wBAAEC,OAAO,EAAE,QAAQ;wBAAEC,KAAK,EAAE;sBAAU,CAAE;sBAAA7D,QAAA,gBACvE1I,OAAA;wBAAK2K,KAAK,EAAE;0BAAE6B,QAAQ,EAAE,MAAM;0BAAEC,YAAY,EAAE;wBAAO,CAAE;wBAAA/D,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChE9I,OAAA;wBAAA0I,QAAA,EAAG;sBAAqD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,GAENjF,uBAAuB,CAAC,CAAC,CAAC6B,GAAG,CAAEhB,OAAO;sBAAA,IAAAgI,eAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,iBAAA;sBAAA,oBACpC7M,OAAA;wBAAqC8H,SAAS,EAAC,iBAAiB;wBAAAY,QAAA,gBAC9D1I,OAAA;0BAAK8H,SAAS,EAAC,wBAAwB;0BAAAY,QAAA,EACpChE,OAAO,CAACoI,MAAM,MAAAJ,eAAA,GAAIhI,OAAO,CAACG,MAAM,cAAA6H,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBf,MAAM,CAAC,CAAC,CAAC,cAAAgB,qBAAA,uBAAzBA,qBAAA,CAA2Bf,WAAW,CAAC,CAAC,KAAI;wBAAG;0BAAAjD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D,CAAC,eACN9I,OAAA;0BAAK8H,SAAS,EAAC,yBAAyB;0BAAAY,QAAA,gBACtC1I,OAAA;4BAAK8H,SAAS,EAAC,wBAAwB;4BAAAY,QAAA,gBACrC1I,OAAA;8BAAM8H,SAAS,EAAC,wBAAwB;8BAAAY,QAAA,EAAEhE,OAAO,CAACG;4BAAM;8BAAA8D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,EAC/D,CAACpE,OAAO,CAACM,QAAQ,KAAK,OAAO,IAAIN,OAAO,CAACQ,OAAO,kBAC/ClF,OAAA,CAACJ,UAAU;8BAAC+K,KAAK,EAAE;gCAAE4B,KAAK,EAAE,SAAS;gCAAEC,QAAQ,EAAE,MAAM;gCAAEO,UAAU,EAAE;8BAAM,CAAE;8BAACtF,KAAK,EAAC;4BAAgB;8BAAAkB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CACvG,eACD9I,OAAA;8BAAM8H,SAAS,EAAC,sBAAsB;8BAAAY,QAAA,EACnC1E,aAAa,CAACU,OAAO,CAACU,SAAS,IAAIV,OAAO,CAACT,SAAS;4BAAC;8BAAA0E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAClD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eACN9I,OAAA;4BAAK8H,SAAS,EAAC,sBAAsB;4BAAAY,QAAA,EAClChE,OAAO,CAACE;0BAAI;4BAAA+D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN9I,OAAA;4BAAK8H,SAAS,EAAC,yBAAyB;4BAAAY,QAAA,gBACtC1I,OAAA;8BACEmJ,OAAO,EAAEA,CAAA,KAAM3D,iBAAiB,CAACd,OAAO,CAACK,GAAG,IAAIL,OAAO,CAACX,EAAE,CAAE;8BAC5D+D,SAAS,EAAG,0BAAyB,CAAA8E,iBAAA,GAAAlI,OAAO,CAACY,OAAO,cAAAsH,iBAAA,eAAfA,iBAAA,CAAiBzJ,QAAQ,CAAC9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,GAAG,CAAC,GAAG,OAAO,GAAG,EAAG,EAAE;8BAAA2D,QAAA,gBAE3F1I,OAAA;gCAAA0I,QAAA,EAAO,CAAAmE,iBAAA,GAAAnI,OAAO,CAACY,OAAO,cAAAuH,iBAAA,eAAfA,iBAAA,CAAiB1J,QAAQ,CAAC9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,GAAG,CAAC,GAAG,IAAI,GAAG;8BAAI;gCAAA4D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,EAChEpE,OAAO,CAACW,KAAK,GAAG,CAAC,iBAAIrF,OAAA;gCAAA0I,QAAA,EAAOhE,OAAO,CAACW;8BAAK;gCAAAsD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5C,CAAC,eACT9I,OAAA;8BAAQ8H,SAAS,EAAC,wBAAwB;8BAAAY,QAAA,eACxC1I,OAAA;gCAAA0I,QAAA,EAAM;8BAAE;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC,eACT9I,OAAA;8BAAQ8H,SAAS,EAAC,wBAAwB;8BAAAY,QAAA,EAAC;4BAE3C;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,EACRpE,OAAO,CAACrE,IAAI,MAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,GAAG,kBACzB/E,OAAA,CAAAE,SAAA;8BAAAwI,QAAA,gBACE1I,OAAA;gCAAQ8H,SAAS,EAAC,wBAAwB;gCAAAY,QAAA,EAAC;8BAE3C;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eACT9I,OAAA;gCACE8H,SAAS,EAAC,wBAAwB;gCAClCqB,OAAO,EAAEA,CAAA,KAAM;kCACb,IAAI6D,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;oCACnEnH,mBAAmB,CAACpB,OAAO,CAACK,GAAG,IAAIL,OAAO,CAACX,EAAE,CAAC;kCAChD;gCACF,CAAE;gCAAA2E,QAAA,EACH;8BAED;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC;4BAAA,eACT,CACH;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA,GAjDEpE,OAAO,CAACK,GAAG,IAAIL,OAAO,CAACX,EAAE;wBAAA4E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAkD9B,CAAC;oBAAA,CACP;kBACF;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA,GAnRO9C,KAAK;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoRV,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEN9I,OAAA;QAAK8H,SAAS,EAAC,aAAa;QAAAY,QAAA,gBAC1B1I,OAAA,CAACP,eAAe;UAACqI,SAAS,EAAC;QAAY;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1C9I,OAAA;UAAA0I,QAAA,EAAK3F,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1E9I,OAAA;UAAA0I,QAAA,EAAI3F,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJ9I,OAAA;UAAG8H,SAAS,EAAC,YAAY;UAAAY,QAAA,EAAE3F,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1I,EAAA,CApsBID,YAAY;EAAA,QAEHZ,WAAW;AAAA;AAAA2N,EAAA,GAFpB/M,YAAY;AAssBlB,eAAeA,YAAY;AAAC,IAAA+M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}