{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial } from '../../../apicalls/study';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoLessons = () => {\n  _s();\n  // Redux state with safe destructuring\n  const usersState = useSelector(state => {\n    console.log('Redux state:', state); // Debug log\n    return state.users || {};\n  });\n  const user = usersState.user || null;\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Debug user state\n  console.log('User state:', user);\n\n  // Helper functions\n  const getThumbnailUrl = video => {\n    if (video.thumbnailUrl) return video.thumbnailUrl;\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      var _video$videoID$match;\n      const videoId = video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be') ? ((_video$videoID$match = video.videoID.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/)) === null || _video$videoID$match === void 0 ? void 0 : _video$videoID$match[1]) || video.videoID : video.videoID;\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/320/180';\n  };\n  const getSubjectName = subject => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      console.log('Fetching videos with filters:', {\n        level: selectedLevel,\n        class: selectedClass !== 'all' ? selectedClass : undefined,\n        subject: selectedSubject !== 'all' ? selectedSubject : undefined,\n        type: 'video'\n      });\n\n      // Use the proper API call for study materials (videos)\n      const filters = {\n        level: selectedLevel,\n        class: selectedClass !== 'all' ? selectedClass : undefined,\n        subject: selectedSubject !== 'all' ? selectedSubject : undefined,\n        type: 'video' // Filter for videos only\n      };\n\n      const response = await getStudyMaterial(filters);\n      console.log('API Response:', response);\n      if (response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        var _response$data$data;\n        setVideos(response.data.data || []);\n        console.log('Videos loaded:', ((_response$data$data = response.data.data) === null || _response$data$data === void 0 ? void 0 : _response$data$data.length) || 0);\n      } else {\n        console.error('API Error:', response);\n        // For now, let's use mock data to test the UI\n        const mockVideos = [{\n          id: '1',\n          title: 'Introduction to Mathematics',\n          subject: 'mathematics',\n          className: '1',\n          level: 'primary',\n          videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',\n          thumbnailUrl: '/api/placeholder/320/180',\n          duration: '10:30',\n          topic: 'Numbers',\n          subtitles: []\n        }, {\n          id: '2',\n          title: 'Basic English Grammar',\n          subject: 'english',\n          className: '2',\n          level: 'primary',\n          videoID: 'dQw4w9WgXcQ',\n          // YouTube video ID\n          duration: '15:45',\n          topic: 'Grammar',\n          subtitles: [{\n            language: 'en',\n            languageName: 'English',\n            url: '',\n            isDefault: true\n          }]\n        }];\n        setVideos(mockVideos);\n        console.log('Using mock videos due to API error');\n      }\n    } catch (err) {\n      console.error('Fetch error:', err);\n      setError(err.message || 'Failed to load videos');\n\n      // Use mock data as fallback\n      const mockVideos = [{\n        id: '1',\n        title: 'Sample Video Lesson',\n        subject: 'mathematics',\n        className: '1',\n        level: 'primary',\n        videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',\n        thumbnailUrl: '/api/placeholder/320/180',\n        duration: '10:30',\n        topic: 'Sample Topic',\n        subtitles: []\n      }];\n      setVideos(mockVideos);\n      console.log('Using fallback mock videos');\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      var _video$title, _video$subject, _video$topic;\n      const matchesSearch = !searchTerm || ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isKiswahili ? 'Masomo ya Video' : 'Video Lessons'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: isKiswahili ? 'Tafuta video...' : 'Search videos...',\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLevel,\n          onChange: e => setSelectedLevel(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"primary\",\n            children: isKiswahili ? 'Msingi' : 'Primary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"secondary\",\n            children: isKiswahili ? 'Sekondari' : 'Secondary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advance\",\n            children: isKiswahili ? 'Juu' : 'Advanced'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedClass,\n          onChange: e => setSelectedClass(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedSubject,\n          onChange: e => setSelectedSubject(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card\",\n            onClick: () => handleShowVideo(index),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card-thumbnail\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getThumbnailUrl(video),\n                alt: video.title,\n                className: \"thumbnail-image\",\n                loading: \"lazy\",\n                onError: e => {\n                  if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                    let videoId = video.videoID;\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                    const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                    const currentSrc = e.target.src;\n                    const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                    if (currentIndex < fallbacks.length - 1) {\n                      e.target.src = fallbacks[currentIndex + 1];\n                    } else {\n                      e.target.src = '/api/placeholder/320/180';\n                    }\n                  } else {\n                    e.target.src = '/api/placeholder/320/180';\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"play-overlay\",\n                children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                  className: \"play-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-duration\",\n                children: video.duration || \"Video\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 21\n              }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"subtitle-badge\",\n                children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 25\n                }, this), \"CC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"video-title\",\n                children: video.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-subject\",\n                  children: getSubjectName(video.subject)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-class\",\n                  children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-tags\",\n                children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"topic-tag\",\n                  children: video.topic\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 39\n                }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"shared-tag\",\n                  children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 17\n          }, this), currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-video-player\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"youtube-style-layout\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-player\",\n                children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                  ref: ref => setVideoRef(ref),\n                  controls: true,\n                  autoPlay: true,\n                  playsInline: true,\n                  preload: \"metadata\",\n                  width: \"100%\",\n                  height: \"100%\",\n                  poster: getThumbnailUrl(video),\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    backgroundColor: '#000',\n                    objectFit: 'contain'\n                  },\n                  onError: e => setVideoError(`Failed to load video: ${video.title}`),\n                  onCanPlay: () => setVideoError(null),\n                  crossOrigin: \"anonymous\",\n                  children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                    src: video.signedVideoUrl || video.videoUrl,\n                    type: \"video/mp4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 29\n                  }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                    kind: \"subtitles\",\n                    src: subtitle.url,\n                    srcLang: subtitle.language,\n                    label: subtitle.languageName,\n                    default: subtitle.isDefault || index === 0\n                  }, `${subtitle.language}-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 31\n                  }, this)), \"Your browser does not support the video tag.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 27\n                }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                  src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                  title: video.title,\n                  frameBorder: \"0\",\n                  allowFullScreen: true,\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    border: 'none'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-icon\",\n                    children: \"\\u26A0\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: \"Video Unavailable\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: videoError || \"This video cannot be played at the moment.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"youtube-video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u2022\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Class \", video.className || video.class]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-actions\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"youtube-action-btn\",\n                    onClick: () => setCurrentVideoIndex(null),\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2715\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Close\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 19\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 197,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoLessons, \"r2SMMcgtsI2PFivZCCs/s04bnno=\", false, function () {\n  return [useSelector];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useSelector", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "TbAlertTriangle", "MdVerified", "getStudyMaterial", "jsxDEV", "_jsxDEV", "VideoLessons", "_s", "usersState", "state", "console", "log", "users", "user", "videos", "setVideos", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "currentVideoIndex", "setCurrentVideoIndex", "videoRef", "setVideoRef", "videoError", "setVideoError", "isKiswahili", "getThumbnailUrl", "video", "thumbnailUrl", "videoID", "includes", "_video$videoID$match", "videoId", "match", "getSubjectName", "subject", "subjectMap", "handleShowVideo", "index", "filteredAndSortedVideos", "videoUrl", "signedVideoUrl", "warn", "fetchVideos", "_response$data", "level", "class", "undefined", "type", "filters", "response", "data", "success", "_response$data$data", "length", "mockVideos", "id", "title", "className", "duration", "topic", "subtitles", "language", "languageName", "url", "isDefault", "err", "message", "filtered", "filter", "_video$title", "_video$subject", "_video$topic", "matchesSearch", "toLowerCase", "matchesLevel", "matchesClass", "matchesSubject", "sort", "a", "b", "Date", "createdAt", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "e", "target", "onClick", "map", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "split", "pop", "sharedFromClass", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "style", "backgroundColor", "objectFit", "onCanPlay", "crossOrigin", "subtitle", "kind", "srcLang", "label", "default", "frameBorder", "allowFullScreen", "border", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial } from '../../../apicalls/study';\nimport './index.css';\n\nconst VideoLessons = () => {\n  // Redux state with safe destructuring\n  const usersState = useSelector(state => {\n    console.log('Redux state:', state); // Debug log\n    return state.users || {};\n  });\n  const user = usersState.user || null;\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Debug user state\n  console.log('User state:', user);\n\n  // Helper functions\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnailUrl) return video.thumbnailUrl;\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      const videoId = video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be')\n        ? video.videoID.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/)?.[1] || video.videoID\n        : video.videoID;\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/320/180';\n  };\n\n  const getSubjectName = (subject) => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('Fetching videos with filters:', {\n        level: selectedLevel,\n        class: selectedClass !== 'all' ? selectedClass : undefined,\n        subject: selectedSubject !== 'all' ? selectedSubject : undefined,\n        type: 'video'\n      });\n\n      // Use the proper API call for study materials (videos)\n      const filters = {\n        level: selectedLevel,\n        class: selectedClass !== 'all' ? selectedClass : undefined,\n        subject: selectedSubject !== 'all' ? selectedSubject : undefined,\n        type: 'video' // Filter for videos only\n      };\n\n      const response = await getStudyMaterial(filters);\n      console.log('API Response:', response);\n\n      if (response?.data?.success) {\n        setVideos(response.data.data || []);\n        console.log('Videos loaded:', response.data.data?.length || 0);\n      } else {\n        console.error('API Error:', response);\n        // For now, let's use mock data to test the UI\n        const mockVideos = [\n          {\n            id: '1',\n            title: 'Introduction to Mathematics',\n            subject: 'mathematics',\n            className: '1',\n            level: 'primary',\n            videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            thumbnailUrl: '/api/placeholder/320/180',\n            duration: '10:30',\n            topic: 'Numbers',\n            subtitles: []\n          },\n          {\n            id: '2',\n            title: 'Basic English Grammar',\n            subject: 'english',\n            className: '2',\n            level: 'primary',\n            videoID: 'dQw4w9WgXcQ', // YouTube video ID\n            duration: '15:45',\n            topic: 'Grammar',\n            subtitles: [{ language: 'en', languageName: 'English', url: '', isDefault: true }]\n          }\n        ];\n        setVideos(mockVideos);\n        console.log('Using mock videos due to API error');\n      }\n    } catch (err) {\n      console.error('Fetch error:', err);\n      setError(err.message || 'Failed to load videos');\n\n      // Use mock data as fallback\n      const mockVideos = [\n        {\n          id: '1',\n          title: 'Sample Video Lesson',\n          subject: 'mathematics',\n          className: '1',\n          level: 'primary',\n          videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',\n          thumbnailUrl: '/api/placeholder/320/180',\n          duration: '10:30',\n          topic: 'Sample Topic',\n          subtitles: []\n        }\n      ];\n      setVideos(mockVideos);\n      console.log('Using fallback mock videos');\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      const matchesSearch = !searchTerm ||\n        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());\n\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n\n  return (\n    <div className=\"video-lessons-container\">\n      <div className=\"video-lessons-header\">\n        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>\n        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"video-controls\">\n        <div className=\"search-section\">\n          <input\n            type=\"text\"\n            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n\n        <div className=\"filter-section\">\n          <select\n            value={selectedLevel}\n            onChange={(e) => setSelectedLevel(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"primary\">{isKiswahili ? 'Msingi' : 'Primary'}</option>\n            <option value=\"secondary\">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>\n            <option value=\"advance\">{isKiswahili ? 'Juu' : 'Advanced'}</option>\n          </select>\n\n          <select\n            value={selectedClass}\n            onChange={(e) => setSelectedClass(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n            {/* Add class options based on selected level */}\n          </select>\n\n          <select\n            value={selectedSubject}\n            onChange={(e) => setSelectedSubject(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n            {/* Add subject options */}\n          </select>\n        </div>\n      </div>\n\n      {/* Video Content */}\n      <div className=\"video-content\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-item\">\n                {/* Video Card */}\n                <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                  <div className=\"video-card-thumbnail\">\n                    <img\n                      src={getThumbnailUrl(video)}\n                      alt={video.title}\n                      className=\"thumbnail-image\"\n                      loading=\"lazy\"\n                      onError={(e) => {\n                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                          let videoId = video.videoID;\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                          const fallbacks = [\n                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                            '/api/placeholder/320/180'\n                          ];\n                          const currentSrc = e.target.src;\n                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                          if (currentIndex < fallbacks.length - 1) {\n                            e.target.src = fallbacks[currentIndex + 1];\n                          } else {\n                            e.target.src = '/api/placeholder/320/180';\n                          }\n                        } else {\n                          e.target.src = '/api/placeholder/320/180';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                    </div>\n                    <div className=\"video-duration\">\n                      {video.duration || \"Video\"}\n                    </div>\n                    {video.subtitles && video.subtitles.length > 0 && (\n                      <div className=\"subtitle-badge\">\n                        <TbInfoCircle />\n                        CC\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"video-card-content\">\n                    <h3 className=\"video-title\">{video.title}</h3>\n                    <div className=\"video-meta\">\n                      <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                      <span className=\"video-class\">\n                        {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                          ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)\n                          : `Form ${video.className || video.class}`}\n                      </span>\n                    </div>\n                    <div className=\"video-tags\">\n                      {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                      {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                        <span className=\"shared-tag\">\n                          {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}\n                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                            ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)\n                            : `Form ${video.sharedFromClass}`}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Inline Video Player */}\n                {currentVideoIndex === index && (\n                  <div className=\"inline-video-player\">\n                    <div className=\"youtube-style-layout\">\n                      <div className=\"youtube-video-player\">\n                        {video.videoUrl ? (\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"100%\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            }}\n                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}\n                            onCanPlay={() => setVideoError(null)}\n                            crossOrigin=\"anonymous\"\n                          >\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n                            Your browser does not support the video tag.\n                          </video>\n                        ) : video.videoID ? (\n                          <iframe\n                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                            title={video.title}\n                            frameBorder=\"0\"\n                            allowFullScreen\n                            style={{ width: '100%', height: '100%', border: 'none' }}\n                          ></iframe>\n                        ) : (\n                          <div className=\"video-error\">\n                            <div className=\"error-icon\">⚠️</div>\n                            <h3>Video Unavailable</h3>\n                            <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"youtube-video-info\">\n                        <h1 className=\"youtube-video-title\">{video.title}</h1>\n                        <div className=\"youtube-video-meta\">\n                          <span>{getSubjectName(video.subject)}</span>\n                          <span>•</span>\n                          <span>Class {video.className || video.class}</span>\n                        </div>\n                        <div className=\"youtube-video-actions\">\n                          <button\n                            className=\"youtube-action-btn\"\n                            onClick={() => setCurrentVideoIndex(null)}\n                          >\n                            <span>✕</span>\n                            <span>Close</span>\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAMC,UAAU,GAAGX,WAAW,CAACY,KAAK,IAAI;IACtCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,KAAK,CAAC,CAAC,CAAC;IACpC,OAAOA,KAAK,CAACG,KAAK,IAAI,CAAC,CAAC;EAC1B,CAAC,CAAC;EACF,MAAMC,IAAI,GAAGL,UAAU,CAACK,IAAI,IAAI,IAAI;;EAEpC;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACqC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAMyC,WAAW,GAAGZ,aAAa,KAAK,mBAAmB;;EAEzD;EACAZ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEE,IAAI,CAAC;;EAEhC;EACA,MAAMsB,eAAe,GAAIC,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACC,YAAY,EAAE,OAAOD,KAAK,CAACC,YAAY;IACjD,IAAID,KAAK,CAACE,OAAO,IAAI,CAACF,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;MAAA,IAAAC,oBAAA;MAC7D,MAAMC,OAAO,GAAGL,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIH,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,UAAU,CAAC,GACvF,EAAAC,oBAAA,GAAAJ,KAAK,CAACE,OAAO,CAACI,KAAK,CAAC,oDAAoD,CAAC,cAAAF,oBAAA,uBAAzEA,oBAAA,CAA4E,CAAC,CAAC,KAAIJ,KAAK,CAACE,OAAO,GAC/FF,KAAK,CAACE,OAAO;MACjB,OAAQ,8BAA6BG,OAAQ,oBAAmB;IAClE;IACA,OAAO,0BAA0B;EACnC,CAAC;EAED,MAAME,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,UAAU,GAAG;MACjB,aAAa,EAAEX,WAAW,GAAG,UAAU,GAAG,aAAa;MACvD,SAAS,EAAEA,WAAW,GAAG,YAAY,GAAG,SAAS;MACjD,WAAW,EAAE,WAAW;MACxB,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG,SAAS;MAC9C,gBAAgB,EAAEA,WAAW,GAAG,kBAAkB,GAAG,gBAAgB;MACrE,QAAQ,EAAEA,WAAW,GAAG,OAAO,GAAG,QAAQ;MAC1C,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,WAAW,GAAG,WAAW;MACpD,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,OAAO,GAAG,WAAW;MAChD,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG;IACvC,CAAC;IACD,OAAOW,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO;EACvC,CAAC;;EAED;EACA,MAAME,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMX,KAAK,GAAGY,uBAAuB,CAACD,KAAK,CAAC;IAC5ClB,oBAAoB,CAACkB,KAAK,CAAC;IAC3Bd,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIG,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEa,QAAQ,KAAKb,KAAK,CAACa,QAAQ,CAACV,QAAQ,CAAC,eAAe,CAAC,IAAIH,KAAK,CAACa,QAAQ,CAACV,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF;QACA;QACA;QACAH,KAAK,CAACc,cAAc,GAAGd,KAAK,CAACa,QAAQ;MACvC,CAAC,CAAC,OAAO/B,KAAK,EAAE;QACdR,OAAO,CAACyC,IAAI,CAAC,8CAA8C,CAAC;QAC5Df,KAAK,CAACc,cAAc,GAAGd,KAAK,CAACa,QAAQ;MACvC;IACF;EACF,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGxD,WAAW,CAAC,YAAY;IAC1C,IAAI;MAAA,IAAAyD,cAAA;MACFpC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEdT,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3C2C,KAAK,EAAEhC,aAAa;QACpBiC,KAAK,EAAE/B,aAAa,KAAK,KAAK,GAAGA,aAAa,GAAGgC,SAAS;QAC1DZ,OAAO,EAAElB,eAAe,KAAK,KAAK,GAAGA,eAAe,GAAG8B,SAAS;QAChEC,IAAI,EAAE;MACR,CAAC,CAAC;;MAEF;MACA,MAAMC,OAAO,GAAG;QACdJ,KAAK,EAAEhC,aAAa;QACpBiC,KAAK,EAAE/B,aAAa,KAAK,KAAK,GAAGA,aAAa,GAAGgC,SAAS;QAC1DZ,OAAO,EAAElB,eAAe,KAAK,KAAK,GAAGA,eAAe,GAAG8B,SAAS;QAChEC,IAAI,EAAE,OAAO,CAAC;MAChB,CAAC;;MAED,MAAME,QAAQ,GAAG,MAAMxD,gBAAgB,CAACuD,OAAO,CAAC;MAChDhD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgD,QAAQ,CAAC;MAEtC,IAAIA,QAAQ,aAARA,QAAQ,gBAAAN,cAAA,GAARM,QAAQ,CAAEC,IAAI,cAAAP,cAAA,eAAdA,cAAA,CAAgBQ,OAAO,EAAE;QAAA,IAAAC,mBAAA;QAC3B/C,SAAS,CAAC4C,QAAQ,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QACnClD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,EAAAmD,mBAAA,GAAAH,QAAQ,CAACC,IAAI,CAACA,IAAI,cAAAE,mBAAA,uBAAlBA,mBAAA,CAAoBC,MAAM,KAAI,CAAC,CAAC;MAChE,CAAC,MAAM;QACLrD,OAAO,CAACQ,KAAK,CAAC,YAAY,EAAEyC,QAAQ,CAAC;QACrC;QACA,MAAMK,UAAU,GAAG,CACjB;UACEC,EAAE,EAAE,GAAG;UACPC,KAAK,EAAE,6BAA6B;UACpCtB,OAAO,EAAE,aAAa;UACtBuB,SAAS,EAAE,GAAG;UACdb,KAAK,EAAE,SAAS;UAChBL,QAAQ,EAAE,4CAA4C;UACtDZ,YAAY,EAAE,0BAA0B;UACxC+B,QAAQ,EAAE,OAAO;UACjBC,KAAK,EAAE,SAAS;UAChBC,SAAS,EAAE;QACb,CAAC,EACD;UACEL,EAAE,EAAE,GAAG;UACPC,KAAK,EAAE,uBAAuB;UAC9BtB,OAAO,EAAE,SAAS;UAClBuB,SAAS,EAAE,GAAG;UACdb,KAAK,EAAE,SAAS;UAChBhB,OAAO,EAAE,aAAa;UAAE;UACxB8B,QAAQ,EAAE,OAAO;UACjBC,KAAK,EAAE,SAAS;UAChBC,SAAS,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,YAAY,EAAE,SAAS;YAAEC,GAAG,EAAE,EAAE;YAAEC,SAAS,EAAE;UAAK,CAAC;QACnF,CAAC,CACF;QACD3D,SAAS,CAACiD,UAAU,CAAC;QACrBtD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACnD;IACF,CAAC,CAAC,OAAOgE,GAAG,EAAE;MACZjE,OAAO,CAACQ,KAAK,CAAC,cAAc,EAAEyD,GAAG,CAAC;MAClCxD,QAAQ,CAACwD,GAAG,CAACC,OAAO,IAAI,uBAAuB,CAAC;;MAEhD;MACA,MAAMZ,UAAU,GAAG,CACjB;QACEC,EAAE,EAAE,GAAG;QACPC,KAAK,EAAE,qBAAqB;QAC5BtB,OAAO,EAAE,aAAa;QACtBuB,SAAS,EAAE,GAAG;QACdb,KAAK,EAAE,SAAS;QAChBL,QAAQ,EAAE,4CAA4C;QACtDZ,YAAY,EAAE,0BAA0B;QACxC+B,QAAQ,EAAE,OAAO;QACjBC,KAAK,EAAE,cAAc;QACrBC,SAAS,EAAE;MACb,CAAC,CACF;MACDvD,SAAS,CAACiD,UAAU,CAAC;MACrBtD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IAC3C,CAAC,SAAS;MACRM,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACK,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEnD;EACA,MAAMsB,uBAAuB,GAAGrD,OAAO,CAAC,MAAM;IAC5C,IAAIkF,QAAQ,GAAG/D,MAAM,CAACgE,MAAM,CAAC1C,KAAK,IAAI;MAAA,IAAA2C,YAAA,EAAAC,cAAA,EAAAC,YAAA;MACpC,MAAMC,aAAa,GAAG,CAAC9D,UAAU,MAAA2D,YAAA,GAC/B3C,KAAK,CAAC8B,KAAK,cAAAa,YAAA,uBAAXA,YAAA,CAAaI,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAACnB,UAAU,CAAC+D,WAAW,CAAC,CAAC,CAAC,OAAAH,cAAA,GAC7D5C,KAAK,CAACQ,OAAO,cAAAoC,cAAA,uBAAbA,cAAA,CAAeG,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAACnB,UAAU,CAAC+D,WAAW,CAAC,CAAC,CAAC,OAAAF,YAAA,GAC/D7C,KAAK,CAACiC,KAAK,cAAAY,YAAA,uBAAXA,YAAA,CAAaE,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAACnB,UAAU,CAAC+D,WAAW,CAAC,CAAC,CAAC;MAE/D,MAAMC,YAAY,GAAG9D,aAAa,KAAK,KAAK,IAAIc,KAAK,CAACkB,KAAK,KAAKhC,aAAa;MAC7E,MAAM+D,YAAY,GAAG7D,aAAa,KAAK,KAAK,IAAIY,KAAK,CAAC+B,SAAS,KAAK3C,aAAa,IAAIY,KAAK,CAACmB,KAAK,KAAK/B,aAAa;MAClH,MAAM8D,cAAc,GAAG5D,eAAe,KAAK,KAAK,IAAIU,KAAK,CAACQ,OAAO,KAAKlB,eAAe;MAErF,OAAOwD,aAAa,IAAIE,YAAY,IAAIC,YAAY,IAAIC,cAAc;IACxE,CAAC,CAAC;IAEF,OAAOT,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAAC;EAC/E,CAAC,EAAE,CAAC7E,MAAM,EAAEM,UAAU,EAAEE,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEvE;EACAhC,SAAS,CAAC,MAAM;IACd0D,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA1D,SAAS,CAAC,MAAM;IACd0D,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAC9B,aAAa,EAAEE,aAAa,EAAEE,eAAe,EAAE0B,WAAW,CAAC,CAAC;EAEhE,oBACE/C,OAAA;IAAK8D,SAAS,EAAC,yBAAyB;IAAAyB,QAAA,gBACtCvF,OAAA;MAAK8D,SAAS,EAAC,sBAAsB;MAAAyB,QAAA,gBACnCvF,OAAA;QAAAuF,QAAA,EAAK1D,WAAW,GAAG,iBAAiB,GAAG;MAAe;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5D3F,OAAA;QAAAuF,QAAA,EAAI1D,WAAW,GAAG,iCAAiC,GAAG;MAAoC;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAGN3F,OAAA;MAAK8D,SAAS,EAAC,gBAAgB;MAAAyB,QAAA,gBAC7BvF,OAAA;QAAK8D,SAAS,EAAC,gBAAgB;QAAAyB,QAAA,eAC7BvF,OAAA;UACEoD,IAAI,EAAC,MAAM;UACXwC,WAAW,EAAE/D,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;UAClEgE,KAAK,EAAE9E,UAAW;UAClB+E,QAAQ,EAAGC,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/C/B,SAAS,EAAC;QAAc;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN3F,OAAA;QAAK8D,SAAS,EAAC,gBAAgB;QAAAyB,QAAA,gBAC7BvF,OAAA;UACE6F,KAAK,EAAE5E,aAAc;UACrB6E,QAAQ,EAAGC,CAAC,IAAK7E,gBAAgB,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAClD/B,SAAS,EAAC,eAAe;UAAAyB,QAAA,gBAEzBvF,OAAA;YAAQ6F,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAE1D,WAAW,GAAG,QAAQ,GAAG;UAAS;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrE3F,OAAA;YAAQ6F,KAAK,EAAC,WAAW;YAAAN,QAAA,EAAE1D,WAAW,GAAG,WAAW,GAAG;UAAW;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5E3F,OAAA;YAAQ6F,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAE1D,WAAW,GAAG,KAAK,GAAG;UAAU;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAET3F,OAAA;UACE6F,KAAK,EAAE1E,aAAc;UACrB2E,QAAQ,EAAGC,CAAC,IAAK3E,gBAAgB,CAAC2E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAClD/B,SAAS,EAAC,eAAe;UAAAyB,QAAA,eAEzBvF,OAAA;YAAQ6F,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAE1D,WAAW,GAAG,eAAe,GAAG;UAAa;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtE,CAAC,eAET3F,OAAA;UACE6F,KAAK,EAAExE,eAAgB;UACvByE,QAAQ,EAAGC,CAAC,IAAKzE,kBAAkB,CAACyE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpD/B,SAAS,EAAC,eAAe;UAAAyB,QAAA,eAEzBvF,OAAA;YAAQ6F,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAE1D,WAAW,GAAG,aAAa,GAAG;UAAc;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3F,OAAA;MAAK8D,SAAS,EAAC,eAAe;MAAAyB,QAAA,EAC3B5E,OAAO,gBACNX,OAAA;QAAK8D,SAAS,EAAC,eAAe;QAAAyB,QAAA,gBAC5BvF,OAAA;UAAK8D,SAAS,EAAC;QAAiB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC3F,OAAA;UAAAuF,QAAA,EAAI1D,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJ9E,KAAK,gBACPb,OAAA;QAAK8D,SAAS,EAAC,aAAa;QAAAyB,QAAA,gBAC1BvF,OAAA,CAACJ,eAAe;UAACkE,SAAS,EAAC;QAAY;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1C3F,OAAA;UAAAuF,QAAA,EAAK1D,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7E3F,OAAA;UAAAuF,QAAA,EAAI1E;QAAK;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd3F,OAAA;UAAQiG,OAAO,EAAElD,WAAY;UAACe,SAAS,EAAC,WAAW;UAAAyB,QAAA,EAChD1D,WAAW,GAAG,aAAa,GAAG;QAAW;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJhD,uBAAuB,CAACe,MAAM,GAAG,CAAC,gBACpC1D,OAAA;QAAK8D,SAAS,EAAC,aAAa;QAAAyB,QAAA,EACzB5C,uBAAuB,CAACuD,GAAG,CAAC,CAACnE,KAAK,EAAEW,KAAK,kBACxC1C,OAAA;UAAiB8D,SAAS,EAAC,YAAY;UAAAyB,QAAA,gBAErCvF,OAAA;YAAK8D,SAAS,EAAC,YAAY;YAACmC,OAAO,EAAEA,CAAA,KAAMxD,eAAe,CAACC,KAAK,CAAE;YAAA6C,QAAA,gBAChEvF,OAAA;cAAK8D,SAAS,EAAC,sBAAsB;cAAAyB,QAAA,gBACnCvF,OAAA;gBACEmG,GAAG,EAAErE,eAAe,CAACC,KAAK,CAAE;gBAC5BqE,GAAG,EAAErE,KAAK,CAAC8B,KAAM;gBACjBC,SAAS,EAAC,iBAAiB;gBAC3BnD,OAAO,EAAC,MAAM;gBACd0F,OAAO,EAAGN,CAAC,IAAK;kBACd,IAAIhE,KAAK,CAACE,OAAO,IAAI,CAACF,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;oBAC7D,IAAIE,OAAO,GAAGL,KAAK,CAACE,OAAO;oBAC3B,MAAMI,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;oBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;oBACpC,MAAMkE,SAAS,GAAG,CACf,8BAA6BlE,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;oBACD,MAAMmE,UAAU,GAAGR,CAAC,CAACC,MAAM,CAACG,GAAG;oBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACrC,GAAG,IAAImC,UAAU,CAACrE,QAAQ,CAACkC,GAAG,CAACsC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC1F,IAAIH,YAAY,GAAGF,SAAS,CAAC5C,MAAM,GAAG,CAAC,EAAE;sBACvCqC,CAAC,CAACC,MAAM,CAACG,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;oBAC5C,CAAC,MAAM;sBACLT,CAAC,CAACC,MAAM,CAACG,GAAG,GAAG,0BAA0B;oBAC3C;kBACF,CAAC,MAAM;oBACLJ,CAAC,CAACC,MAAM,CAACG,GAAG,GAAG,0BAA0B;kBAC3C;gBACF;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF3F,OAAA;gBAAK8D,SAAS,EAAC,cAAc;gBAAAyB,QAAA,eAC3BvF,OAAA,CAACP,YAAY;kBAACqE,SAAS,EAAC;gBAAW;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACN3F,OAAA;gBAAK8D,SAAS,EAAC,gBAAgB;gBAAAyB,QAAA,EAC5BxD,KAAK,CAACgC,QAAQ,IAAI;cAAO;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,EACL5D,KAAK,CAACkC,SAAS,IAAIlC,KAAK,CAACkC,SAAS,CAACP,MAAM,GAAG,CAAC,iBAC5C1D,OAAA;gBAAK8D,SAAS,EAAC,gBAAgB;gBAAAyB,QAAA,gBAC7BvF,OAAA,CAACL,YAAY;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,MAElB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN3F,OAAA;cAAK8D,SAAS,EAAC,oBAAoB;cAAAyB,QAAA,gBACjCvF,OAAA;gBAAI8D,SAAS,EAAC,aAAa;gBAAAyB,QAAA,EAAExD,KAAK,CAAC8B;cAAK;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9C3F,OAAA;gBAAK8D,SAAS,EAAC,YAAY;gBAAAyB,QAAA,gBACzBvF,OAAA;kBAAM8D,SAAS,EAAC,eAAe;kBAAAyB,QAAA,EAAEjD,cAAc,CAACP,KAAK,CAACQ,OAAO;gBAAC;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtE3F,OAAA;kBAAM8D,SAAS,EAAC,aAAa;kBAAAyB,QAAA,EAC1BtE,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEY,WAAW,GAAI,aAAYE,KAAK,CAAC+B,SAAS,IAAI/B,KAAK,CAACmB,KAAM,EAAC,GAAI,SAAQnB,KAAK,CAAC+B,SAAS,IAAI/B,KAAK,CAACmB,KAAM,EAAC,GACvG,QAAOnB,KAAK,CAAC+B,SAAS,IAAI/B,KAAK,CAACmB,KAAM;gBAAC;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN3F,OAAA;gBAAK8D,SAAS,EAAC,YAAY;gBAAAyB,QAAA,GACxBxD,KAAK,CAACiC,KAAK,iBAAIhE,OAAA;kBAAM8D,SAAS,EAAC,WAAW;kBAAAyB,QAAA,EAAExD,KAAK,CAACiC;gBAAK;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/D5D,KAAK,CAAC6E,eAAe,IAAI7E,KAAK,CAAC6E,eAAe,MAAM7E,KAAK,CAAC+B,SAAS,IAAI/B,KAAK,CAACmB,KAAK,CAAC,iBAClFlD,OAAA;kBAAM8D,SAAS,EAAC,YAAY;kBAAAyB,QAAA,GACzB1D,WAAW,GAAG,qBAAqB,GAAG,cAAc,EACpDZ,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEY,WAAW,GAAI,aAAYE,KAAK,CAAC6E,eAAgB,EAAC,GAAI,SAAQ7E,KAAK,CAAC6E,eAAgB,EAAC,GACrF,QAAO7E,KAAK,CAAC6E,eAAgB,EAAC;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLpE,iBAAiB,KAAKmB,KAAK,iBAC1B1C,OAAA;YAAK8D,SAAS,EAAC,qBAAqB;YAAAyB,QAAA,eAClCvF,OAAA;cAAK8D,SAAS,EAAC,sBAAsB;cAAAyB,QAAA,gBACnCvF,OAAA;gBAAK8D,SAAS,EAAC,sBAAsB;gBAAAyB,QAAA,EAClCxD,KAAK,CAACa,QAAQ,gBACb5C,OAAA;kBACE6G,GAAG,EAAGA,GAAG,IAAKnF,WAAW,CAACmF,GAAG,CAAE;kBAC/BC,QAAQ;kBACRC,QAAQ;kBACRC,WAAW;kBACXC,OAAO,EAAC,UAAU;kBAClBC,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAC,MAAM;kBACbC,MAAM,EAAEtF,eAAe,CAACC,KAAK,CAAE;kBAC/BsF,KAAK,EAAE;oBACLH,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdG,eAAe,EAAE,MAAM;oBACvBC,SAAS,EAAE;kBACb,CAAE;kBACFlB,OAAO,EAAGN,CAAC,IAAKnE,aAAa,CAAE,yBAAwBG,KAAK,CAAC8B,KAAM,EAAC,CAAE;kBACtE2D,SAAS,EAAEA,CAAA,KAAM5F,aAAa,CAAC,IAAI,CAAE;kBACrC6F,WAAW,EAAC,WAAW;kBAAAlC,QAAA,gBAEvBvF,OAAA;oBAAQmG,GAAG,EAAEpE,KAAK,CAACc,cAAc,IAAId,KAAK,CAACa,QAAS;oBAACQ,IAAI,EAAC;kBAAW;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACvE5D,KAAK,CAACkC,SAAS,IAAIlC,KAAK,CAACkC,SAAS,CAACP,MAAM,GAAG,CAAC,IAAI3B,KAAK,CAACkC,SAAS,CAACiC,GAAG,CAAC,CAACwB,QAAQ,EAAEhF,KAAK,kBACpF1C,OAAA;oBAEE2H,IAAI,EAAC,WAAW;oBAChBxB,GAAG,EAAEuB,QAAQ,CAACtD,GAAI;oBAClBwD,OAAO,EAAEF,QAAQ,CAACxD,QAAS;oBAC3B2D,KAAK,EAAEH,QAAQ,CAACvD,YAAa;oBAC7B2D,OAAO,EAAEJ,QAAQ,CAACrD,SAAS,IAAI3B,KAAK,KAAK;kBAAE,GALrC,GAAEgF,QAAQ,CAACxD,QAAS,IAAGxB,KAAM,EAAC;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,GACN5D,KAAK,CAACE,OAAO,gBACfjC,OAAA;kBACEmG,GAAG,EAAG,iCAAgCpE,KAAK,CAACE,OAAQ,mBAAmB;kBACvE4B,KAAK,EAAE9B,KAAK,CAAC8B,KAAM;kBACnBkE,WAAW,EAAC,GAAG;kBACfC,eAAe;kBACfX,KAAK,EAAE;oBAAEH,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE,MAAM;oBAAEc,MAAM,EAAE;kBAAO;gBAAE;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,gBAEV3F,OAAA;kBAAK8D,SAAS,EAAC,aAAa;kBAAAyB,QAAA,gBAC1BvF,OAAA;oBAAK8D,SAAS,EAAC,YAAY;oBAAAyB,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpC3F,OAAA;oBAAAuF,QAAA,EAAI;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1B3F,OAAA;oBAAAuF,QAAA,EAAI5D,UAAU,IAAI;kBAA4C;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3F,OAAA;gBAAK8D,SAAS,EAAC,oBAAoB;gBAAAyB,QAAA,gBACjCvF,OAAA;kBAAI8D,SAAS,EAAC,qBAAqB;kBAAAyB,QAAA,EAAExD,KAAK,CAAC8B;gBAAK;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtD3F,OAAA;kBAAK8D,SAAS,EAAC,oBAAoB;kBAAAyB,QAAA,gBACjCvF,OAAA;oBAAAuF,QAAA,EAAOjD,cAAc,CAACP,KAAK,CAACQ,OAAO;kBAAC;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5C3F,OAAA;oBAAAuF,QAAA,EAAM;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACd3F,OAAA;oBAAAuF,QAAA,GAAM,QAAM,EAACxD,KAAK,CAAC+B,SAAS,IAAI/B,KAAK,CAACmB,KAAK;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACN3F,OAAA;kBAAK8D,SAAS,EAAC,uBAAuB;kBAAAyB,QAAA,eACpCvF,OAAA;oBACE8D,SAAS,EAAC,oBAAoB;oBAC9BmC,OAAO,EAAEA,CAAA,KAAMzE,oBAAoB,CAAC,IAAI,CAAE;oBAAA+D,QAAA,gBAE1CvF,OAAA;sBAAAuF,QAAA,EAAM;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACd3F,OAAA;sBAAAuF,QAAA,EAAM;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA,GAhJOjD,KAAK;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiJV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEN3F,OAAA;QAAK8D,SAAS,EAAC,aAAa;QAAAyB,QAAA,gBAC1BvF,OAAA,CAACN,eAAe;UAACoE,SAAS,EAAC;QAAY;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1C3F,OAAA;UAAAuF,QAAA,EAAK1D,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1E3F,OAAA;UAAAuF,QAAA,EAAI1D,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJ3F,OAAA;UAAG8D,SAAS,EAAC,YAAY;UAAAyB,QAAA,EAAE1D,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzF,EAAA,CA/ZID,YAAY;EAAA,QAEGT,WAAW;AAAA;AAAA0I,EAAA,GAF1BjI,YAAY;AAialB,eAAeA,YAAY;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}