const mongoose = require('mongoose');
require('dotenv').config();

const Question = require('./models/questionModel');

async function cleanQuestionInstructions() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');

    // Find questions that still have remnants of the instructions
    console.log('🔍 Finding questions with instruction remnants...');
    
    const instructionPatterns = [
      /\(?\s*WRITE\s+YOUR?\s+ANSWER\s+IN\s+[^)]*\)?/gi,
      /\(?\s*WRITE\s+IN\s+[^)]*\)?/gi,
      /\(?\s*Answer\s+in\s+capital[^)]*\)?/gi,
      /\(?\s*Write\s+your\s+answer\s+in\s+capital[^)]*\)?/gi,
      /\(?\s*Using\s+capital\s+letters[^)]*\)?/gi,
      /\s+S\s+AND\s+A?\s*FULLSTOP\)?/gi,
      /\s+S\s+AND\s+FULLSTOP\)?/gi,
      /\s+with\s+a\s+fullstop\)?/gi,
      /\s+AND\s+A?\s*FULLSTOP\)?/gi
    ];

    // Find all questions that might need cleaning
    const allQuestions = await Question.find({});
    console.log(`📊 Checking ${allQuestions.length} total questions...`);

    let questionsToFix = [];
    
    for (const question of allQuestions) {
      let needsFix = false;
      for (const pattern of instructionPatterns) {
        if (pattern.test(question.name)) {
          needsFix = true;
          break;
        }
      }
      if (needsFix) {
        questionsToFix.push(question);
      }
    }

    console.log(`📊 Found ${questionsToFix.length} questions that need cleaning`);

    if (questionsToFix.length === 0) {
      console.log('✅ No questions need cleaning');
      return;
    }

    // Show sample questions before fixing
    console.log('\n📋 Sample questions to be cleaned:');
    questionsToFix.slice(0, 5).forEach((question, index) => {
      console.log(`${index + 1}. ${question.name.substring(0, 120)}...`);
    });

    // Clean the questions
    console.log('\n🔧 Cleaning questions...');
    let fixedCount = 0;

    for (const question of questionsToFix) {
      let cleanedText = question.name;

      // Apply all cleaning patterns
      for (const pattern of instructionPatterns) {
        cleanedText = cleanedText.replace(pattern, '');
      }

      // Additional cleanup
      cleanedText = cleanedText
        .replace(/\(\s*\)/g, '') // Remove empty parentheses
        .replace(/\[\s*\]/g, '') // Remove empty brackets
        .replace(/\s+/g, ' ') // Multiple spaces to single space
        .replace(/\s*[.,;:]+\s*/g, '. ') // Clean up punctuation
        .replace(/\.\s*\./g, '.') // Remove double periods
        .replace(/^\s*[.,;:]+\s*/, '') // Remove leading punctuation
        .replace(/\s*[.,;:]+\s*$/, '') // Remove trailing punctuation except final period
        .trim();

      // Add proper ending if needed
      if (cleanedText && !cleanedText.match(/[.?!]$/)) {
        cleanedText += '?';
      }

      // Only update if the text actually changed and is valid
      if (cleanedText !== question.name && cleanedText.length > 5) {
        await Question.findByIdAndUpdate(question._id, {
          name: cleanedText
        });

        console.log(`✅ Cleaned question ${question._id}`);
        console.log(`   Before: ${question.name.substring(0, 80)}...`);
        console.log(`   After:  ${cleanedText.substring(0, 80)}...`);
        console.log('');
        
        fixedCount++;
      }
    }

    console.log(`🎉 Successfully cleaned ${fixedCount} questions`);

    // Final verification
    console.log('\n🔍 Final verification...');
    const remainingIssues = await Question.find({
      $or: [
        { name: { $regex: /WRITE\s+YOUR?\s+ANSWER\s+IN/i } },
        { name: { $regex: /WRITE\s+IN\s+CAPITAL/i } },
        { name: { $regex: /S\s+AND\s+FULLSTOP/i } }
      ]
    });

    console.log(`📊 Remaining questions with instruction remnants: ${remainingIssues.length}`);

    if (remainingIssues.length > 0) {
      console.log('⚠️ Questions that still need attention:');
      remainingIssues.forEach((question, index) => {
        console.log(`${index + 1}. ${question.name.substring(0, 100)}...`);
      });
    } else {
      console.log('✅ All instruction remnants have been successfully removed!');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
}

// Run the script
cleanQuestionInstructions();
