{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ContentRenderer.js\";\nimport React from 'react';\nimport { InlineMath, BlockMath } from 'react-katex';\nimport 'katex/dist/katex.min.css';\n\n// Ensure KaTeX CSS is loaded\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst katexCSS = `\n.katex {\n  font-size: 1.1em !important;\n  line-height: 1.2 !important;\n}\n.katex-display {\n  margin: 1em 0 !important;\n  text-align: center !important;\n}\n`;\n\n// Inject CSS if not already present\nif (typeof document !== 'undefined' && !document.getElementById('katex-custom-styles')) {\n  const style = document.createElement('style');\n  style.id = 'katex-custom-styles';\n  style.textContent = katexCSS;\n  document.head.appendChild(style);\n}\nconst ContentRenderer = ({\n  text\n}) => {\n  // Handle undefined, null, or empty text\n  if (!text || typeof text !== 'string') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 16\n    }, this);\n  }\n\n  // Enhanced formatting for AI responses\n  const formatAIResponse = content => {\n    // First, remove all ** symbols and replace with bold formatting\n    let cleanContent = content.replace(/\\*\\*(.*?)\\*\\*/g, '<BOLD>$1</BOLD>');\n\n    // Split into lines for processing\n    const lines = cleanContent.split('\\n');\n    const formattedLines = [];\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i].trim();\n\n      // Skip empty lines but preserve spacing\n      if (!line) {\n        formattedLines.push( /*#__PURE__*/_jsxDEV(\"br\", {}, `br-${i}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 37\n        }, this));\n        continue;\n      }\n\n      // Handle numbered lists (1., 2., etc.)\n      if (/^\\d+\\.\\s/.test(line)) {\n        const content = line.replace(/^\\d+\\.\\s/, '');\n        formattedLines.push( /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '8px 0',\n            paddingLeft: '16px',\n            position: 'relative',\n            lineHeight: '1.6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              left: '0',\n              fontWeight: 'bold',\n              color: '#3b82f6'\n            },\n            children: [line.match(/^\\d+/)[0], \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 25\n          }, this)]\n        }, `numbered-${i}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 21\n        }, this));\n        continue;\n      }\n\n      // Handle bullet points (-, *, •)\n      if (/^[-*•]\\s/.test(line)) {\n        const content = line.replace(/^[-*•]\\s/, '');\n        formattedLines.push( /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '6px 0',\n            paddingLeft: '16px',\n            position: 'relative',\n            lineHeight: '1.6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              left: '0',\n              color: '#3b82f6',\n              fontWeight: 'bold'\n            },\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 25\n          }, this)]\n        }, `bullet-${i}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 21\n        }, this));\n        continue;\n      }\n\n      // Handle headers (##, ###)\n      if (/^#{2,3}\\s/.test(line)) {\n        const level = line.match(/^#+/)[0].length;\n        const content = line.replace(/^#+\\s/, '');\n        formattedLines.push( /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: level === 2 ? '18px' : '16px',\n            fontWeight: 'bold',\n            color: '#1f2937',\n            margin: '16px 0 8px 0',\n            borderBottom: level === 2 ? '2px solid #e5e7eb' : 'none',\n            paddingBottom: level === 2 ? '4px' : '0'\n          },\n          children: content\n        }, `header-${i}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 21\n        }, this));\n        continue;\n      }\n\n      // Regular text with bold formatting (convert BOLD tags to styled spans)\n      const processedLine = line.replace(/<BOLD>(.*?)<\\/BOLD>/g, '<span style=\"font-weight: bold; color: #1f2937;\">$1</span>').replace(/\\*\\*/g, ''); // Remove any remaining ** symbols\n      formattedLines.push( /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          margin: '4px 0',\n          lineHeight: '1.6'\n        },\n        dangerouslySetInnerHTML: {\n          __html: processedLine\n        }\n      }, `text-${i}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 17\n      }, this));\n    }\n    return formattedLines;\n  };\n  const inlineMathRegex = /\\\\\\(.*?\\\\\\)/g;\n  const blockMathRegex = /\\\\\\[.*?\\\\\\]/gs;\n  const boldTextRegex = /\\*\\*.*?\\*\\*/g;\n  // console.log('Text: ', text);\n  let modifiedText = text.replace(blockMathRegex, match => match.replace(/\\n/g, '~~NEWLINE~~'));\n  const lines = modifiedText.split('\\n');\n  // console.log('Lines with symbol: ', lines);\n  const restoredLines = lines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\n  // console.log('Lines: ', restoredLines);\n\n  const inlineMathSymbol = \"~~INLINEMATH~~\";\n  const blockMathSymbol = \"~~BLOCKMATH~~\";\n  const boldSymbol = \"~~BOLD~~\";\n  let newModifiedText = text.replace(blockMathRegex, match => {\n    return `~~BLOCKMATH~~${match.replace(/\\n/g, '~~NEWLINE~~')}~~BLOCKMATH~~`;\n  });\n  newModifiedText = newModifiedText.replace(inlineMathRegex, match => {\n    return `~~INLINEMATH~~${match}~~INLINEMATH~~`;\n  });\n  newModifiedText = newModifiedText.replace(boldTextRegex, match => {\n    // console.log('Bold Part: ', match);\n    return `~~BOLD~~${match.replace(/\\*\\*/g, '')}~~BOLD~~`;\n  });\n  const newLines = newModifiedText.split('\\n');\n  const newRestoredLines = newLines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\n\n  // console.log('New Modified Text: ', newModifiedText);\n\n  const newRegex = /(~~INLINEMATH~~\\\\?\\(.*?\\\\?\\)~~INLINEMATH~~|~~BLOCKMATH~~\\\\?\\[.*?\\\\?\\]~~BLOCKMATH~~|~~BOLD~~.*?~~BOLD~~)/;\n\n  // Debug logging removed to prevent React rendering issues\n\n  // Check if text contains mathematical expressions\n  const hasMath = inlineMathRegex.test(text) || blockMathRegex.test(text);\n\n  // If no math, use enhanced AI formatting\n  if (!hasMath) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 'inherit',\n        lineHeight: 'inherit'\n      },\n      children: formatAIResponse(text)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Original math rendering logic for mathematical content\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: newRestoredLines.map((line, lineIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: line.trim() === '' ? /*#__PURE__*/_jsxDEV(\"br\", {}, `br-${lineIndex}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 25\n      }, this) : line.split(newRegex).map((part, index) => {\n        if (part.startsWith(boldSymbol) && part.endsWith(boldSymbol)) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: part.replace(/~~BOLD~~/g, '').split(newRegex).map((nestedPart, n_index) => {\n              if (nestedPart.startsWith(inlineMathSymbol) && nestedPart.endsWith(inlineMathSymbol)) {\n                return /*#__PURE__*/_jsxDEV(InlineMath, {\n                  children: nestedPart.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')\n                }, `${lineIndex}-${index}-${n_index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 53\n                }, this);\n              } else if (nestedPart.startsWith(blockMathSymbol) && nestedPart.endsWith(blockMathSymbol)) {\n                return /*#__PURE__*/_jsxDEV(BlockMath, {\n                  children: nestedPart.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')\n                }, `${lineIndex}-${index}-${n_index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 53\n                }, this);\n              } else {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    whiteSpace: 'pre-wrap'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: nestedPart\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 57\n                  }, this)\n                }, `${lineIndex}-${index}-${n_index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 53\n                }, this);\n              }\n            })\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 37\n          }, this);\n        } else if (part.startsWith(inlineMathSymbol) && part.endsWith(inlineMathSymbol)) {\n          return /*#__PURE__*/_jsxDEV(InlineMath, {\n            children: part.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 37\n          }, this);\n        } else if (part.startsWith(blockMathSymbol) && part.endsWith(blockMathSymbol)) {\n          return /*#__PURE__*/_jsxDEV(BlockMath, {\n            children: part.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 37\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              whiteSpace: 'pre-wrap'\n            },\n            children: part\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 37\n          }, this);\n        }\n      })\n    }, lineIndex, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 9\n  }, this);\n};\n_c = ContentRenderer;\nexport default ContentRenderer;\nvar _c;\n$RefreshReg$(_c, \"ContentRenderer\");", "map": {"version": 3, "names": ["React", "InlineMath", "BlockMath", "jsxDEV", "_jsxDEV", "katexCSS", "document", "getElementById", "style", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "Content<PERSON><PERSON><PERSON>", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatAIResponse", "content", "cleanContent", "replace", "lines", "split", "formattedLines", "i", "length", "line", "trim", "push", "test", "margin", "paddingLeft", "position", "lineHeight", "children", "left", "fontWeight", "color", "match", "level", "fontSize", "borderBottom", "paddingBottom", "processedLine", "dangerouslySetInnerHTML", "__html", "inlineMathRegex", "blockMathRegex", "boldTextRegex", "modifiedText", "restoredLines", "map", "inlineMathSymbol", "blockMathSymbol", "boldSymbol", "newModifiedText", "newLines", "newRestoredLines", "newRegex", "<PERSON><PERSON><PERSON>", "lineIndex", "part", "index", "startsWith", "endsWith", "Fragment", "nested<PERSON><PERSON>", "n_index", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ContentRenderer.js"], "sourcesContent": ["import React from 'react';\r\nimport { InlineMath, BlockMath } from 'react-katex';\r\nimport 'katex/dist/katex.min.css';\r\n\r\n// Ensure KaTeX CSS is loaded\r\nconst katexCSS = `\r\n.katex {\r\n  font-size: 1.1em !important;\r\n  line-height: 1.2 !important;\r\n}\r\n.katex-display {\r\n  margin: 1em 0 !important;\r\n  text-align: center !important;\r\n}\r\n`;\r\n\r\n// Inject CSS if not already present\r\nif (typeof document !== 'undefined' && !document.getElementById('katex-custom-styles')) {\r\n  const style = document.createElement('style');\r\n  style.id = 'katex-custom-styles';\r\n  style.textContent = katexCSS;\r\n  document.head.appendChild(style);\r\n}\r\n\r\nconst ContentRenderer = ({ text }) => {\r\n    // Handle undefined, null, or empty text\r\n    if (!text || typeof text !== 'string') {\r\n        return <div></div>;\r\n    }\r\n\r\n    // Enhanced formatting for AI responses\r\n    const formatAIResponse = (content) => {\r\n        // First, remove all ** symbols and replace with bold formatting\r\n        let cleanContent = content.replace(/\\*\\*(.*?)\\*\\*/g, '<BOLD>$1</BOLD>');\r\n\r\n        // Split into lines for processing\r\n        const lines = cleanContent.split('\\n');\r\n        const formattedLines = [];\r\n\r\n        for (let i = 0; i < lines.length; i++) {\r\n            const line = lines[i].trim();\r\n\r\n            // Skip empty lines but preserve spacing\r\n            if (!line) {\r\n                formattedLines.push(<br key={`br-${i}`} />);\r\n                continue;\r\n            }\r\n\r\n            // Handle numbered lists (1., 2., etc.)\r\n            if (/^\\d+\\.\\s/.test(line)) {\r\n                const content = line.replace(/^\\d+\\.\\s/, '');\r\n                formattedLines.push(\r\n                    <div key={`numbered-${i}`} style={{\r\n                        margin: '8px 0',\r\n                        paddingLeft: '16px',\r\n                        position: 'relative',\r\n                        lineHeight: '1.6'\r\n                    }}>\r\n                        <span style={{\r\n                            position: 'absolute',\r\n                            left: '0',\r\n                            fontWeight: 'bold',\r\n                            color: '#3b82f6'\r\n                        }}>\r\n                            {line.match(/^\\d+/)[0]}.\r\n                        </span>\r\n                        <span>{content}</span>\r\n                    </div>\r\n                );\r\n                continue;\r\n            }\r\n\r\n            // Handle bullet points (-, *, •)\r\n            if (/^[-*•]\\s/.test(line)) {\r\n                const content = line.replace(/^[-*•]\\s/, '');\r\n                formattedLines.push(\r\n                    <div key={`bullet-${i}`} style={{\r\n                        margin: '6px 0',\r\n                        paddingLeft: '16px',\r\n                        position: 'relative',\r\n                        lineHeight: '1.6'\r\n                    }}>\r\n                        <span style={{\r\n                            position: 'absolute',\r\n                            left: '0',\r\n                            color: '#3b82f6',\r\n                            fontWeight: 'bold'\r\n                        }}>\r\n                            •\r\n                        </span>\r\n                        <span>{content}</span>\r\n                    </div>\r\n                );\r\n                continue;\r\n            }\r\n\r\n            // Handle headers (##, ###)\r\n            if (/^#{2,3}\\s/.test(line)) {\r\n                const level = line.match(/^#+/)[0].length;\r\n                const content = line.replace(/^#+\\s/, '');\r\n                formattedLines.push(\r\n                    <div key={`header-${i}`} style={{\r\n                        fontSize: level === 2 ? '18px' : '16px',\r\n                        fontWeight: 'bold',\r\n                        color: '#1f2937',\r\n                        margin: '16px 0 8px 0',\r\n                        borderBottom: level === 2 ? '2px solid #e5e7eb' : 'none',\r\n                        paddingBottom: level === 2 ? '4px' : '0'\r\n                    }}>\r\n                        {content}\r\n                    </div>\r\n                );\r\n                continue;\r\n            }\r\n\r\n            // Regular text with bold formatting (convert BOLD tags to styled spans)\r\n            const processedLine = line\r\n                .replace(/<BOLD>(.*?)<\\/BOLD>/g, '<span style=\"font-weight: bold; color: #1f2937;\">$1</span>')\r\n                .replace(/\\*\\*/g, ''); // Remove any remaining ** symbols\r\n            formattedLines.push(\r\n                <div key={`text-${i}`} style={{\r\n                    margin: '4px 0',\r\n                    lineHeight: '1.6'\r\n                }} dangerouslySetInnerHTML={{ __html: processedLine }} />\r\n            );\r\n        }\r\n\r\n        return formattedLines;\r\n    };\r\n\r\n    const inlineMathRegex = /\\\\\\(.*?\\\\\\)/g;\r\n    const blockMathRegex = /\\\\\\[.*?\\\\\\]/gs;\r\n    const boldTextRegex = /\\*\\*.*?\\*\\*/g;\r\n    // console.log('Text: ', text);\r\n    let modifiedText = text.replace(blockMathRegex, match => match.replace(/\\n/g, '~~NEWLINE~~'));\r\n    const lines = modifiedText.split('\\n');\r\n    // console.log('Lines with symbol: ', lines);\r\n    const restoredLines = lines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n    // console.log('Lines: ', restoredLines);\r\n\r\n\r\n\r\n\r\n    const inlineMathSymbol = \"~~INLINEMATH~~\";\r\n    const blockMathSymbol = \"~~BLOCKMATH~~\";\r\n    const boldSymbol = \"~~BOLD~~\";\r\n\r\n    let newModifiedText = text.replace(blockMathRegex, match => {\r\n        return `~~BLOCKMATH~~${match.replace(/\\n/g, '~~NEWLINE~~')}~~BLOCKMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(inlineMathRegex, match => {\r\n        return `~~INLINEMATH~~${match}~~INLINEMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(boldTextRegex, match => {\r\n        // console.log('Bold Part: ', match);\r\n        return `~~BOLD~~${match.replace(/\\*\\*/g, '')}~~BOLD~~`;\r\n    });\r\n\r\n    const newLines = newModifiedText.split('\\n');\r\n\r\n    const newRestoredLines = newLines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n\r\n    // console.log('New Modified Text: ', newModifiedText);\r\n\r\n    const newRegex = /(~~INLINEMATH~~\\\\?\\(.*?\\\\?\\)~~INLINEMATH~~|~~BLOCKMATH~~\\\\?\\[.*?\\\\?\\]~~BLOCKMATH~~|~~BOLD~~.*?~~BOLD~~)/;\r\n\r\n    // Debug logging removed to prevent React rendering issues\r\n\r\n    // Check if text contains mathematical expressions\r\n    const hasMath = inlineMathRegex.test(text) || blockMathRegex.test(text);\r\n\r\n    // If no math, use enhanced AI formatting\r\n    if (!hasMath) {\r\n        return (\r\n            <div style={{ fontSize: 'inherit', lineHeight: 'inherit' }}>\r\n                {formatAIResponse(text)}\r\n            </div>\r\n        );\r\n    }\r\n\r\n    // Original math rendering logic for mathematical content\r\n    return (\r\n        <div>\r\n            {newRestoredLines.map((line, lineIndex) => (\r\n                <div key={lineIndex}>\r\n                    {line.trim() === '' ?\r\n                        <br key={`br-${lineIndex}`} />\r\n                        :\r\n                        line.split(newRegex).map((part, index) => {\r\n                            if (part.startsWith(boldSymbol) && part.endsWith(boldSymbol)) {\r\n                                return (\r\n                                    <React.Fragment key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BOLD~~/g, '').split(newRegex).map((nestedPart, n_index) => {\r\n                                            if (nestedPart.startsWith(inlineMathSymbol) && nestedPart.endsWith(inlineMathSymbol)) {\r\n                                                return (\r\n                                                    <InlineMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                                    </InlineMath>\r\n                                                );\r\n                                            } else if (nestedPart.startsWith(blockMathSymbol) && nestedPart.endsWith(blockMathSymbol)) {\r\n                                                return (\r\n                                                    <BlockMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                                    </BlockMath>\r\n                                                );\r\n                                            } else {\r\n                                                return (\r\n                                                    <span key={`${lineIndex}-${index}-${n_index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                                        <strong>{nestedPart}</strong>\r\n                                                    </span>\r\n                                                );\r\n                                            }\r\n                                        })}\r\n                                    </React.Fragment>\r\n                                );\r\n                            } else if (part.startsWith(inlineMathSymbol) && part.endsWith(inlineMathSymbol)) {\r\n                                return (\r\n                                    <InlineMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                    </InlineMath>\r\n                                );\r\n                            } else if (part.startsWith(blockMathSymbol) && part.endsWith(blockMathSymbol)) {\r\n                                return (\r\n                                    <BlockMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                    </BlockMath>\r\n                                );\r\n                            } else {\r\n                                return (\r\n                                    <span key={`${lineIndex}-${index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                        {part}\r\n                                    </span>\r\n                                );\r\n                            }\r\n                        })}\r\n                </div>\r\n            ))}\r\n        </div>\r\n\r\n    )\r\n};\r\n\r\nexport default ContentRenderer;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,SAAS,QAAQ,aAAa;AACnD,OAAO,0BAA0B;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,QAAQ,GAAI;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC,EAAE;EACtF,MAAMC,KAAK,GAAGF,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EAC7CD,KAAK,CAACE,EAAE,GAAG,qBAAqB;EAChCF,KAAK,CAACG,WAAW,GAAGN,QAAQ;EAC5BC,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;AAClC;AAEA,MAAMM,eAAe,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAClC;EACA,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACnC,oBAAOX,OAAA;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAU,CAAC;EACtB;;EAEA;EACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IAClC;IACA,IAAIC,YAAY,GAAGD,OAAO,CAACE,OAAO,CAAC,gBAAgB,EAAE,iBAAiB,CAAC;;IAEvE;IACA,MAAMC,KAAK,GAAGF,YAAY,CAACG,KAAK,CAAC,IAAI,CAAC;IACtC,MAAMC,cAAc,GAAG,EAAE;IAEzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC,MAAME,IAAI,GAAGL,KAAK,CAACG,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;;MAE5B;MACA,IAAI,CAACD,IAAI,EAAE;QACPH,cAAc,CAACK,IAAI,eAAC3B,OAAA,WAAU,MAAKuB,CAAE,EAAC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,CAAC;QAC3C;MACJ;;MAEA;MACA,IAAI,UAAU,CAACa,IAAI,CAACH,IAAI,CAAC,EAAE;QACvB,MAAMR,OAAO,GAAGQ,IAAI,CAACN,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;QAC5CG,cAAc,CAACK,IAAI,eACf3B,OAAA;UAA2BI,KAAK,EAAE;YAC9ByB,MAAM,EAAE,OAAO;YACfC,WAAW,EAAE,MAAM;YACnBC,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE;UAChB,CAAE;UAAAC,QAAA,gBACEjC,OAAA;YAAMI,KAAK,EAAE;cACT2B,QAAQ,EAAE,UAAU;cACpBG,IAAI,EAAE,GAAG;cACTC,UAAU,EAAE,MAAM;cAClBC,KAAK,EAAE;YACX,CAAE;YAAAH,QAAA,GACGR,IAAI,CAACY,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC,GAC3B;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPf,OAAA;YAAAiC,QAAA,EAAOhB;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAdf,YAAWQ,CAAE,EAAC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAepB,CACT,CAAC;QACD;MACJ;;MAEA;MACA,IAAI,UAAU,CAACa,IAAI,CAACH,IAAI,CAAC,EAAE;QACvB,MAAMR,OAAO,GAAGQ,IAAI,CAACN,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;QAC5CG,cAAc,CAACK,IAAI,eACf3B,OAAA;UAAyBI,KAAK,EAAE;YAC5ByB,MAAM,EAAE,OAAO;YACfC,WAAW,EAAE,MAAM;YACnBC,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE;UAChB,CAAE;UAAAC,QAAA,gBACEjC,OAAA;YAAMI,KAAK,EAAE;cACT2B,QAAQ,EAAE,UAAU;cACpBG,IAAI,EAAE,GAAG;cACTE,KAAK,EAAE,SAAS;cAChBD,UAAU,EAAE;YAChB,CAAE;YAAAF,QAAA,EAAC;UAEH;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPf,OAAA;YAAAiC,QAAA,EAAOhB;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAdf,UAASQ,CAAE,EAAC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAelB,CACT,CAAC;QACD;MACJ;;MAEA;MACA,IAAI,WAAW,CAACa,IAAI,CAACH,IAAI,CAAC,EAAE;QACxB,MAAMa,KAAK,GAAGb,IAAI,CAACY,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACb,MAAM;QACzC,MAAMP,OAAO,GAAGQ,IAAI,CAACN,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;QACzCG,cAAc,CAACK,IAAI,eACf3B,OAAA;UAAyBI,KAAK,EAAE;YAC5BmC,QAAQ,EAAED,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM;YACvCH,UAAU,EAAE,MAAM;YAClBC,KAAK,EAAE,SAAS;YAChBP,MAAM,EAAE,cAAc;YACtBW,YAAY,EAAEF,KAAK,KAAK,CAAC,GAAG,mBAAmB,GAAG,MAAM;YACxDG,aAAa,EAAEH,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG;UACzC,CAAE;UAAAL,QAAA,EACGhB;QAAO,GARD,UAASM,CAAE,EAAC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASlB,CACT,CAAC;QACD;MACJ;;MAEA;MACA,MAAM2B,aAAa,GAAGjB,IAAI,CACrBN,OAAO,CAAC,sBAAsB,EAAE,4DAA4D,CAAC,CAC7FA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;MAC3BG,cAAc,CAACK,IAAI,eACf3B,OAAA;QAAuBI,KAAK,EAAE;UAC1ByB,MAAM,EAAE,OAAO;UACfG,UAAU,EAAE;QAChB,CAAE;QAACW,uBAAuB,EAAE;UAAEC,MAAM,EAAEF;QAAc;MAAE,GAH3C,QAAOnB,CAAE,EAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGmC,CAC5D,CAAC;IACL;IAEA,OAAOO,cAAc;EACzB,CAAC;EAED,MAAMuB,eAAe,GAAG,cAAc;EACtC,MAAMC,cAAc,GAAG,eAAe;EACtC,MAAMC,aAAa,GAAG,cAAc;EACpC;EACA,IAAIC,YAAY,GAAGrC,IAAI,CAACQ,OAAO,CAAC2B,cAAc,EAAET,KAAK,IAAIA,KAAK,CAAClB,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;EAC7F,MAAMC,KAAK,GAAG4B,YAAY,CAAC3B,KAAK,CAAC,IAAI,CAAC;EACtC;EACA,MAAM4B,aAAa,GAAG7B,KAAK,CAAC8B,GAAG,CAACzB,IAAI,IAAIA,IAAI,CAACN,OAAO,CAAC,cAAc,EAAG,MAAK,CAAC,CAAC;EAC7E;;EAKA,MAAMgC,gBAAgB,GAAG,gBAAgB;EACzC,MAAMC,eAAe,GAAG,eAAe;EACvC,MAAMC,UAAU,GAAG,UAAU;EAE7B,IAAIC,eAAe,GAAG3C,IAAI,CAACQ,OAAO,CAAC2B,cAAc,EAAET,KAAK,IAAI;IACxD,OAAQ,gBAAeA,KAAK,CAAClB,OAAO,CAAC,KAAK,EAAE,aAAa,CAAE,eAAc;EAC7E,CAAC,CAAC;EAEFmC,eAAe,GAAGA,eAAe,CAACnC,OAAO,CAAC0B,eAAe,EAAER,KAAK,IAAI;IAChE,OAAQ,iBAAgBA,KAAM,gBAAe;EACjD,CAAC,CAAC;EAEFiB,eAAe,GAAGA,eAAe,CAACnC,OAAO,CAAC4B,aAAa,EAAEV,KAAK,IAAI;IAC9D;IACA,OAAQ,WAAUA,KAAK,CAAClB,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,UAAS;EAC1D,CAAC,CAAC;EAEF,MAAMoC,QAAQ,GAAGD,eAAe,CAACjC,KAAK,CAAC,IAAI,CAAC;EAE5C,MAAMmC,gBAAgB,GAAGD,QAAQ,CAACL,GAAG,CAACzB,IAAI,IAAIA,IAAI,CAACN,OAAO,CAAC,cAAc,EAAG,MAAK,CAAC,CAAC;;EAEnF;;EAEA,MAAMsC,QAAQ,GAAG,yGAAyG;;EAE1H;;EAEA;EACA,MAAMC,OAAO,GAAGb,eAAe,CAACjB,IAAI,CAACjB,IAAI,CAAC,IAAImC,cAAc,CAAClB,IAAI,CAACjB,IAAI,CAAC;;EAEvE;EACA,IAAI,CAAC+C,OAAO,EAAE;IACV,oBACI1D,OAAA;MAAKI,KAAK,EAAE;QAAEmC,QAAQ,EAAE,SAAS;QAAEP,UAAU,EAAE;MAAU,CAAE;MAAAC,QAAA,EACtDjB,gBAAgB,CAACL,IAAI;IAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAEd;;EAEA;EACA,oBACIf,OAAA;IAAAiC,QAAA,EACKuB,gBAAgB,CAACN,GAAG,CAAC,CAACzB,IAAI,EAAEkC,SAAS,kBAClC3D,OAAA;MAAAiC,QAAA,EACKR,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,gBACf1B,OAAA,WAAU,MAAK2D,SAAU,EAAC;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,GAE9BU,IAAI,CAACJ,KAAK,CAACoC,QAAQ,CAAC,CAACP,GAAG,CAAC,CAACU,IAAI,EAAEC,KAAK,KAAK;QACtC,IAAID,IAAI,CAACE,UAAU,CAACT,UAAU,CAAC,IAAIO,IAAI,CAACG,QAAQ,CAACV,UAAU,CAAC,EAAE;UAC1D,oBACIrD,OAAA,CAACJ,KAAK,CAACoE,QAAQ;YAAA/B,QAAA,EACV2B,IAAI,CAACzC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACE,KAAK,CAACoC,QAAQ,CAAC,CAACP,GAAG,CAAC,CAACe,UAAU,EAAEC,OAAO,KAAK;cACxE,IAAID,UAAU,CAACH,UAAU,CAACX,gBAAgB,CAAC,IAAIc,UAAU,CAACF,QAAQ,CAACZ,gBAAgB,CAAC,EAAE;gBAClF,oBACInD,OAAA,CAACH,UAAU;kBAAAoC,QAAA,EACNgC,UAAU,CAAC9C,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,cAAc,EAAE,EAAE;gBAAC,GADxD,GAAEwC,SAAU,IAAGE,KAAM,IAAGK,OAAQ,EAAC;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvC,CAAC;cAErB,CAAC,MAAM,IAAIkD,UAAU,CAACH,UAAU,CAACV,eAAe,CAAC,IAAIa,UAAU,CAACF,QAAQ,CAACX,eAAe,CAAC,EAAE;gBACvF,oBACIpD,OAAA,CAACF,SAAS;kBAAAmC,QAAA,EACLgC,UAAU,CAAC9C,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,EAAE;gBAAC,GADrD,GAAEwC,SAAU,IAAGE,KAAM,IAAGK,OAAQ,EAAC;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvC,CAAC;cAEpB,CAAC,MAAM;gBACH,oBACIf,OAAA;kBAA+CI,KAAK,EAAE;oBAAE+D,UAAU,EAAE;kBAAW,CAAE;kBAAAlC,QAAA,eAC7EjC,OAAA;oBAAAiC,QAAA,EAASgC;kBAAU;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC,GADrB,GAAE4C,SAAU,IAAGE,KAAM,IAAGK,OAAQ,EAAC;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvC,CAAC;cAEf;YACJ,CAAC;UAAC,GArBgB,GAAE4C,SAAU,IAAGE,KAAM,EAAC;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsB5B,CAAC;QAEzB,CAAC,MAAM,IAAI6C,IAAI,CAACE,UAAU,CAACX,gBAAgB,CAAC,IAAIS,IAAI,CAACG,QAAQ,CAACZ,gBAAgB,CAAC,EAAE;UAC7E,oBACInD,OAAA,CAACH,UAAU;YAAAoC,QAAA,EACN2B,IAAI,CAACzC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,cAAc,EAAE,EAAE;UAAC,GADlD,GAAEwC,SAAU,IAAGE,KAAM,EAAC;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC;QAErB,CAAC,MAAM,IAAI6C,IAAI,CAACE,UAAU,CAACV,eAAe,CAAC,IAAIQ,IAAI,CAACG,QAAQ,CAACX,eAAe,CAAC,EAAE;UAC3E,oBACIpD,OAAA,CAACF,SAAS;YAAAmC,QAAA,EACL2B,IAAI,CAACzC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,EAAE;UAAC,GAD/C,GAAEwC,SAAU,IAAGE,KAAM,EAAC;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC;QAEpB,CAAC,MAAM;UACH,oBACIf,OAAA;YAAoCI,KAAK,EAAE;cAAE+D,UAAU,EAAE;YAAW,CAAE;YAAAlC,QAAA,EACjE2B;UAAI,GADG,GAAED,SAAU,IAAGE,KAAM,EAAC;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC;QAEf;MACJ,CAAC;IAAC,GAlDA4C,SAAS;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAmDd,CACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAGd,CAAC;AAACqD,EAAA,GA1NI1D,eAAe;AA4NrB,eAAeA,eAAe;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}