{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// ✅ Fixed block around line 1066 with React.Fragment wrapper\n/*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"videos-grid\",\n  children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-card\",\n        onClick: () => handleShowVideo(index),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-card-thumbnail\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: getThumbnailUrl(video),\n            alt: video.title,\n            className: \"thumbnail-image\",\n            loading: \"lazy\",\n            onError: e => {\n              if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                let videoId = video.videoID;\n                const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                videoId = match ? match[1] : videoId;\n                const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                const currentSrc = e.target.src;\n                const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                if (currentIndex < fallbacks.length - 1) {\n                  e.target.src = fallbacks[currentIndex + 1];\n                } else {\n                  e.target.src = '/api/placeholder/320/180';\n                }\n              } else {\n                e.target.src = '/api/placeholder/320/180';\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 9,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"play-overlay\",\n            children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n              className: \"play-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-duration\",\n            children: video.duration || \"Video\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subtitle-badge\",\n            children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 17\n            }, this), \"CC\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"video-title\",\n            children: video.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-meta\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"video-subject\",\n              children: getSubjectName(video.subject)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"video-class\",\n              children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-tags\",\n            children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"topic-tag\",\n              children: video.topic\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 31\n            }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"shared-tag\",\n              children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-video-player\",\n        children: /*#__PURE__*/_jsxDEV(\"video\", {\n          ref: ref => setVideoRef(ref),\n          controls: true,\n          autoPlay: true,\n          playsInline: true,\n          preload: \"metadata\",\n          width: \"100%\",\n          height: \"100%\",\n          poster: getThumbnailUrl(video),\n          style: {\n            width: '100%',\n            height: '100%',\n            backgroundColor: '#000',\n            objectFit: 'contain'\n          },\n          onError: e => setVideoError(`Failed to load video: ${video.title}`),\n          onCanPlay: () => setVideoError(null),\n          crossOrigin: \"anonymous\",\n          children: [/*#__PURE__*/_jsxDEV(\"source\", {\n            src: video.signedVideoUrl || video.videoUrl,\n            type: \"video/mp4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n            kind: \"subtitles\",\n            src: subtitle.url,\n            srcLang: subtitle.language,\n            label: subtitle.languageName,\n            default: subtitle.isDefault || index === 0\n          }, `${subtitle.language}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 17\n          }, this)), \"Your browser does not support the video tag.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 7\n    }, this)\n  }, index, false, {\n    fileName: _jsxFileName,\n    lineNumber: 4,\n    columnNumber: 5\n  }, this))\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 2,\n  columnNumber: 1\n}, this);", "map": {"version": 3, "names": ["_jsxDEV", "className", "children", "filteredAndSortedVideos", "map", "video", "index", "React", "Fragment", "onClick", "handleShowVideo", "src", "getThumbnailUrl", "alt", "title", "loading", "onError", "e", "videoID", "includes", "videoId", "match", "fallbacks", "currentSrc", "target", "currentIndex", "findIndex", "url", "split", "pop", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FaPlayCircle", "duration", "subtitles", "TbInfoCircle", "getSubjectName", "subject", "selectedLevel", "isKiswahili", "class", "topic", "sharedFromClass", "currentVideoIndex", "ref", "setVideoRef", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "style", "backgroundColor", "objectFit", "setVideoError", "onCanPlay", "crossOrigin", "signedVideoUrl", "videoUrl", "type", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["// ✅ Fixed block around line 1066 with React.Fragment wrapper\n<div className=\"videos-grid\">\n  {filteredAndSortedVideos.map((video, index) => (\n    <React.Fragment key={index}>\n      <div className=\"video-item\">\n        {/* Video Card */}\n        <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n          <div className=\"video-card-thumbnail\">\n            <img\n              src={getThumbnailUrl(video)}\n              alt={video.title}\n              className=\"thumbnail-image\"\n              loading=\"lazy\"\n              onError={(e) => {\n                if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                  let videoId = video.videoID;\n                  const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                  videoId = match ? match[1] : videoId;\n                  const fallbacks = [\n                    `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                    `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                    `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                    '/api/placeholder/320/180'\n                  ];\n                  const currentSrc = e.target.src;\n                  const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                  if (currentIndex < fallbacks.length - 1) {\n                    e.target.src = fallbacks[currentIndex + 1];\n                  } else {\n                    e.target.src = '/api/placeholder/320/180';\n                  }\n                } else {\n                  e.target.src = '/api/placeholder/320/180';\n                }\n              }}\n            />\n            <div className=\"play-overlay\">\n              <FaPlayCircle className=\"play-icon\" />\n            </div>\n            <div className=\"video-duration\">\n              {video.duration || \"Video\"}\n            </div>\n            {video.subtitles && video.subtitles.length > 0 && (\n              <div className=\"subtitle-badge\">\n                <TbInfoCircle />\n                CC\n              </div>\n            )}\n          </div>\n\n          <div className=\"video-card-content\">\n            <h3 className=\"video-title\">{video.title}</h3>\n            <div className=\"video-meta\">\n              <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n              <span className=\"video-class\">\n                {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                  ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)\n                  : `Form ${video.className || video.class}`}\n              </span>\n            </div>\n            <div className=\"video-tags\">\n              {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n              {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                <span className=\"shared-tag\">\n                  {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}\n                  {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                    ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)\n                    : `Form ${video.sharedFromClass}`}\n                </span>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Inline Video Player */}\n        {currentVideoIndex === index && (\n          <div className=\"inline-video-player\">\n            <video\n              ref={(ref) => setVideoRef(ref)}\n              controls\n              autoPlay\n              playsInline\n              preload=\"metadata\"\n              width=\"100%\"\n              height=\"100%\"\n              poster={getThumbnailUrl(video)}\n              style={{\n                width: '100%',\n                height: '100%',\n                backgroundColor: '#000',\n                objectFit: 'contain'\n              }}\n              onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}\n              onCanPlay={() => setVideoError(null)}\n              crossOrigin=\"anonymous\"\n            >\n              <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n              {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                <track\n                  key={`${subtitle.language}-${index}`}\n                  kind=\"subtitles\"\n                  src={subtitle.url}\n                  srcLang={subtitle.language}\n                  label={subtitle.languageName}\n                  default={subtitle.isDefault || index === 0}\n                />\n              ))}\n              Your browser does not support the video tag.\n            </video>\n          </div>\n        )}\n      </div>\n    </React.Fragment>\n  ))}\n</div>\n"], "mappings": ";;AAAA;AACA,aAAAA,OAAA;EAAKC,SAAS,EAAC,aAAa;EAAAC,QAAA,EACzBC,uBAAuB,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACxCN,OAAA,CAACO,KAAK,CAACC,QAAQ;IAAAN,QAAA,eACbF,OAAA;MAAKC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAEzBF,OAAA;QAAKC,SAAS,EAAC,YAAY;QAACQ,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAACJ,KAAK,CAAE;QAAAJ,QAAA,gBAChEF,OAAA;UAAKC,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCF,OAAA;YACEW,GAAG,EAAEC,eAAe,CAACP,KAAK,CAAE;YAC5BQ,GAAG,EAAER,KAAK,CAACS,KAAM;YACjBb,SAAS,EAAC,iBAAiB;YAC3Bc,OAAO,EAAC,MAAM;YACdC,OAAO,EAAGC,CAAC,IAAK;cACd,IAAIZ,KAAK,CAACa,OAAO,IAAI,CAACb,KAAK,CAACa,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;gBAC7D,IAAIC,OAAO,GAAGf,KAAK,CAACa,OAAO;gBAC3B,MAAMG,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;gBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;gBACpC,MAAME,SAAS,GAAG,CACf,8BAA6BF,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;gBACD,MAAMG,UAAU,GAAGN,CAAC,CAACO,MAAM,CAACb,GAAG;gBAC/B,MAAMc,YAAY,GAAGH,SAAS,CAACI,SAAS,CAACC,GAAG,IAAIJ,UAAU,CAACJ,QAAQ,CAACQ,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1F,IAAIJ,YAAY,GAAGH,SAAS,CAACQ,MAAM,GAAG,CAAC,EAAE;kBACvCb,CAAC,CAACO,MAAM,CAACb,GAAG,GAAGW,SAAS,CAACG,YAAY,GAAG,CAAC,CAAC;gBAC5C,CAAC,MAAM;kBACLR,CAAC,CAACO,MAAM,CAACb,GAAG,GAAG,0BAA0B;gBAC3C;cACF,CAAC,MAAM;gBACLM,CAAC,CAACO,MAAM,CAACb,GAAG,GAAG,0BAA0B;cAC3C;YACF;UAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFlC,OAAA;YAAKC,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BF,OAAA,CAACmC,YAAY;cAAClC,SAAS,EAAC;YAAW;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACNlC,OAAA;YAAKC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BG,KAAK,CAAC+B,QAAQ,IAAI;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,EACL7B,KAAK,CAACgC,SAAS,IAAIhC,KAAK,CAACgC,SAAS,CAACP,MAAM,GAAG,CAAC,iBAC5C9B,OAAA;YAAKC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BF,OAAA,CAACsC,YAAY;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,MAElB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENlC,OAAA;UAAKC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCF,OAAA;YAAIC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEG,KAAK,CAACS;UAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9ClC,OAAA;YAAKC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBF,OAAA;cAAMC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEqC,cAAc,CAAClC,KAAK,CAACmC,OAAO;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtElC,OAAA;cAAMC,SAAS,EAAC,aAAa;cAAAC,QAAA,EAC1BuC,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEC,WAAW,GAAI,aAAYrC,KAAK,CAACJ,SAAS,IAAII,KAAK,CAACsC,KAAM,EAAC,GAAI,SAAQtC,KAAK,CAACJ,SAAS,IAAII,KAAK,CAACsC,KAAM,EAAC,GACvG,QAAOtC,KAAK,CAACJ,SAAS,IAAII,KAAK,CAACsC,KAAM;YAAC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNlC,OAAA;YAAKC,SAAS,EAAC,YAAY;YAAAC,QAAA,GACxBG,KAAK,CAACuC,KAAK,iBAAI5C,OAAA;cAAMC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEG,KAAK,CAACuC;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC/D7B,KAAK,CAACwC,eAAe,IAAIxC,KAAK,CAACwC,eAAe,MAAMxC,KAAK,CAACJ,SAAS,IAAII,KAAK,CAACsC,KAAK,CAAC,iBAClF3C,OAAA;cAAMC,SAAS,EAAC,YAAY;cAAAC,QAAA,GACzBwC,WAAW,GAAG,qBAAqB,GAAG,cAAc,EACpDD,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEC,WAAW,GAAI,aAAYrC,KAAK,CAACwC,eAAgB,EAAC,GAAI,SAAQxC,KAAK,CAACwC,eAAgB,EAAC,GACrF,QAAOxC,KAAK,CAACwC,eAAgB,EAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLY,iBAAiB,KAAKxC,KAAK,iBAC1BN,OAAA;QAAKC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClCF,OAAA;UACE+C,GAAG,EAAGA,GAAG,IAAKC,WAAW,CAACD,GAAG,CAAE;UAC/BE,QAAQ;UACRC,QAAQ;UACRC,WAAW;UACXC,OAAO,EAAC,UAAU;UAClBC,KAAK,EAAC,MAAM;UACZC,MAAM,EAAC,MAAM;UACbC,MAAM,EAAE3C,eAAe,CAACP,KAAK,CAAE;UAC/BmD,KAAK,EAAE;YACLH,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdG,eAAe,EAAE,MAAM;YACvBC,SAAS,EAAE;UACb,CAAE;UACF1C,OAAO,EAAGC,CAAC,IAAK0C,aAAa,CAAE,yBAAwBtD,KAAK,CAACS,KAAM,EAAC,CAAE;UACtE8C,SAAS,EAAEA,CAAA,KAAMD,aAAa,CAAC,IAAI,CAAE;UACrCE,WAAW,EAAC,WAAW;UAAA3D,QAAA,gBAEvBF,OAAA;YAAQW,GAAG,EAAEN,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,QAAS;YAACC,IAAI,EAAC;UAAW;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACvE7B,KAAK,CAACgC,SAAS,IAAIhC,KAAK,CAACgC,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIzB,KAAK,CAACgC,SAAS,CAACjC,GAAG,CAAC,CAAC6D,QAAQ,EAAE3D,KAAK,kBACpFN,OAAA;YAEEkE,IAAI,EAAC,WAAW;YAChBvD,GAAG,EAAEsD,QAAQ,CAACtC,GAAI;YAClBwC,OAAO,EAAEF,QAAQ,CAACG,QAAS;YAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;YAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAIlE,KAAK,KAAK;UAAE,GALrC,GAAE2D,QAAQ,CAACG,QAAS,IAAG9D,KAAM,EAAC;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC,GA5Ga5B,KAAK;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA6GV,CACjB;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}