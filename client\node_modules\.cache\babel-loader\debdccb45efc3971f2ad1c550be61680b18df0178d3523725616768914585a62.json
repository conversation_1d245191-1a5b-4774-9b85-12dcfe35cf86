{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoLessons = () => {\n  _s();\n  // Redux state with safe destructuring\n  const usersState = useSelector(state => state.users || {});\n  const user = usersState.user || null;\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = video => {\n    if (video.thumbnailUrl) return video.thumbnailUrl;\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      var _video$videoID$match;\n      const videoId = video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be') ? ((_video$videoID$match = video.videoID.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/)) === null || _video$videoID$match === void 0 ? void 0 : _video$videoID$match[1]) || video.videoID : video.videoID;\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/320/180';\n  };\n  const getSubjectName = subject => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      console.log('Fetching videos with filters:', {\n        level: selectedLevel,\n        class: selectedClass !== 'all' ? selectedClass : undefined,\n        subject: selectedSubject !== 'all' ? selectedSubject : undefined,\n        type: 'video'\n      });\n\n      // Use the proper API call for study materials (videos)\n      const filters = {\n        level: selectedLevel,\n        class: selectedClass !== 'all' ? selectedClass : undefined,\n        subject: selectedSubject !== 'all' ? selectedSubject : undefined,\n        type: 'video' // Filter for videos only\n      };\n\n      const response = await getStudyMaterial(filters);\n      console.log('API Response:', response);\n      if (response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        var _response$data$data;\n        setVideos(response.data.data || []);\n        console.log('Videos loaded:', ((_response$data$data = response.data.data) === null || _response$data$data === void 0 ? void 0 : _response$data$data.length) || 0);\n      } else {\n        console.error('API Error:', response);\n        // For now, let's use mock data to test the UI\n        const mockVideos = [{\n          id: '1',\n          title: 'Introduction to Mathematics',\n          subject: 'mathematics',\n          className: '1',\n          level: 'primary',\n          videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',\n          thumbnailUrl: '/api/placeholder/320/180',\n          duration: '10:30',\n          topic: 'Numbers',\n          subtitles: []\n        }, {\n          id: '2',\n          title: 'Basic English Grammar',\n          subject: 'english',\n          className: '2',\n          level: 'primary',\n          videoID: 'dQw4w9WgXcQ',\n          // YouTube video ID\n          duration: '15:45',\n          topic: 'Grammar',\n          subtitles: [{\n            language: 'en',\n            languageName: 'English',\n            url: '',\n            isDefault: true\n          }]\n        }];\n        setVideos(mockVideos);\n        console.log('Using mock videos due to API error');\n      }\n    } catch (err) {\n      console.error('Fetch error:', err);\n      setError(err.message || 'Failed to load videos');\n\n      // Use mock data as fallback\n      const mockVideos = [{\n        id: '1',\n        title: 'Sample Video Lesson',\n        subject: 'mathematics',\n        className: '1',\n        level: 'primary',\n        videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',\n        thumbnailUrl: '/api/placeholder/320/180',\n        duration: '10:30',\n        topic: 'Sample Topic',\n        subtitles: []\n      }];\n      setVideos(mockVideos);\n      console.log('Using fallback mock videos');\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      var _video$title, _video$subject, _video$topic;\n      const matchesSearch = !searchTerm || ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isKiswahili ? 'Masomo ya Video' : 'Video Lessons'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: isKiswahili ? 'Tafuta video...' : 'Search videos...',\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLevel,\n          onChange: e => setSelectedLevel(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"primary\",\n            children: isKiswahili ? 'Msingi' : 'Primary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"secondary\",\n            children: isKiswahili ? 'Sekondari' : 'Secondary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advance\",\n            children: isKiswahili ? 'Juu' : 'Advanced'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedClass,\n          onChange: e => setSelectedClass(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedSubject,\n          onChange: e => setSelectedSubject(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card\",\n            onClick: () => handleShowVideo(index),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card-thumbnail\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getThumbnailUrl(video),\n                alt: video.title,\n                className: \"thumbnail-image\",\n                loading: \"lazy\",\n                onError: e => {\n                  if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                    let videoId = video.videoID;\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                    const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                    const currentSrc = e.target.src;\n                    const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                    if (currentIndex < fallbacks.length - 1) {\n                      e.target.src = fallbacks[currentIndex + 1];\n                    } else {\n                      e.target.src = '/api/placeholder/320/180';\n                    }\n                  } else {\n                    e.target.src = '/api/placeholder/320/180';\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"play-overlay\",\n                children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                  className: \"play-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-duration\",\n                children: video.duration || \"Video\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 21\n              }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"subtitle-badge\",\n                children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 25\n                }, this), \"CC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"video-title\",\n                children: video.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-subject\",\n                  children: getSubjectName(video.subject)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-class\",\n                  children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-tags\",\n                children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"topic-tag\",\n                  children: video.topic\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 39\n                }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"shared-tag\",\n                  children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 17\n          }, this), currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-video-player\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"youtube-style-layout\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-player\",\n                children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                  ref: ref => setVideoRef(ref),\n                  controls: true,\n                  autoPlay: true,\n                  playsInline: true,\n                  preload: \"metadata\",\n                  width: \"100%\",\n                  height: \"100%\",\n                  poster: getThumbnailUrl(video),\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    backgroundColor: '#000',\n                    objectFit: 'contain'\n                  },\n                  onError: e => setVideoError(`Failed to load video: ${video.title}`),\n                  onCanPlay: () => setVideoError(null),\n                  crossOrigin: \"anonymous\",\n                  children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                    src: video.signedVideoUrl || video.videoUrl,\n                    type: \"video/mp4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 29\n                  }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                    kind: \"subtitles\",\n                    src: subtitle.url,\n                    srcLang: subtitle.language,\n                    label: subtitle.languageName,\n                    default: subtitle.isDefault || index === 0\n                  }, `${subtitle.language}-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 31\n                  }, this)), \"Your browser does not support the video tag.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 27\n                }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                  src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                  title: video.title,\n                  frameBorder: \"0\",\n                  allowFullScreen: true,\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    border: 'none'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-icon\",\n                    children: \"\\u26A0\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: \"Video Unavailable\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: videoError || \"This video cannot be played at the moment.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"youtube-video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u2022\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Class \", video.className || video.class]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-actions\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"youtube-action-btn\",\n                    onClick: () => setCurrentVideoIndex(null),\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2715\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Close\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 397,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 19\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoLessons, \"r2SMMcgtsI2PFivZCCs/s04bnno=\", false, function () {\n  return [useSelector];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useSelector", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "TbAlertTriangle", "MdVerified", "getStudyMaterial", "getAllVideos", "jsxDEV", "_jsxDEV", "VideoLessons", "_s", "usersState", "state", "users", "user", "videos", "setVideos", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "currentVideoIndex", "setCurrentVideoIndex", "videoRef", "setVideoRef", "videoError", "setVideoError", "isKiswahili", "getThumbnailUrl", "video", "thumbnailUrl", "videoID", "includes", "_video$videoID$match", "videoId", "match", "getSubjectName", "subject", "subjectMap", "handleShowVideo", "index", "filteredAndSortedVideos", "videoUrl", "signedVideoUrl", "console", "warn", "fetchVideos", "_response$data", "log", "level", "class", "undefined", "type", "filters", "response", "data", "success", "_response$data$data", "length", "mockVideos", "id", "title", "className", "duration", "topic", "subtitles", "language", "languageName", "url", "isDefault", "err", "message", "filtered", "filter", "_video$title", "_video$subject", "_video$topic", "matchesSearch", "toLowerCase", "matchesLevel", "matchesClass", "matchesSubject", "sort", "a", "b", "Date", "createdAt", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "e", "target", "onClick", "map", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "split", "pop", "sharedFromClass", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "style", "backgroundColor", "objectFit", "onCanPlay", "crossOrigin", "subtitle", "kind", "srcLang", "label", "default", "frameBorder", "allowFullScreen", "border", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\n\nconst VideoLessons = () => {\n  // Redux state with safe destructuring\n  const usersState = useSelector(state => state.users || {});\n  const user = usersState.user || null;\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnailUrl) return video.thumbnailUrl;\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      const videoId = video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be')\n        ? video.videoID.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/)?.[1] || video.videoID\n        : video.videoID;\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/320/180';\n  };\n\n  const getSubjectName = (subject) => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('Fetching videos with filters:', {\n        level: selectedLevel,\n        class: selectedClass !== 'all' ? selectedClass : undefined,\n        subject: selectedSubject !== 'all' ? selectedSubject : undefined,\n        type: 'video'\n      });\n\n      // Use the proper API call for study materials (videos)\n      const filters = {\n        level: selectedLevel,\n        class: selectedClass !== 'all' ? selectedClass : undefined,\n        subject: selectedSubject !== 'all' ? selectedSubject : undefined,\n        type: 'video' // Filter for videos only\n      };\n\n      const response = await getStudyMaterial(filters);\n      console.log('API Response:', response);\n\n      if (response?.data?.success) {\n        setVideos(response.data.data || []);\n        console.log('Videos loaded:', response.data.data?.length || 0);\n      } else {\n        console.error('API Error:', response);\n        // For now, let's use mock data to test the UI\n        const mockVideos = [\n          {\n            id: '1',\n            title: 'Introduction to Mathematics',\n            subject: 'mathematics',\n            className: '1',\n            level: 'primary',\n            videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            thumbnailUrl: '/api/placeholder/320/180',\n            duration: '10:30',\n            topic: 'Numbers',\n            subtitles: []\n          },\n          {\n            id: '2',\n            title: 'Basic English Grammar',\n            subject: 'english',\n            className: '2',\n            level: 'primary',\n            videoID: 'dQw4w9WgXcQ', // YouTube video ID\n            duration: '15:45',\n            topic: 'Grammar',\n            subtitles: [{ language: 'en', languageName: 'English', url: '', isDefault: true }]\n          }\n        ];\n        setVideos(mockVideos);\n        console.log('Using mock videos due to API error');\n      }\n    } catch (err) {\n      console.error('Fetch error:', err);\n      setError(err.message || 'Failed to load videos');\n\n      // Use mock data as fallback\n      const mockVideos = [\n        {\n          id: '1',\n          title: 'Sample Video Lesson',\n          subject: 'mathematics',\n          className: '1',\n          level: 'primary',\n          videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',\n          thumbnailUrl: '/api/placeholder/320/180',\n          duration: '10:30',\n          topic: 'Sample Topic',\n          subtitles: []\n        }\n      ];\n      setVideos(mockVideos);\n      console.log('Using fallback mock videos');\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      const matchesSearch = !searchTerm ||\n        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());\n\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n\n  return (\n    <div className=\"video-lessons-container\">\n      <div className=\"video-lessons-header\">\n        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>\n        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"video-controls\">\n        <div className=\"search-section\">\n          <input\n            type=\"text\"\n            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n\n        <div className=\"filter-section\">\n          <select\n            value={selectedLevel}\n            onChange={(e) => setSelectedLevel(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"primary\">{isKiswahili ? 'Msingi' : 'Primary'}</option>\n            <option value=\"secondary\">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>\n            <option value=\"advance\">{isKiswahili ? 'Juu' : 'Advanced'}</option>\n          </select>\n\n          <select\n            value={selectedClass}\n            onChange={(e) => setSelectedClass(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n            {/* Add class options based on selected level */}\n          </select>\n\n          <select\n            value={selectedSubject}\n            onChange={(e) => setSelectedSubject(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n            {/* Add subject options */}\n          </select>\n        </div>\n      </div>\n\n      {/* Video Content */}\n      <div className=\"video-content\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-item\">\n                {/* Video Card */}\n                <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                  <div className=\"video-card-thumbnail\">\n                    <img\n                      src={getThumbnailUrl(video)}\n                      alt={video.title}\n                      className=\"thumbnail-image\"\n                      loading=\"lazy\"\n                      onError={(e) => {\n                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                          let videoId = video.videoID;\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                          const fallbacks = [\n                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                            '/api/placeholder/320/180'\n                          ];\n                          const currentSrc = e.target.src;\n                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                          if (currentIndex < fallbacks.length - 1) {\n                            e.target.src = fallbacks[currentIndex + 1];\n                          } else {\n                            e.target.src = '/api/placeholder/320/180';\n                          }\n                        } else {\n                          e.target.src = '/api/placeholder/320/180';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                    </div>\n                    <div className=\"video-duration\">\n                      {video.duration || \"Video\"}\n                    </div>\n                    {video.subtitles && video.subtitles.length > 0 && (\n                      <div className=\"subtitle-badge\">\n                        <TbInfoCircle />\n                        CC\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"video-card-content\">\n                    <h3 className=\"video-title\">{video.title}</h3>\n                    <div className=\"video-meta\">\n                      <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                      <span className=\"video-class\">\n                        {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                          ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)\n                          : `Form ${video.className || video.class}`}\n                      </span>\n                    </div>\n                    <div className=\"video-tags\">\n                      {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                      {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                        <span className=\"shared-tag\">\n                          {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}\n                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                            ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)\n                            : `Form ${video.sharedFromClass}`}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Inline Video Player */}\n                {currentVideoIndex === index && (\n                  <div className=\"inline-video-player\">\n                    <div className=\"youtube-style-layout\">\n                      <div className=\"youtube-video-player\">\n                        {video.videoUrl ? (\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"100%\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            }}\n                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}\n                            onCanPlay={() => setVideoError(null)}\n                            crossOrigin=\"anonymous\"\n                          >\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n                            Your browser does not support the video tag.\n                          </video>\n                        ) : video.videoID ? (\n                          <iframe\n                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                            title={video.title}\n                            frameBorder=\"0\"\n                            allowFullScreen\n                            style={{ width: '100%', height: '100%', border: 'none' }}\n                          ></iframe>\n                        ) : (\n                          <div className=\"video-error\">\n                            <div className=\"error-icon\">⚠️</div>\n                            <h3>Video Unavailable</h3>\n                            <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"youtube-video-info\">\n                        <h1 className=\"youtube-video-title\">{video.title}</h1>\n                        <div className=\"youtube-video-meta\">\n                          <span>{getSubjectName(video.subject)}</span>\n                          <span>•</span>\n                          <span>Class {video.className || video.class}</span>\n                        </div>\n                        <div className=\"youtube-video-actions\">\n                          <button\n                            className=\"youtube-action-btn\"\n                            onClick={() => setCurrentVideoIndex(null)}\n                          >\n                            <span>✕</span>\n                            <span>Close</span>\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAMC,UAAU,GAAGZ,WAAW,CAACa,KAAK,IAAIA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAMC,IAAI,GAAGH,UAAU,CAACG,IAAI,IAAI,IAAI;;EAEpC;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAMwC,WAAW,GAAGZ,aAAa,KAAK,mBAAmB;;EAEzD;EACA,MAAMa,eAAe,GAAIC,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACC,YAAY,EAAE,OAAOD,KAAK,CAACC,YAAY;IACjD,IAAID,KAAK,CAACE,OAAO,IAAI,CAACF,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;MAAA,IAAAC,oBAAA;MAC7D,MAAMC,OAAO,GAAGL,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIH,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,UAAU,CAAC,GACvF,EAAAC,oBAAA,GAAAJ,KAAK,CAACE,OAAO,CAACI,KAAK,CAAC,oDAAoD,CAAC,cAAAF,oBAAA,uBAAzEA,oBAAA,CAA4E,CAAC,CAAC,KAAIJ,KAAK,CAACE,OAAO,GAC/FF,KAAK,CAACE,OAAO;MACjB,OAAQ,8BAA6BG,OAAQ,oBAAmB;IAClE;IACA,OAAO,0BAA0B;EACnC,CAAC;EAED,MAAME,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,UAAU,GAAG;MACjB,aAAa,EAAEX,WAAW,GAAG,UAAU,GAAG,aAAa;MACvD,SAAS,EAAEA,WAAW,GAAG,YAAY,GAAG,SAAS;MACjD,WAAW,EAAE,WAAW;MACxB,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG,SAAS;MAC9C,gBAAgB,EAAEA,WAAW,GAAG,kBAAkB,GAAG,gBAAgB;MACrE,QAAQ,EAAEA,WAAW,GAAG,OAAO,GAAG,QAAQ;MAC1C,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,WAAW,GAAG,WAAW;MACpD,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,OAAO,GAAG,WAAW;MAChD,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG;IACvC,CAAC;IACD,OAAOW,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO;EACvC,CAAC;;EAED;EACA,MAAME,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMX,KAAK,GAAGY,uBAAuB,CAACD,KAAK,CAAC;IAC5ClB,oBAAoB,CAACkB,KAAK,CAAC;IAC3Bd,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIG,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEa,QAAQ,KAAKb,KAAK,CAACa,QAAQ,CAACV,QAAQ,CAAC,eAAe,CAAC,IAAIH,KAAK,CAACa,QAAQ,CAACV,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF;QACA;QACA;QACAH,KAAK,CAACc,cAAc,GAAGd,KAAK,CAACa,QAAQ;MACvC,CAAC,CAAC,OAAO/B,KAAK,EAAE;QACdiC,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;QAC5DhB,KAAK,CAACc,cAAc,GAAGd,KAAK,CAACa,QAAQ;MACvC;IACF;EACF,CAAC;;EAED;EACA,MAAMI,WAAW,GAAGxD,WAAW,CAAC,YAAY;IAC1C,IAAI;MAAA,IAAAyD,cAAA;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEdgC,OAAO,CAACI,GAAG,CAAC,+BAA+B,EAAE;QAC3CC,KAAK,EAAElC,aAAa;QACpBmC,KAAK,EAAEjC,aAAa,KAAK,KAAK,GAAGA,aAAa,GAAGkC,SAAS;QAC1Dd,OAAO,EAAElB,eAAe,KAAK,KAAK,GAAGA,eAAe,GAAGgC,SAAS;QAChEC,IAAI,EAAE;MACR,CAAC,CAAC;;MAEF;MACA,MAAMC,OAAO,GAAG;QACdJ,KAAK,EAAElC,aAAa;QACpBmC,KAAK,EAAEjC,aAAa,KAAK,KAAK,GAAGA,aAAa,GAAGkC,SAAS;QAC1Dd,OAAO,EAAElB,eAAe,KAAK,KAAK,GAAGA,eAAe,GAAGgC,SAAS;QAChEC,IAAI,EAAE,OAAO,CAAC;MAChB,CAAC;;MAED,MAAME,QAAQ,GAAG,MAAMzD,gBAAgB,CAACwD,OAAO,CAAC;MAChDT,OAAO,CAACI,GAAG,CAAC,eAAe,EAAEM,QAAQ,CAAC;MAEtC,IAAIA,QAAQ,aAARA,QAAQ,gBAAAP,cAAA,GAARO,QAAQ,CAAEC,IAAI,cAAAR,cAAA,eAAdA,cAAA,CAAgBS,OAAO,EAAE;QAAA,IAAAC,mBAAA;QAC3BjD,SAAS,CAAC8C,QAAQ,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QACnCX,OAAO,CAACI,GAAG,CAAC,gBAAgB,EAAE,EAAAS,mBAAA,GAAAH,QAAQ,CAACC,IAAI,CAACA,IAAI,cAAAE,mBAAA,uBAAlBA,mBAAA,CAAoBC,MAAM,KAAI,CAAC,CAAC;MAChE,CAAC,MAAM;QACLd,OAAO,CAACjC,KAAK,CAAC,YAAY,EAAE2C,QAAQ,CAAC;QACrC;QACA,MAAMK,UAAU,GAAG,CACjB;UACEC,EAAE,EAAE,GAAG;UACPC,KAAK,EAAE,6BAA6B;UACpCxB,OAAO,EAAE,aAAa;UACtByB,SAAS,EAAE,GAAG;UACdb,KAAK,EAAE,SAAS;UAChBP,QAAQ,EAAE,4CAA4C;UACtDZ,YAAY,EAAE,0BAA0B;UACxCiC,QAAQ,EAAE,OAAO;UACjBC,KAAK,EAAE,SAAS;UAChBC,SAAS,EAAE;QACb,CAAC,EACD;UACEL,EAAE,EAAE,GAAG;UACPC,KAAK,EAAE,uBAAuB;UAC9BxB,OAAO,EAAE,SAAS;UAClByB,SAAS,EAAE,GAAG;UACdb,KAAK,EAAE,SAAS;UAChBlB,OAAO,EAAE,aAAa;UAAE;UACxBgC,QAAQ,EAAE,OAAO;UACjBC,KAAK,EAAE,SAAS;UAChBC,SAAS,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,YAAY,EAAE,SAAS;YAAEC,GAAG,EAAE,EAAE;YAAEC,SAAS,EAAE;UAAK,CAAC;QACnF,CAAC,CACF;QACD7D,SAAS,CAACmD,UAAU,CAAC;QACrBf,OAAO,CAACI,GAAG,CAAC,oCAAoC,CAAC;MACnD;IACF,CAAC,CAAC,OAAOsB,GAAG,EAAE;MACZ1B,OAAO,CAACjC,KAAK,CAAC,cAAc,EAAE2D,GAAG,CAAC;MAClC1D,QAAQ,CAAC0D,GAAG,CAACC,OAAO,IAAI,uBAAuB,CAAC;;MAEhD;MACA,MAAMZ,UAAU,GAAG,CACjB;QACEC,EAAE,EAAE,GAAG;QACPC,KAAK,EAAE,qBAAqB;QAC5BxB,OAAO,EAAE,aAAa;QACtByB,SAAS,EAAE,GAAG;QACdb,KAAK,EAAE,SAAS;QAChBP,QAAQ,EAAE,4CAA4C;QACtDZ,YAAY,EAAE,0BAA0B;QACxCiC,QAAQ,EAAE,OAAO;QACjBC,KAAK,EAAE,cAAc;QACrBC,SAAS,EAAE;MACb,CAAC,CACF;MACDzD,SAAS,CAACmD,UAAU,CAAC;MACrBf,OAAO,CAACI,GAAG,CAAC,4BAA4B,CAAC;IAC3C,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACK,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEnD;EACA,MAAMsB,uBAAuB,GAAGpD,OAAO,CAAC,MAAM;IAC5C,IAAImF,QAAQ,GAAGjE,MAAM,CAACkE,MAAM,CAAC5C,KAAK,IAAI;MAAA,IAAA6C,YAAA,EAAAC,cAAA,EAAAC,YAAA;MACpC,MAAMC,aAAa,GAAG,CAAChE,UAAU,MAAA6D,YAAA,GAC/B7C,KAAK,CAACgC,KAAK,cAAAa,YAAA,uBAAXA,YAAA,CAAaI,WAAW,CAAC,CAAC,CAAC9C,QAAQ,CAACnB,UAAU,CAACiE,WAAW,CAAC,CAAC,CAAC,OAAAH,cAAA,GAC7D9C,KAAK,CAACQ,OAAO,cAAAsC,cAAA,uBAAbA,cAAA,CAAeG,WAAW,CAAC,CAAC,CAAC9C,QAAQ,CAACnB,UAAU,CAACiE,WAAW,CAAC,CAAC,CAAC,OAAAF,YAAA,GAC/D/C,KAAK,CAACmC,KAAK,cAAAY,YAAA,uBAAXA,YAAA,CAAaE,WAAW,CAAC,CAAC,CAAC9C,QAAQ,CAACnB,UAAU,CAACiE,WAAW,CAAC,CAAC,CAAC;MAE/D,MAAMC,YAAY,GAAGhE,aAAa,KAAK,KAAK,IAAIc,KAAK,CAACoB,KAAK,KAAKlC,aAAa;MAC7E,MAAMiE,YAAY,GAAG/D,aAAa,KAAK,KAAK,IAAIY,KAAK,CAACiC,SAAS,KAAK7C,aAAa,IAAIY,KAAK,CAACqB,KAAK,KAAKjC,aAAa;MAClH,MAAMgE,cAAc,GAAG9D,eAAe,KAAK,KAAK,IAAIU,KAAK,CAACQ,OAAO,KAAKlB,eAAe;MAErF,OAAO0D,aAAa,IAAIE,YAAY,IAAIC,YAAY,IAAIC,cAAc;IACxE,CAAC,CAAC;IAEF,OAAOT,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAAC;EAC/E,CAAC,EAAE,CAAC/E,MAAM,EAAEM,UAAU,EAAEE,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEvE;EACA/B,SAAS,CAAC,MAAM;IACd0D,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA1D,SAAS,CAAC,MAAM;IACd0D,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAC/B,aAAa,EAAEE,aAAa,EAAEE,eAAe,EAAE2B,WAAW,CAAC,CAAC;EAEhE,oBACE9C,OAAA;IAAK8D,SAAS,EAAC,yBAAyB;IAAAyB,QAAA,gBACtCvF,OAAA;MAAK8D,SAAS,EAAC,sBAAsB;MAAAyB,QAAA,gBACnCvF,OAAA;QAAAuF,QAAA,EAAK5D,WAAW,GAAG,iBAAiB,GAAG;MAAe;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5D3F,OAAA;QAAAuF,QAAA,EAAI5D,WAAW,GAAG,iCAAiC,GAAG;MAAoC;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAGN3F,OAAA;MAAK8D,SAAS,EAAC,gBAAgB;MAAAyB,QAAA,gBAC7BvF,OAAA;QAAK8D,SAAS,EAAC,gBAAgB;QAAAyB,QAAA,eAC7BvF,OAAA;UACEoD,IAAI,EAAC,MAAM;UACXwC,WAAW,EAAEjE,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;UAClEkE,KAAK,EAAEhF,UAAW;UAClBiF,QAAQ,EAAGC,CAAC,IAAKjF,aAAa,CAACiF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/C/B,SAAS,EAAC;QAAc;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN3F,OAAA;QAAK8D,SAAS,EAAC,gBAAgB;QAAAyB,QAAA,gBAC7BvF,OAAA;UACE6F,KAAK,EAAE9E,aAAc;UACrB+E,QAAQ,EAAGC,CAAC,IAAK/E,gBAAgB,CAAC+E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAClD/B,SAAS,EAAC,eAAe;UAAAyB,QAAA,gBAEzBvF,OAAA;YAAQ6F,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAE5D,WAAW,GAAG,QAAQ,GAAG;UAAS;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrE3F,OAAA;YAAQ6F,KAAK,EAAC,WAAW;YAAAN,QAAA,EAAE5D,WAAW,GAAG,WAAW,GAAG;UAAW;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5E3F,OAAA;YAAQ6F,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAE5D,WAAW,GAAG,KAAK,GAAG;UAAU;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAET3F,OAAA;UACE6F,KAAK,EAAE5E,aAAc;UACrB6E,QAAQ,EAAGC,CAAC,IAAK7E,gBAAgB,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAClD/B,SAAS,EAAC,eAAe;UAAAyB,QAAA,eAEzBvF,OAAA;YAAQ6F,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAE5D,WAAW,GAAG,eAAe,GAAG;UAAa;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtE,CAAC,eAET3F,OAAA;UACE6F,KAAK,EAAE1E,eAAgB;UACvB2E,QAAQ,EAAGC,CAAC,IAAK3E,kBAAkB,CAAC2E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpD/B,SAAS,EAAC,eAAe;UAAAyB,QAAA,eAEzBvF,OAAA;YAAQ6F,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAE5D,WAAW,GAAG,aAAa,GAAG;UAAc;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3F,OAAA;MAAK8D,SAAS,EAAC,eAAe;MAAAyB,QAAA,EAC3B9E,OAAO,gBACNT,OAAA;QAAK8D,SAAS,EAAC,eAAe;QAAAyB,QAAA,gBAC5BvF,OAAA;UAAK8D,SAAS,EAAC;QAAiB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC3F,OAAA;UAAAuF,QAAA,EAAI5D,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJhF,KAAK,gBACPX,OAAA;QAAK8D,SAAS,EAAC,aAAa;QAAAyB,QAAA,gBAC1BvF,OAAA,CAACL,eAAe;UAACmE,SAAS,EAAC;QAAY;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1C3F,OAAA;UAAAuF,QAAA,EAAK5D,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7E3F,OAAA;UAAAuF,QAAA,EAAI5E;QAAK;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd3F,OAAA;UAAQiG,OAAO,EAAEnD,WAAY;UAACgB,SAAS,EAAC,WAAW;UAAAyB,QAAA,EAChD5D,WAAW,GAAG,aAAa,GAAG;QAAW;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJlD,uBAAuB,CAACiB,MAAM,GAAG,CAAC,gBACpC1D,OAAA;QAAK8D,SAAS,EAAC,aAAa;QAAAyB,QAAA,EACzB9C,uBAAuB,CAACyD,GAAG,CAAC,CAACrE,KAAK,EAAEW,KAAK,kBACxCxC,OAAA;UAAiB8D,SAAS,EAAC,YAAY;UAAAyB,QAAA,gBAErCvF,OAAA;YAAK8D,SAAS,EAAC,YAAY;YAACmC,OAAO,EAAEA,CAAA,KAAM1D,eAAe,CAACC,KAAK,CAAE;YAAA+C,QAAA,gBAChEvF,OAAA;cAAK8D,SAAS,EAAC,sBAAsB;cAAAyB,QAAA,gBACnCvF,OAAA;gBACEmG,GAAG,EAAEvE,eAAe,CAACC,KAAK,CAAE;gBAC5BuE,GAAG,EAAEvE,KAAK,CAACgC,KAAM;gBACjBC,SAAS,EAAC,iBAAiB;gBAC3BrD,OAAO,EAAC,MAAM;gBACd4F,OAAO,EAAGN,CAAC,IAAK;kBACd,IAAIlE,KAAK,CAACE,OAAO,IAAI,CAACF,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;oBAC7D,IAAIE,OAAO,GAAGL,KAAK,CAACE,OAAO;oBAC3B,MAAMI,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;oBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;oBACpC,MAAMoE,SAAS,GAAG,CACf,8BAA6BpE,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;oBACD,MAAMqE,UAAU,GAAGR,CAAC,CAACC,MAAM,CAACG,GAAG;oBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACrC,GAAG,IAAImC,UAAU,CAACvE,QAAQ,CAACoC,GAAG,CAACsC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC1F,IAAIH,YAAY,GAAGF,SAAS,CAAC5C,MAAM,GAAG,CAAC,EAAE;sBACvCqC,CAAC,CAACC,MAAM,CAACG,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;oBAC5C,CAAC,MAAM;sBACLT,CAAC,CAACC,MAAM,CAACG,GAAG,GAAG,0BAA0B;oBAC3C;kBACF,CAAC,MAAM;oBACLJ,CAAC,CAACC,MAAM,CAACG,GAAG,GAAG,0BAA0B;kBAC3C;gBACF;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF3F,OAAA;gBAAK8D,SAAS,EAAC,cAAc;gBAAAyB,QAAA,eAC3BvF,OAAA,CAACR,YAAY;kBAACsE,SAAS,EAAC;gBAAW;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACN3F,OAAA;gBAAK8D,SAAS,EAAC,gBAAgB;gBAAAyB,QAAA,EAC5B1D,KAAK,CAACkC,QAAQ,IAAI;cAAO;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,EACL9D,KAAK,CAACoC,SAAS,IAAIpC,KAAK,CAACoC,SAAS,CAACP,MAAM,GAAG,CAAC,iBAC5C1D,OAAA;gBAAK8D,SAAS,EAAC,gBAAgB;gBAAAyB,QAAA,gBAC7BvF,OAAA,CAACN,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,MAElB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN3F,OAAA;cAAK8D,SAAS,EAAC,oBAAoB;cAAAyB,QAAA,gBACjCvF,OAAA;gBAAI8D,SAAS,EAAC,aAAa;gBAAAyB,QAAA,EAAE1D,KAAK,CAACgC;cAAK;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9C3F,OAAA;gBAAK8D,SAAS,EAAC,YAAY;gBAAAyB,QAAA,gBACzBvF,OAAA;kBAAM8D,SAAS,EAAC,eAAe;kBAAAyB,QAAA,EAAEnD,cAAc,CAACP,KAAK,CAACQ,OAAO;gBAAC;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtE3F,OAAA;kBAAM8D,SAAS,EAAC,aAAa;kBAAAyB,QAAA,EAC1BxE,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEY,WAAW,GAAI,aAAYE,KAAK,CAACiC,SAAS,IAAIjC,KAAK,CAACqB,KAAM,EAAC,GAAI,SAAQrB,KAAK,CAACiC,SAAS,IAAIjC,KAAK,CAACqB,KAAM,EAAC,GACvG,QAAOrB,KAAK,CAACiC,SAAS,IAAIjC,KAAK,CAACqB,KAAM;gBAAC;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN3F,OAAA;gBAAK8D,SAAS,EAAC,YAAY;gBAAAyB,QAAA,GACxB1D,KAAK,CAACmC,KAAK,iBAAIhE,OAAA;kBAAM8D,SAAS,EAAC,WAAW;kBAAAyB,QAAA,EAAE1D,KAAK,CAACmC;gBAAK;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/D9D,KAAK,CAAC+E,eAAe,IAAI/E,KAAK,CAAC+E,eAAe,MAAM/E,KAAK,CAACiC,SAAS,IAAIjC,KAAK,CAACqB,KAAK,CAAC,iBAClFlD,OAAA;kBAAM8D,SAAS,EAAC,YAAY;kBAAAyB,QAAA,GACzB5D,WAAW,GAAG,qBAAqB,GAAG,cAAc,EACpDZ,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEY,WAAW,GAAI,aAAYE,KAAK,CAAC+E,eAAgB,EAAC,GAAI,SAAQ/E,KAAK,CAAC+E,eAAgB,EAAC,GACrF,QAAO/E,KAAK,CAAC+E,eAAgB,EAAC;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLtE,iBAAiB,KAAKmB,KAAK,iBAC1BxC,OAAA;YAAK8D,SAAS,EAAC,qBAAqB;YAAAyB,QAAA,eAClCvF,OAAA;cAAK8D,SAAS,EAAC,sBAAsB;cAAAyB,QAAA,gBACnCvF,OAAA;gBAAK8D,SAAS,EAAC,sBAAsB;gBAAAyB,QAAA,EAClC1D,KAAK,CAACa,QAAQ,gBACb1C,OAAA;kBACE6G,GAAG,EAAGA,GAAG,IAAKrF,WAAW,CAACqF,GAAG,CAAE;kBAC/BC,QAAQ;kBACRC,QAAQ;kBACRC,WAAW;kBACXC,OAAO,EAAC,UAAU;kBAClBC,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAC,MAAM;kBACbC,MAAM,EAAExF,eAAe,CAACC,KAAK,CAAE;kBAC/BwF,KAAK,EAAE;oBACLH,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdG,eAAe,EAAE,MAAM;oBACvBC,SAAS,EAAE;kBACb,CAAE;kBACFlB,OAAO,EAAGN,CAAC,IAAKrE,aAAa,CAAE,yBAAwBG,KAAK,CAACgC,KAAM,EAAC,CAAE;kBACtE2D,SAAS,EAAEA,CAAA,KAAM9F,aAAa,CAAC,IAAI,CAAE;kBACrC+F,WAAW,EAAC,WAAW;kBAAAlC,QAAA,gBAEvBvF,OAAA;oBAAQmG,GAAG,EAAEtE,KAAK,CAACc,cAAc,IAAId,KAAK,CAACa,QAAS;oBAACU,IAAI,EAAC;kBAAW;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACvE9D,KAAK,CAACoC,SAAS,IAAIpC,KAAK,CAACoC,SAAS,CAACP,MAAM,GAAG,CAAC,IAAI7B,KAAK,CAACoC,SAAS,CAACiC,GAAG,CAAC,CAACwB,QAAQ,EAAElF,KAAK,kBACpFxC,OAAA;oBAEE2H,IAAI,EAAC,WAAW;oBAChBxB,GAAG,EAAEuB,QAAQ,CAACtD,GAAI;oBAClBwD,OAAO,EAAEF,QAAQ,CAACxD,QAAS;oBAC3B2D,KAAK,EAAEH,QAAQ,CAACvD,YAAa;oBAC7B2D,OAAO,EAAEJ,QAAQ,CAACrD,SAAS,IAAI7B,KAAK,KAAK;kBAAE,GALrC,GAAEkF,QAAQ,CAACxD,QAAS,IAAG1B,KAAM,EAAC;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,GACN9D,KAAK,CAACE,OAAO,gBACf/B,OAAA;kBACEmG,GAAG,EAAG,iCAAgCtE,KAAK,CAACE,OAAQ,mBAAmB;kBACvE8B,KAAK,EAAEhC,KAAK,CAACgC,KAAM;kBACnBkE,WAAW,EAAC,GAAG;kBACfC,eAAe;kBACfX,KAAK,EAAE;oBAAEH,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE,MAAM;oBAAEc,MAAM,EAAE;kBAAO;gBAAE;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,gBAEV3F,OAAA;kBAAK8D,SAAS,EAAC,aAAa;kBAAAyB,QAAA,gBAC1BvF,OAAA;oBAAK8D,SAAS,EAAC,YAAY;oBAAAyB,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpC3F,OAAA;oBAAAuF,QAAA,EAAI;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1B3F,OAAA;oBAAAuF,QAAA,EAAI9D,UAAU,IAAI;kBAA4C;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3F,OAAA;gBAAK8D,SAAS,EAAC,oBAAoB;gBAAAyB,QAAA,gBACjCvF,OAAA;kBAAI8D,SAAS,EAAC,qBAAqB;kBAAAyB,QAAA,EAAE1D,KAAK,CAACgC;gBAAK;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtD3F,OAAA;kBAAK8D,SAAS,EAAC,oBAAoB;kBAAAyB,QAAA,gBACjCvF,OAAA;oBAAAuF,QAAA,EAAOnD,cAAc,CAACP,KAAK,CAACQ,OAAO;kBAAC;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5C3F,OAAA;oBAAAuF,QAAA,EAAM;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACd3F,OAAA;oBAAAuF,QAAA,GAAM,QAAM,EAAC1D,KAAK,CAACiC,SAAS,IAAIjC,KAAK,CAACqB,KAAK;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACN3F,OAAA;kBAAK8D,SAAS,EAAC,uBAAuB;kBAAAyB,QAAA,eACpCvF,OAAA;oBACE8D,SAAS,EAAC,oBAAoB;oBAC9BmC,OAAO,EAAEA,CAAA,KAAM3E,oBAAoB,CAAC,IAAI,CAAE;oBAAAiE,QAAA,gBAE1CvF,OAAA;sBAAAuF,QAAA,EAAM;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACd3F,OAAA;sBAAAuF,QAAA,EAAM;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA,GAhJOnD,KAAK;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiJV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEN3F,OAAA;QAAK8D,SAAS,EAAC,aAAa;QAAAyB,QAAA,gBAC1BvF,OAAA,CAACP,eAAe;UAACqE,SAAS,EAAC;QAAY;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1C3F,OAAA;UAAAuF,QAAA,EAAK5D,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1E3F,OAAA;UAAAuF,QAAA,EAAI5D,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJ3F,OAAA;UAAG8D,SAAS,EAAC,YAAY;UAAAyB,QAAA,EAAE5D,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzF,EAAA,CAzZID,YAAY;EAAA,QAEGV,WAAW;AAAA;AAAA2I,EAAA,GAF1BjI,YAAY;AA2ZlB,eAAeA,YAAY;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}