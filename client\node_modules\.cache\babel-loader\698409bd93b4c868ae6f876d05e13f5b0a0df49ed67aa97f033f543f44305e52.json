{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\FloatingBrainwaveAI.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Tb<PERSON><PERSON>ot, TbMinus, TbMaximize, TbX } from 'react-icons/tb';\nimport { chatWithChatGPT, uploadImg } from '../apicalls/chat';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { useSelector } from 'react-redux';\nimport { useLocation } from 'react-router-dom';\nimport ContentRenderer from './ContentRenderer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FloatingBrainwaveAI = () => {\n  _s();\n  const {\n    isKiswahili\n  } = useLanguage();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const location = useLocation();\n  const [isOpen, setIsOpen] = useState(false);\n  const [messages, setMessages] = useState([]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const inputRef = useRef(null);\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [messages]);\n  const handleImageSelect = event => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Memoize initial message to prevent unnecessary re-renders\n  const initialMessage = React.useMemo(() => {\n    const content = isKiswahili ? `Hujambo! Mimi ni Brainwave AI, msaidizi wako wa masomo. Niko hapa kukusaidia na maswali yoyote ya masomo. Je, una swali lolote?` : `Hello! I'm Brainwave AI, your educational assistant. I'm here to help you with any study questions you might have. What would you like to learn about today?`;\n    return {\n      role: \"assistant\",\n      content\n    };\n  }, [isKiswahili]);\n  const sendMessage = async () => {\n    if (!input.trim() && !selectedImage) return;\n    const userMessage = input.trim();\n    const imageFile = selectedImage;\n    setInput('');\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n    let imageUrl = null;\n    if (imageFile) {\n      try {\n        setIsLoading(true);\n\n        // Create FormData for image upload\n        const formData = new FormData();\n        formData.append('image', imageFile);\n        const uploadResponse = await uploadImg(formData);\n        if (uploadResponse.success) {\n          imageUrl = uploadResponse.data.url;\n        } else {\n          throw new Error(uploadResponse.message || 'Image upload failed');\n        }\n      } catch (error) {\n        console.error('Error uploading image:', error);\n      }\n    }\n    const newUserMessage = imageUrl ? {\n      role: \"user\",\n      content: [{\n        type: \"text\",\n        text: userMessage || \"Please analyze this image\"\n      }, {\n        type: \"image_url\",\n        image_url: {\n          url: imageUrl\n        }\n      }]\n    } : {\n      role: \"user\",\n      content: userMessage\n    };\n    setMessages(prev => [...prev, newUserMessage]);\n\n    // Enhanced system prompt\n    const systemPrompt = isKiswahili ? 'Jibu kwa lugha ya Kiswahili tu. Wewe ni msaidizi wa masomo wa Tanzania. Tumia lugha rahisi na ya kielimu. Ikiwa ni swali la picha, soma picha kwa makini na ueleze hatua kwa hatua.' : 'You are an educational assistant for Tanzanian students. Be helpful and provide clear, step-by-step explanations. If this is an image question, carefully analyze the image content and provide detailed solutions.';\n    const chatPayload = {\n      messages: [{\n        role: \"system\",\n        content: systemPrompt\n      }, ...messages, newUserMessage]\n    };\n    try {\n      setIsLoading(true);\n      const response = await chatWithChatGPT(chatPayload);\n      if (response.success) {\n        setMessages(prev => [...prev, {\n          role: \"assistant\",\n          content: response.data\n        }]);\n      } else {\n        throw new Error(response.message || 'Failed to get response');\n      }\n    } catch (error) {\n      const errorMessage = isKiswahili ? \"Samahani, kuna tatizo la mtandao. Tafadhali jaribu tena.\" : \"Sorry, there was a network error. Please try again.\";\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: errorMessage\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleKeyDown = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n  const isMobile = window.innerWidth <= 768;\n\n  // Hide on subscription page\n  if (!user || location.pathname.includes('/subscription')) return null;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [!isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => setIsOpen(true),\n      style: {\n        position: 'fixed',\n        bottom: isMobile ? '20px' : '30px',\n        right: isMobile ? '20px' : '30px',\n        width: isMobile ? '50px' : '60px',\n        height: isMobile ? '50px' : '60px',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        borderRadius: '50%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        cursor: 'pointer',\n        boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\n        zIndex: 1000,\n        transition: 'all 0.3s ease',\n        border: '3px solid rgba(255, 255, 255, 0.2)',\n        backdropFilter: 'blur(10px)'\n      },\n      children: /*#__PURE__*/_jsxDEV(TbRobot, {\n        style: {\n          color: 'white',\n          fontSize: isMobile ? '24px' : '28px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 9\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          bottom: isMobile ? '20px' : '30px',\n          right: isMobile ? '20px' : '30px',\n          width: isMobile ? '320px' : '380px',\n          height: isMobile ? '500px' : '600px',\n          background: 'rgba(255, 255, 255, 0.95)',\n          borderRadius: '20px',\n          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\n          zIndex: 1000,\n          display: 'flex',\n          flexDirection: 'column',\n          border: '1px solid rgba(255, 255, 255, 0.2)',\n          backdropFilter: 'blur(20px)',\n          overflow: 'hidden'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: isMobile ? '16px 20px' : '20px 24px',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TbRobot, {\n              style: {\n                fontSize: isMobile ? '20px' : '24px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: 0,\n                  fontSize: isMobile ? '16px' : '18px',\n                  fontWeight: '600'\n                },\n                children: \"Brainwave AI\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: isMobile ? '11px' : '12px',\n                  opacity: 0.9\n                },\n                children: isKiswahili ? 'Msaidizi wako wa masomo' : 'Your study assistant'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setIsOpen(false);\n              setMessages([]);\n              setInput('');\n              setSelectedImage(null);\n              setImagePreview(null);\n            },\n            style: {\n              background: 'rgba(255, 255, 255, 0.2)',\n              border: 'none',\n              borderRadius: isMobile ? '6px' : '8px',\n              width: isMobile ? '28px' : '32px',\n              height: isMobile ? '28px' : '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              backdropFilter: 'blur(10px)'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: 'white',\n                fontSize: isMobile ? '16px' : '18px',\n                fontWeight: 'bold'\n              },\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            padding: isMobile ? '16px' : '20px',\n            overflowY: 'auto',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '16px'\n          },\n          className: \"custom-scrollbar\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#ffffff',\n              padding: isMobile ? '12px 16px' : '16px 20px',\n              borderRadius: '16px',\n              border: '2px solid #e5e7eb',\n              alignSelf: 'flex-start',\n              maxWidth: '85%',\n              color: '#1f2937',\n              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#1f2937',\n                fontSize: '14px',\n                lineHeight: '1.6'\n              },\n              children: initialMessage.content || 'Hello! I\\'m Brainwave AI, your educational assistant.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              alignSelf: message.role === 'user' ? 'flex-end' : 'flex-start',\n              maxWidth: '85%'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: message.role === 'user' ? 'linear-gradient(135deg, #3b82f6, #1d4ed8)' : '#ffffff',\n                color: message.role === 'user' ? '#ffffff' : '#1f2937',\n                padding: isMobile ? '12px 16px' : '16px 20px',\n                borderRadius: '16px',\n                border: message.role === 'user' ? 'none' : '2px solid #e5e7eb',\n                fontSize: isMobile ? '13px' : '14px',\n                lineHeight: '1.6',\n                boxShadow: message.role === 'user' ? 'none' : '0 2px 8px rgba(0, 0, 0, 0.1)'\n              },\n              children: Array.isArray(message.content) ? message.content.map((item, itemIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [item.type === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: 'inherit',\n                    fontSize: 'inherit',\n                    lineHeight: 'inherit'\n                  },\n                  children: item.text || 'Message content'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 29\n                }, this), item.type === 'image_url' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '8px',\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: item.image_url.url,\n                    alt: \"User upload\",\n                    style: {\n                      maxWidth: '100%',\n                      height: 'auto',\n                      borderRadius: '12px',\n                      maxHeight: isMobile ? '180px' : '250px',\n                      objectFit: 'contain',\n                      border: '3px solid #e2e8f0',\n                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n                      background: '#f8fafc',\n                      cursor: 'pointer'\n                    },\n                    onClick: () => {\n                      window.open(item.image_url.url, '_blank');\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      position: 'absolute',\n                      bottom: '8px',\n                      right: '8px',\n                      background: 'rgba(0, 0, 0, 0.7)',\n                      color: 'white',\n                      padding: '4px 8px',\n                      borderRadius: '6px',\n                      fontSize: '10px',\n                      fontWeight: '500'\n                    },\n                    children: [\"\\uD83D\\uDCF8 \", isKiswahili ? 'Bonyeza kuona kikubwa' : 'Click to enlarge']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 29\n                }, this)]\n              }, itemIndex, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 25\n              }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: 'inherit',\n                  fontSize: 'inherit',\n                  lineHeight: 'inherit'\n                },\n                children: message.content || 'AI response'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 17\n          }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              alignSelf: 'flex-start',\n              maxWidth: '85%'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#ffffff',\n                padding: isMobile ? '12px 16px' : '16px 20px',\n                borderRadius: '16px',\n                border: '2px solid #e5e7eb',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [1, 2, 3].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    background: '#3b82f6',\n                    borderRadius: '50%',\n                    animation: `bounce 1.4s infinite ease-in-out both`,\n                    animationDelay: `${(i - 1) * 0.16}s`\n                  }\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: isMobile ? '12px' : '13px',\n                  color: '#6b7280'\n                },\n                children: isKiswahili ? 'Inafikiri...' : 'Thinking...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this), imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px',\n            padding: '12px',\n            background: 'linear-gradient(135deg, #f0f9ff, #e0f2fe)',\n            borderRadius: '12px',\n            border: '2px solid #0ea5e9',\n            boxShadow: '0 4px 12px rgba(14, 165, 233, 0.15)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: imagePreview,\n                alt: \"Preview\",\n                style: {\n                  width: isMobile ? '80px' : '100px',\n                  height: isMobile ? '80px' : '100px',\n                  objectFit: 'cover',\n                  borderRadius: '8px',\n                  border: '2px solid #0ea5e9',\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: removeImage,\n                style: {\n                  position: 'absolute',\n                  top: '-6px',\n                  right: '-6px',\n                  width: '24px',\n                  height: '24px',\n                  background: '#ef4444',\n                  color: 'white',\n                  borderRadius: '50%',\n                  border: '2px solid white',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '12px',\n                  fontWeight: 'bold',\n                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'\n                },\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '14px',\n                  fontWeight: '600',\n                  color: '#0c4a6e',\n                  margin: '0 0 4px 0',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '6px'\n                },\n                children: [\"\\uD83D\\uDCF8 \", isKiswahili ? 'Picha Imepakiwa' : 'Image Attached']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '11px',\n                  color: '#0369a1',\n                  margin: 0\n                },\n                children: (selectedImage === null || selectedImage === void 0 ? void 0 : selectedImage.name) || 'image.png'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: isMobile ? '6px' : '8px',\n            background: '#f8fafc',\n            borderRadius: isMobile ? '12px' : '16px',\n            padding: isMobile ? '6px' : '8px',\n            border: '2px solid #e2e8f0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            style: {\n              background: '#3b82f6',\n              border: 'none',\n              borderRadius: '6px',\n              width: '24px',\n              height: '24px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              boxShadow: '0 1px 3px rgba(59, 130, 246, 0.3)'\n            },\n            title: isKiswahili ? \"Pakia picha\" : \"Upload image\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: 'white',\n                fontSize: '14px',\n                fontWeight: 'bold'\n              },\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            ref: inputRef,\n            value: input,\n            onChange: e => setInput(e.target.value),\n            onKeyDown: handleKeyDown,\n            placeholder: isKiswahili ? \"Uliza chochote...\" : \"Ask me anything...\",\n            rows: 1,\n            style: {\n              flex: 1,\n              border: 'none',\n              background: 'transparent',\n              outline: 'none',\n              fontSize: isMobile ? '12px' : '14px',\n              color: '#334155',\n              padding: isMobile ? '10px 12px' : '12px 16px',\n              fontFamily: 'inherit'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: sendMessage,\n            disabled: !input.trim() && !selectedImage,\n            style: {\n              background: input.trim() || selectedImage ? '#3b82f6' : '#e2e8f0',\n              border: 'none',\n              borderRadius: '6px',\n              width: '24px',\n              height: '24px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: input.trim() || selectedImage ? 'pointer' : 'not-allowed',\n              transition: 'all 0.2s ease'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: input.trim() || selectedImage ? 'white' : '#9ca3af',\n                fontSize: '12px'\n              },\n              children: \"\\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleImageSelect,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: isMobile ? '9px' : '11px',\n            color: '#94a3b8',\n            textAlign: 'center',\n            margin: isMobile ? '6px 0 0 0' : '8px 0 0 0'\n          },\n          children: isMobile ? isKiswahili ? 'Enter • + Pakia picha' : 'Enter • + Upload image' : isKiswahili ? 'Enter kusonga • + Pakia picha' : 'Press Enter to send • + Upload image'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this)\n    }, void 0, false), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes bounce {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n\n        .custom-scrollbar::-webkit-scrollbar {\n          width: 6px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-track {\n          background: transparent;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 3px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(FloatingBrainwaveAI, \"c72Lc7i+LdZLjh/m8NIEPg+NwOw=\", false, function () {\n  return [useLanguage, useSelector, useLocation];\n});\n_c = FloatingBrainwaveAI;\nexport default FloatingBrainwaveAI;\nvar _c;\n$RefreshReg$(_c, \"FloatingBrainwaveAI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "TbRobot", "TbMinus", "TbMaximize", "TbX", "chatWithChatGPT", "uploadImg", "useLanguage", "useSelector", "useLocation", "Content<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FloatingBrainwaveAI", "_s", "isKiswahili", "user", "state", "location", "isOpen", "setIsOpen", "messages", "setMessages", "input", "setInput", "isLoading", "setIsLoading", "selectedImage", "setSelectedImage", "imagePreview", "setImagePreview", "messagesEndRef", "fileInputRef", "inputRef", "current", "scrollIntoView", "behavior", "handleImageSelect", "event", "file", "target", "files", "type", "startsWith", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "removeImage", "value", "initialMessage", "useMemo", "content", "role", "sendMessage", "trim", "userMessage", "imageFile", "imageUrl", "formData", "FormData", "append", "uploadResponse", "success", "data", "url", "Error", "message", "error", "console", "newUserMessage", "text", "image_url", "prev", "systemPrompt", "chatPayload", "response", "errorMessage", "handleKeyDown", "key", "shift<PERSON>ey", "preventDefault", "isMobile", "window", "innerWidth", "pathname", "includes", "children", "onClick", "style", "position", "bottom", "right", "width", "height", "background", "borderRadius", "display", "alignItems", "justifyContent", "cursor", "boxShadow", "zIndex", "transition", "border", "<PERSON><PERSON>ilter", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexDirection", "overflow", "padding", "gap", "margin", "fontWeight", "opacity", "flex", "overflowY", "className", "alignSelf", "max<PERSON><PERSON><PERSON>", "lineHeight", "map", "index", "Array", "isArray", "item", "itemIndex", "marginTop", "src", "alt", "maxHeight", "objectFit", "open", "i", "animation", "animationDelay", "ref", "marginBottom", "top", "name", "_fileInputRef$current", "click", "title", "onChange", "onKeyDown", "placeholder", "rows", "outline", "fontFamily", "disabled", "accept", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/FloatingBrainwaveAI.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { Tb<PERSON><PERSON><PERSON>, Tb<PERSON>inus, TbMaximize, TbX } from 'react-icons/tb';\nimport { chatWithChatGPT, uploadImg } from '../apicalls/chat';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { useSelector } from 'react-redux';\nimport { useLocation } from 'react-router-dom';\nimport ContentRenderer from './ContentRenderer';\n\nconst FloatingBrainwaveAI = () => {\n  const { isKiswahili } = useLanguage();\n  const { user } = useSelector(state => state.user);\n  const location = useLocation();\n  const [isOpen, setIsOpen] = useState(false);\n  const [messages, setMessages] = useState([]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const inputRef = useRef(null);\n\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  const handleImageSelect = (event) => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = (e) => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n\n\n  // Memoize initial message to prevent unnecessary re-renders\n  const initialMessage = React.useMemo(() => {\n    const content = isKiswahili\n      ? `Hujambo! Mimi ni Brainwave AI, msaidizi wako wa masomo. Niko hapa kukusaidia na maswali yoyote ya masomo. Je, una swali lolote?`\n      : `Hello! I'm Brainwave AI, your educational assistant. I'm here to help you with any study questions you might have. What would you like to learn about today?`;\n\n    return { role: \"assistant\", content };\n  }, [isKiswahili]);\n\n  const sendMessage = async () => {\n    if (!input.trim() && !selectedImage) return;\n\n    const userMessage = input.trim();\n    const imageFile = selectedImage;\n\n    setInput('');\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n\n    let imageUrl = null;\n    if (imageFile) {\n      try {\n        setIsLoading(true);\n\n        // Create FormData for image upload\n        const formData = new FormData();\n        formData.append('image', imageFile);\n\n        const uploadResponse = await uploadImg(formData);\n\n        if (uploadResponse.success) {\n          imageUrl = uploadResponse.data.url;\n        } else {\n          throw new Error(uploadResponse.message || 'Image upload failed');\n        }\n      } catch (error) {\n        console.error('Error uploading image:', error);\n      }\n    }\n\n    const newUserMessage = imageUrl\n      ? {\n          role: \"user\",\n          content: [\n            { type: \"text\", text: userMessage || \"Please analyze this image\" },\n            { type: \"image_url\", image_url: { url: imageUrl } }\n          ]\n        }\n      : { role: \"user\", content: userMessage };\n\n    setMessages(prev => [...prev, newUserMessage]);\n\n    // Enhanced system prompt\n    const systemPrompt = isKiswahili\n      ? 'Jibu kwa lugha ya Kiswahili tu. Wewe ni msaidizi wa masomo wa Tanzania. Tumia lugha rahisi na ya kielimu. Ikiwa ni swali la picha, soma picha kwa makini na ueleze hatua kwa hatua.'\n      : 'You are an educational assistant for Tanzanian students. Be helpful and provide clear, step-by-step explanations. If this is an image question, carefully analyze the image content and provide detailed solutions.';\n\n    const chatPayload = {\n      messages: [\n        { role: \"system\", content: systemPrompt },\n        ...messages,\n        newUserMessage\n      ]\n    };\n\n    try {\n      setIsLoading(true);\n      const response = await chatWithChatGPT(chatPayload);\n\n      if (response.success) {\n        setMessages(prev => [...prev, { role: \"assistant\", content: response.data }]);\n      } else {\n        throw new Error(response.message || 'Failed to get response');\n      }\n    } catch (error) {\n      const errorMessage = isKiswahili\n        ? \"Samahani, kuna tatizo la mtandao. Tafadhali jaribu tena.\"\n        : \"Sorry, there was a network error. Please try again.\";\n      setMessages(prev => [...prev, { role: \"assistant\", content: errorMessage }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyDown = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  const isMobile = window.innerWidth <= 768;\n\n  // Hide on subscription page\n  if (!user || location.pathname.includes('/subscription')) return null;\n\n  return (\n    <>\n      {/* Floating Button */}\n      {!isOpen && (\n        <div\n          onClick={() => setIsOpen(true)}\n          style={{\n            position: 'fixed',\n            bottom: isMobile ? '20px' : '30px',\n            right: isMobile ? '20px' : '30px',\n            width: isMobile ? '50px' : '60px',\n            height: isMobile ? '50px' : '60px',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\n            zIndex: 1000,\n            transition: 'all 0.3s ease',\n            border: '3px solid rgba(255, 255, 255, 0.2)',\n            backdropFilter: 'blur(10px)'\n          }}\n        >\n          <TbRobot style={{ color: 'white', fontSize: isMobile ? '24px' : '28px' }} />\n        </div>\n      )}\n\n      {/* Chat Window */}\n      {isOpen && (\n        <>\n          <div style={{\n            position: 'fixed',\n            bottom: isMobile ? '20px' : '30px',\n            right: isMobile ? '20px' : '30px',\n            width: isMobile ? '320px' : '380px',\n            height: isMobile ? '500px' : '600px',\n            background: 'rgba(255, 255, 255, 0.95)',\n            borderRadius: '20px',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\n            zIndex: 1000,\n            display: 'flex',\n            flexDirection: 'column',\n            border: '1px solid rgba(255, 255, 255, 0.2)',\n            backdropFilter: 'blur(20px)',\n            overflow: 'hidden'\n          }}>\n            {/* Header */}\n            <div style={{\n              padding: isMobile ? '16px 20px' : '20px 24px',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n                <TbRobot style={{ fontSize: isMobile ? '20px' : '24px' }} />\n                <div>\n                  <h3 style={{ margin: 0, fontSize: isMobile ? '16px' : '18px', fontWeight: '600' }}>\n                    Brainwave AI\n                  </h3>\n                  <p style={{ margin: 0, fontSize: isMobile ? '11px' : '12px', opacity: 0.9 }}>\n                    {isKiswahili ? 'Msaidizi wako wa masomo' : 'Your study assistant'}\n                  </p>\n                </div>\n              </div>\n              <button\n                onClick={() => {\n                  setIsOpen(false);\n                  setMessages([]);\n                  setInput('');\n                  setSelectedImage(null);\n                  setImagePreview(null);\n                }}\n                style={{\n                  background: 'rgba(255, 255, 255, 0.2)',\n                  border: 'none',\n                  borderRadius: isMobile ? '6px' : '8px',\n                  width: isMobile ? '28px' : '32px',\n                  height: isMobile ? '28px' : '32px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease',\n                  backdropFilter: 'blur(10px)'\n                }}\n              >\n                <span style={{ color: 'white', fontSize: isMobile ? '16px' : '18px', fontWeight: 'bold' }}>×</span>\n              </button>\n            </div>\n\n            {/* Messages */}\n            <div style={{\n              flex: 1,\n              padding: isMobile ? '16px' : '20px',\n              overflowY: 'auto',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '16px'\n            }} className=\"custom-scrollbar\">\n              {/* Initial Message */}\n              <div style={{\n                background: '#ffffff',\n                padding: isMobile ? '12px 16px' : '16px 20px',\n                borderRadius: '16px',\n                border: '2px solid #e5e7eb',\n                alignSelf: 'flex-start',\n                maxWidth: '85%',\n                color: '#1f2937',\n                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n              }}>\n                <div style={{ color: '#1f2937', fontSize: '14px', lineHeight: '1.6' }}>\n                  {initialMessage.content || 'Hello! I\\'m Brainwave AI, your educational assistant.'}\n                </div>\n              </div>\n\n              {/* Chat Messages */}\n              {messages.map((message, index) => (\n                <div key={index} style={{\n                  alignSelf: message.role === 'user' ? 'flex-end' : 'flex-start',\n                  maxWidth: '85%'\n                }}>\n                  <div style={{\n                    background: message.role === 'user'\n                      ? 'linear-gradient(135deg, #3b82f6, #1d4ed8)'\n                      : '#ffffff',\n                    color: message.role === 'user' ? '#ffffff' : '#1f2937',\n                    padding: isMobile ? '12px 16px' : '16px 20px',\n                    borderRadius: '16px',\n                    border: message.role === 'user' ? 'none' : '2px solid #e5e7eb',\n                    fontSize: isMobile ? '13px' : '14px',\n                    lineHeight: '1.6',\n                    boxShadow: message.role === 'user' ? 'none' : '0 2px 8px rgba(0, 0, 0, 0.1)'\n                  }}>\n                    {Array.isArray(message.content) ? (\n                      message.content.map((item, itemIndex) => (\n                        <div key={itemIndex}>\n                          {item.type === 'text' && (\n                            <div style={{ color: 'inherit', fontSize: 'inherit', lineHeight: 'inherit' }}>\n                              {item.text || 'Message content'}\n                            </div>\n                          )}\n                          {item.type === 'image_url' && (\n                            <div style={{ marginTop: '8px', position: 'relative' }}>\n                              <img \n                                src={item.image_url.url} \n                                alt=\"User upload\" \n                                style={{ \n                                  maxWidth: '100%', \n                                  height: 'auto', \n                                  borderRadius: '12px', \n                                  maxHeight: isMobile ? '180px' : '250px', \n                                  objectFit: 'contain', \n                                  border: '3px solid #e2e8f0', \n                                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n                                  background: '#f8fafc',\n                                  cursor: 'pointer'\n                                }}\n                                onClick={() => {\n                                  window.open(item.image_url.url, '_blank');\n                                }}\n                              />\n                              <div style={{\n                                position: 'absolute',\n                                bottom: '8px',\n                                right: '8px',\n                                background: 'rgba(0, 0, 0, 0.7)',\n                                color: 'white',\n                                padding: '4px 8px',\n                                borderRadius: '6px',\n                                fontSize: '10px',\n                                fontWeight: '500'\n                              }}>\n                                📸 {isKiswahili ? 'Bonyeza kuona kikubwa' : 'Click to enlarge'}\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      ))\n                    ) : (\n                      <div style={{ color: 'inherit', fontSize: 'inherit', lineHeight: 'inherit' }}>\n                        {message.content || 'AI response'}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n\n              {/* Loading indicator */}\n              {isLoading && (\n                <div style={{\n                  alignSelf: 'flex-start',\n                  maxWidth: '85%'\n                }}>\n                  <div style={{\n                    background: '#ffffff',\n                    padding: isMobile ? '12px 16px' : '16px 20px',\n                    borderRadius: '16px',\n                    border: '2px solid #e5e7eb',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n                  }}>\n                    <div style={{ display: 'flex', gap: '4px' }}>\n                      {[1, 2, 3].map(i => (\n                        <div\n                          key={i}\n                          style={{\n                            width: '8px',\n                            height: '8px',\n                            background: '#3b82f6',\n                            borderRadius: '50%',\n                            animation: `bounce 1.4s infinite ease-in-out both`,\n                            animationDelay: `${(i - 1) * 0.16}s`\n                          }}\n                        />\n                      ))}\n                    </div>\n                    <span style={{ fontSize: isMobile ? '12px' : '13px', color: '#6b7280' }}>\n                      {isKiswahili ? 'Inafikiri...' : 'Thinking...'}\n                    </span>\n                  </div>\n                </div>\n              )}\n\n              <div ref={messagesEndRef} />\n            </div>\n\n            {/* Image Preview */}\n            {imagePreview && (\n              <div style={{ marginBottom: '12px', padding: '12px', background: 'linear-gradient(135deg, #f0f9ff, #e0f2fe)', borderRadius: '12px', border: '2px solid #0ea5e9', boxShadow: '0 4px 12px rgba(14, 165, 233, 0.15)' }}>\n                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n                  <div style={{ position: 'relative' }}>\n                    <img src={imagePreview} alt=\"Preview\" style={{ width: isMobile ? '80px' : '100px', height: isMobile ? '80px' : '100px', objectFit: 'cover', borderRadius: '8px', border: '2px solid #0ea5e9', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)' }} />\n                    <button onClick={removeImage} style={{ position: 'absolute', top: '-6px', right: '-6px', width: '24px', height: '24px', background: '#ef4444', color: 'white', borderRadius: '50%', border: '2px solid white', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px', fontWeight: 'bold', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)' }}>×</button>\n                  </div>\n                  <div style={{ flex: 1 }}>\n                    <p style={{ fontSize: '14px', fontWeight: '600', color: '#0c4a6e', margin: '0 0 4px 0', display: 'flex', alignItems: 'center', gap: '6px' }}>\n                      📸 {isKiswahili ? 'Picha Imepakiwa' : 'Image Attached'}\n                    </p>\n                    <p style={{ fontSize: '11px', color: '#0369a1', margin: 0 }}>\n                      {selectedImage?.name || 'image.png'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Input Area */}\n            <div style={{ display: 'flex', gap: isMobile ? '6px' : '8px', background: '#f8fafc', borderRadius: isMobile ? '12px' : '16px', padding: isMobile ? '6px' : '8px', border: '2px solid #e2e8f0' }}>\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                style={{\n                  background: '#3b82f6',\n                  border: 'none',\n                  borderRadius: '6px',\n                  width: '24px',\n                  height: '24px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease',\n                  boxShadow: '0 1px 3px rgba(59, 130, 246, 0.3)'\n                }}\n                title={isKiswahili ? \"Pakia picha\" : \"Upload image\"}\n              >\n                <span style={{ color: 'white', fontSize: '14px', fontWeight: 'bold' }}>+</span>\n              </button>\n\n\n\n              <textarea\n                ref={inputRef}\n                value={input}\n                onChange={(e) => setInput(e.target.value)}\n                onKeyDown={handleKeyDown}\n                placeholder={isKiswahili ? \"Uliza chochote...\" : \"Ask me anything...\"}\n                rows={1}\n                style={{\n                  flex: 1,\n                  border: 'none',\n                  background: 'transparent',\n                  outline: 'none',\n                  fontSize: isMobile ? '12px' : '14px',\n                  color: '#334155',\n                  padding: isMobile ? '10px 12px' : '12px 16px',\n                  fontFamily: 'inherit'\n                }}\n              />\n\n              <button\n                onClick={sendMessage}\n                disabled={!input.trim() && !selectedImage}\n                style={{\n                  background: (input.trim() || selectedImage) ? '#3b82f6' : '#e2e8f0',\n                  border: 'none',\n                  borderRadius: '6px',\n                  width: '24px',\n                  height: '24px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: (input.trim() || selectedImage) ? 'pointer' : 'not-allowed',\n                  transition: 'all 0.2s ease'\n                }}\n              >\n                <span style={{ color: (input.trim() || selectedImage) ? 'white' : '#9ca3af', fontSize: '12px' }}>→</span>\n              </button>\n            </div>\n\n            <input ref={fileInputRef} type=\"file\" accept=\"image/*\" onChange={handleImageSelect} style={{ display: 'none' }} />\n\n            <p style={{ fontSize: isMobile ? '9px' : '11px', color: '#94a3b8', textAlign: 'center', margin: isMobile ? '6px 0 0 0' : '8px 0 0 0' }}>\n              {isMobile\n                ? (isKiswahili ? 'Enter • + Pakia picha' : 'Enter • + Upload image')\n                : (isKiswahili ? 'Enter kusonga • + Pakia picha' : 'Press Enter to send • + Upload image')\n              }\n            </p>\n          </div>\n        </>\n      )}\n\n      <style>{`\n        @keyframes bounce {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n\n        .custom-scrollbar::-webkit-scrollbar {\n          width: 6px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-track {\n          background: transparent;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 3px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n      `}</style>\n    </>\n  );\n};\n\nexport default FloatingBrainwaveAI;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAClE,SAASC,eAAe,EAAEC,SAAS,QAAQ,kBAAkB;AAC7D,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEhD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAY,CAAC,GAAGV,WAAW,CAAC,CAAC;EACrC,MAAM;IAAEW;EAAK,CAAC,GAAGV,WAAW,CAACW,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EACjD,MAAME,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMmC,cAAc,GAAGlC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMmC,YAAY,GAAGnC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMoC,QAAQ,GAAGpC,MAAM,CAAC,IAAI,CAAC;EAE7BC,SAAS,CAAC,MAAM;IACd,IAAIiC,cAAc,CAACG,OAAO,EAAE;MAC1BH,cAAc,CAACG,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;EAEd,MAAMgB,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,IAAIA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1Cf,gBAAgB,CAACW,IAAI,CAAC;MACtB,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKjB,eAAe,CAACiB,CAAC,CAACP,MAAM,CAACQ,MAAM,CAAC;MACvDJ,MAAM,CAACK,aAAa,CAACV,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBtB,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIE,YAAY,CAACE,OAAO,EAAE;MACxBF,YAAY,CAACE,OAAO,CAACiB,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAID;EACA,MAAMC,cAAc,GAAGzD,KAAK,CAAC0D,OAAO,CAAC,MAAM;IACzC,MAAMC,OAAO,GAAGvC,WAAW,GACtB,iIAAgI,GAChI,8JAA6J;IAElK,OAAO;MAAEwC,IAAI,EAAE,WAAW;MAAED;IAAQ,CAAC;EACvC,CAAC,EAAE,CAACvC,WAAW,CAAC,CAAC;EAEjB,MAAMyC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACjC,KAAK,CAACkC,IAAI,CAAC,CAAC,IAAI,CAAC9B,aAAa,EAAE;IAErC,MAAM+B,WAAW,GAAGnC,KAAK,CAACkC,IAAI,CAAC,CAAC;IAChC,MAAME,SAAS,GAAGhC,aAAa;IAE/BH,QAAQ,CAAC,EAAE,CAAC;IACZI,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIE,YAAY,CAACE,OAAO,EAAE;MACxBF,YAAY,CAACE,OAAO,CAACiB,KAAK,GAAG,EAAE;IACjC;IAEA,IAAIS,QAAQ,GAAG,IAAI;IACnB,IAAID,SAAS,EAAE;MACb,IAAI;QACFjC,YAAY,CAAC,IAAI,CAAC;;QAElB;QACA,MAAMmC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEJ,SAAS,CAAC;QAEnC,MAAMK,cAAc,GAAG,MAAM5D,SAAS,CAACyD,QAAQ,CAAC;QAEhD,IAAIG,cAAc,CAACC,OAAO,EAAE;UAC1BL,QAAQ,GAAGI,cAAc,CAACE,IAAI,CAACC,GAAG;QACpC,CAAC,MAAM;UACL,MAAM,IAAIC,KAAK,CAACJ,cAAc,CAACK,OAAO,IAAI,qBAAqB,CAAC;QAClE;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;IACF;IAEA,MAAME,cAAc,GAAGZ,QAAQ,GAC3B;MACEL,IAAI,EAAE,MAAM;MACZD,OAAO,EAAE,CACP;QAAEZ,IAAI,EAAE,MAAM;QAAE+B,IAAI,EAAEf,WAAW,IAAI;MAA4B,CAAC,EAClE;QAAEhB,IAAI,EAAE,WAAW;QAAEgC,SAAS,EAAE;UAAEP,GAAG,EAAEP;QAAS;MAAE,CAAC;IAEvD,CAAC,GACD;MAAEL,IAAI,EAAE,MAAM;MAAED,OAAO,EAAEI;IAAY,CAAC;IAE1CpC,WAAW,CAACqD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,cAAc,CAAC,CAAC;;IAE9C;IACA,MAAMI,YAAY,GAAG7D,WAAW,GAC5B,qLAAqL,GACrL,qNAAqN;IAEzN,MAAM8D,WAAW,GAAG;MAClBxD,QAAQ,EAAE,CACR;QAAEkC,IAAI,EAAE,QAAQ;QAAED,OAAO,EAAEsB;MAAa,CAAC,EACzC,GAAGvD,QAAQ,EACXmD,cAAc;IAElB,CAAC;IAED,IAAI;MACF9C,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMoD,QAAQ,GAAG,MAAM3E,eAAe,CAAC0E,WAAW,CAAC;MAEnD,IAAIC,QAAQ,CAACb,OAAO,EAAE;QACpB3C,WAAW,CAACqD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAEpB,IAAI,EAAE,WAAW;UAAED,OAAO,EAAEwB,QAAQ,CAACZ;QAAK,CAAC,CAAC,CAAC;MAC/E,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAACU,QAAQ,CAACT,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,MAAMS,YAAY,GAAGhE,WAAW,GAC5B,0DAA0D,GAC1D,qDAAqD;MACzDO,WAAW,CAACqD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAEpB,IAAI,EAAE,WAAW;QAAED,OAAO,EAAEyB;MAAa,CAAC,CAAC,CAAC;IAC9E,CAAC,SAAS;MACRrD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMsD,aAAa,GAAIjC,CAAC,IAAK;IAC3B,IAAIA,CAAC,CAACkC,GAAG,KAAK,OAAO,IAAI,CAAClC,CAAC,CAACmC,QAAQ,EAAE;MACpCnC,CAAC,CAACoC,cAAc,CAAC,CAAC;MAClB3B,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAM4B,QAAQ,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG;;EAEzC;EACA,IAAI,CAACtE,IAAI,IAAIE,QAAQ,CAACqE,QAAQ,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE,OAAO,IAAI;EAErE,oBACE9E,OAAA,CAAAE,SAAA;IAAA6E,QAAA,GAEG,CAACtE,MAAM,iBACNT,OAAA;MACEgF,OAAO,EAAEA,CAAA,KAAMtE,SAAS,CAAC,IAAI,CAAE;MAC/BuE,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAET,QAAQ,GAAG,MAAM,GAAG,MAAM;QAClCU,KAAK,EAAEV,QAAQ,GAAG,MAAM,GAAG,MAAM;QACjCW,KAAK,EAAEX,QAAQ,GAAG,MAAM,GAAG,MAAM;QACjCY,MAAM,EAAEZ,QAAQ,GAAG,MAAM,GAAG,MAAM;QAClCa,UAAU,EAAE,mDAAmD;QAC/DC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,qCAAqC;QAChDC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,eAAe;QAC3BC,MAAM,EAAE,oCAAoC;QAC5CC,cAAc,EAAE;MAClB,CAAE;MAAAlB,QAAA,eAEF/E,OAAA,CAACX,OAAO;QAAC4F,KAAK,EAAE;UAAEiB,KAAK,EAAE,OAAO;UAAEC,QAAQ,EAAEzB,QAAQ,GAAG,MAAM,GAAG;QAAO;MAAE;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CACN,EAGA9F,MAAM,iBACLT,OAAA,CAAAE,SAAA;MAAA6E,QAAA,eACE/E,OAAA;QAAKiF,KAAK,EAAE;UACVC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAET,QAAQ,GAAG,MAAM,GAAG,MAAM;UAClCU,KAAK,EAAEV,QAAQ,GAAG,MAAM,GAAG,MAAM;UACjCW,KAAK,EAAEX,QAAQ,GAAG,OAAO,GAAG,OAAO;UACnCY,MAAM,EAAEZ,QAAQ,GAAG,OAAO,GAAG,OAAO;UACpCa,UAAU,EAAE,2BAA2B;UACvCC,YAAY,EAAE,MAAM;UACpBK,SAAS,EAAE,iCAAiC;UAC5CC,MAAM,EAAE,IAAI;UACZL,OAAO,EAAE,MAAM;UACfe,aAAa,EAAE,QAAQ;UACvBR,MAAM,EAAE,oCAAoC;UAC5CC,cAAc,EAAE,YAAY;UAC5BQ,QAAQ,EAAE;QACZ,CAAE;QAAA1B,QAAA,gBAEA/E,OAAA;UAAKiF,KAAK,EAAE;YACVyB,OAAO,EAAEhC,QAAQ,GAAG,WAAW,GAAG,WAAW;YAC7Ca,UAAU,EAAE,mDAAmD;YAC/DW,KAAK,EAAE,OAAO;YACdT,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAZ,QAAA,gBACA/E,OAAA;YAAKiF,KAAK,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEiB,GAAG,EAAE;YAAO,CAAE;YAAA5B,QAAA,gBACjE/E,OAAA,CAACX,OAAO;cAAC4F,KAAK,EAAE;gBAAEkB,QAAQ,EAAEzB,QAAQ,GAAG,MAAM,GAAG;cAAO;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DvG,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAIiF,KAAK,EAAE;kBAAE2B,MAAM,EAAE,CAAC;kBAAET,QAAQ,EAAEzB,QAAQ,GAAG,MAAM,GAAG,MAAM;kBAAEmC,UAAU,EAAE;gBAAM,CAAE;gBAAA9B,QAAA,EAAC;cAEnF;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvG,OAAA;gBAAGiF,KAAK,EAAE;kBAAE2B,MAAM,EAAE,CAAC;kBAAET,QAAQ,EAAEzB,QAAQ,GAAG,MAAM,GAAG,MAAM;kBAAEoC,OAAO,EAAE;gBAAI,CAAE;gBAAA/B,QAAA,EACzE1E,WAAW,GAAG,yBAAyB,GAAG;cAAsB;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvG,OAAA;YACEgF,OAAO,EAAEA,CAAA,KAAM;cACbtE,SAAS,CAAC,KAAK,CAAC;cAChBE,WAAW,CAAC,EAAE,CAAC;cACfE,QAAQ,CAAC,EAAE,CAAC;cACZI,gBAAgB,CAAC,IAAI,CAAC;cACtBE,eAAe,CAAC,IAAI,CAAC;YACvB,CAAE;YACF6D,KAAK,EAAE;cACLM,UAAU,EAAE,0BAA0B;cACtCS,MAAM,EAAE,MAAM;cACdR,YAAY,EAAEd,QAAQ,GAAG,KAAK,GAAG,KAAK;cACtCW,KAAK,EAAEX,QAAQ,GAAG,MAAM,GAAG,MAAM;cACjCY,MAAM,EAAEZ,QAAQ,GAAG,MAAM,GAAG,MAAM;cAClCe,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAE,SAAS;cACjBG,UAAU,EAAE,eAAe;cAC3BE,cAAc,EAAE;YAClB,CAAE;YAAAlB,QAAA,eAEF/E,OAAA;cAAMiF,KAAK,EAAE;gBAAEiB,KAAK,EAAE,OAAO;gBAAEC,QAAQ,EAAEzB,QAAQ,GAAG,MAAM,GAAG,MAAM;gBAAEmC,UAAU,EAAE;cAAO,CAAE;cAAA9B,QAAA,EAAC;YAAC;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNvG,OAAA;UAAKiF,KAAK,EAAE;YACV8B,IAAI,EAAE,CAAC;YACPL,OAAO,EAAEhC,QAAQ,GAAG,MAAM,GAAG,MAAM;YACnCsC,SAAS,EAAE,MAAM;YACjBvB,OAAO,EAAE,MAAM;YACfe,aAAa,EAAE,QAAQ;YACvBG,GAAG,EAAE;UACP,CAAE;UAACM,SAAS,EAAC,kBAAkB;UAAAlC,QAAA,gBAE7B/E,OAAA;YAAKiF,KAAK,EAAE;cACVM,UAAU,EAAE,SAAS;cACrBmB,OAAO,EAAEhC,QAAQ,GAAG,WAAW,GAAG,WAAW;cAC7Cc,YAAY,EAAE,MAAM;cACpBQ,MAAM,EAAE,mBAAmB;cAC3BkB,SAAS,EAAE,YAAY;cACvBC,QAAQ,EAAE,KAAK;cACfjB,KAAK,EAAE,SAAS;cAChBL,SAAS,EAAE;YACb,CAAE;YAAAd,QAAA,eACA/E,OAAA;cAAKiF,KAAK,EAAE;gBAAEiB,KAAK,EAAE,SAAS;gBAAEC,QAAQ,EAAE,MAAM;gBAAEiB,UAAU,EAAE;cAAM,CAAE;cAAArC,QAAA,EACnErC,cAAc,CAACE,OAAO,IAAI;YAAuD;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL5F,QAAQ,CAAC0G,GAAG,CAAC,CAAC1D,OAAO,EAAE2D,KAAK,kBAC3BtH,OAAA;YAAiBiF,KAAK,EAAE;cACtBiC,SAAS,EAAEvD,OAAO,CAACd,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,YAAY;cAC9DsE,QAAQ,EAAE;YACZ,CAAE;YAAApC,QAAA,eACA/E,OAAA;cAAKiF,KAAK,EAAE;gBACVM,UAAU,EAAE5B,OAAO,CAACd,IAAI,KAAK,MAAM,GAC/B,2CAA2C,GAC3C,SAAS;gBACbqD,KAAK,EAAEvC,OAAO,CAACd,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;gBACtD6D,OAAO,EAAEhC,QAAQ,GAAG,WAAW,GAAG,WAAW;gBAC7Cc,YAAY,EAAE,MAAM;gBACpBQ,MAAM,EAAErC,OAAO,CAACd,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,mBAAmB;gBAC9DsD,QAAQ,EAAEzB,QAAQ,GAAG,MAAM,GAAG,MAAM;gBACpC0C,UAAU,EAAE,KAAK;gBACjBvB,SAAS,EAAElC,OAAO,CAACd,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG;cAChD,CAAE;cAAAkC,QAAA,EACCwC,KAAK,CAACC,OAAO,CAAC7D,OAAO,CAACf,OAAO,CAAC,GAC7Be,OAAO,CAACf,OAAO,CAACyE,GAAG,CAAC,CAACI,IAAI,EAAEC,SAAS,kBAClC1H,OAAA;gBAAA+E,QAAA,GACG0C,IAAI,CAACzF,IAAI,KAAK,MAAM,iBACnBhC,OAAA;kBAAKiF,KAAK,EAAE;oBAAEiB,KAAK,EAAE,SAAS;oBAAEC,QAAQ,EAAE,SAAS;oBAAEiB,UAAU,EAAE;kBAAU,CAAE;kBAAArC,QAAA,EAC1E0C,IAAI,CAAC1D,IAAI,IAAI;gBAAiB;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CACN,EACAkB,IAAI,CAACzF,IAAI,KAAK,WAAW,iBACxBhC,OAAA;kBAAKiF,KAAK,EAAE;oBAAE0C,SAAS,EAAE,KAAK;oBAAEzC,QAAQ,EAAE;kBAAW,CAAE;kBAAAH,QAAA,gBACrD/E,OAAA;oBACE4H,GAAG,EAAEH,IAAI,CAACzD,SAAS,CAACP,GAAI;oBACxBoE,GAAG,EAAC,aAAa;oBACjB5C,KAAK,EAAE;sBACLkC,QAAQ,EAAE,MAAM;sBAChB7B,MAAM,EAAE,MAAM;sBACdE,YAAY,EAAE,MAAM;sBACpBsC,SAAS,EAAEpD,QAAQ,GAAG,OAAO,GAAG,OAAO;sBACvCqD,SAAS,EAAE,SAAS;sBACpB/B,MAAM,EAAE,mBAAmB;sBAC3BH,SAAS,EAAE,gCAAgC;sBAC3CN,UAAU,EAAE,SAAS;sBACrBK,MAAM,EAAE;oBACV,CAAE;oBACFZ,OAAO,EAAEA,CAAA,KAAM;sBACbL,MAAM,CAACqD,IAAI,CAACP,IAAI,CAACzD,SAAS,CAACP,GAAG,EAAE,QAAQ,CAAC;oBAC3C;kBAAE;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFvG,OAAA;oBAAKiF,KAAK,EAAE;sBACVC,QAAQ,EAAE,UAAU;sBACpBC,MAAM,EAAE,KAAK;sBACbC,KAAK,EAAE,KAAK;sBACZG,UAAU,EAAE,oBAAoB;sBAChCW,KAAK,EAAE,OAAO;sBACdQ,OAAO,EAAE,SAAS;sBAClBlB,YAAY,EAAE,KAAK;sBACnBW,QAAQ,EAAE,MAAM;sBAChBU,UAAU,EAAE;oBACd,CAAE;oBAAA9B,QAAA,GAAC,eACE,EAAC1E,WAAW,GAAG,uBAAuB,GAAG,kBAAkB;kBAAA;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA,GAxCOmB,SAAS;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyCd,CACN,CAAC,gBAEFvG,OAAA;gBAAKiF,KAAK,EAAE;kBAAEiB,KAAK,EAAE,SAAS;kBAAEC,QAAQ,EAAE,SAAS;kBAAEiB,UAAU,EAAE;gBAAU,CAAE;gBAAArC,QAAA,EAC1EpB,OAAO,CAACf,OAAO,IAAI;cAAa;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GAlEEe,KAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmEV,CACN,CAAC,EAGDxF,SAAS,iBACRf,OAAA;YAAKiF,KAAK,EAAE;cACViC,SAAS,EAAE,YAAY;cACvBC,QAAQ,EAAE;YACZ,CAAE;YAAApC,QAAA,eACA/E,OAAA;cAAKiF,KAAK,EAAE;gBACVM,UAAU,EAAE,SAAS;gBACrBmB,OAAO,EAAEhC,QAAQ,GAAG,WAAW,GAAG,WAAW;gBAC7Cc,YAAY,EAAE,MAAM;gBACpBQ,MAAM,EAAE,mBAAmB;gBAC3BP,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBiB,GAAG,EAAE,KAAK;gBACVd,SAAS,EAAE;cACb,CAAE;cAAAd,QAAA,gBACA/E,OAAA;gBAAKiF,KAAK,EAAE;kBAAEQ,OAAO,EAAE,MAAM;kBAAEkB,GAAG,EAAE;gBAAM,CAAE;gBAAA5B,QAAA,EACzC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACsC,GAAG,CAACY,CAAC,iBACdjI,OAAA;kBAEEiF,KAAK,EAAE;oBACLI,KAAK,EAAE,KAAK;oBACZC,MAAM,EAAE,KAAK;oBACbC,UAAU,EAAE,SAAS;oBACrBC,YAAY,EAAE,KAAK;oBACnB0C,SAAS,EAAG,uCAAsC;oBAClDC,cAAc,EAAG,GAAE,CAACF,CAAC,GAAG,CAAC,IAAI,IAAK;kBACpC;gBAAE,GARGA,CAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASP,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvG,OAAA;gBAAMiF,KAAK,EAAE;kBAAEkB,QAAQ,EAAEzB,QAAQ,GAAG,MAAM,GAAG,MAAM;kBAAEwB,KAAK,EAAE;gBAAU,CAAE;gBAAAnB,QAAA,EACrE1E,WAAW,GAAG,cAAc,GAAG;cAAa;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDvG,OAAA;YAAKoI,GAAG,EAAE/G;UAAe;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,EAGLpF,YAAY,iBACXnB,OAAA;UAAKiF,KAAK,EAAE;YAAEoD,YAAY,EAAE,MAAM;YAAE3B,OAAO,EAAE,MAAM;YAAEnB,UAAU,EAAE,2CAA2C;YAAEC,YAAY,EAAE,MAAM;YAAEQ,MAAM,EAAE,mBAAmB;YAAEH,SAAS,EAAE;UAAsC,CAAE;UAAAd,QAAA,eAClN/E,OAAA;YAAKiF,KAAK,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEiB,GAAG,EAAE;YAAO,CAAE;YAAA5B,QAAA,gBACjE/E,OAAA;cAAKiF,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAAH,QAAA,gBACnC/E,OAAA;gBAAK4H,GAAG,EAAEzG,YAAa;gBAAC0G,GAAG,EAAC,SAAS;gBAAC5C,KAAK,EAAE;kBAAEI,KAAK,EAAEX,QAAQ,GAAG,MAAM,GAAG,OAAO;kBAAEY,MAAM,EAAEZ,QAAQ,GAAG,MAAM,GAAG,OAAO;kBAAEqD,SAAS,EAAE,OAAO;kBAAEvC,YAAY,EAAE,KAAK;kBAAEQ,MAAM,EAAE,mBAAmB;kBAAEH,SAAS,EAAE;gBAA+B;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7OvG,OAAA;gBAAQgF,OAAO,EAAExC,WAAY;gBAACyC,KAAK,EAAE;kBAAEC,QAAQ,EAAE,UAAU;kBAAEoD,GAAG,EAAE,MAAM;kBAAElD,KAAK,EAAE,MAAM;kBAAEC,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE,MAAM;kBAAEC,UAAU,EAAE,SAAS;kBAAEW,KAAK,EAAE,OAAO;kBAAEV,YAAY,EAAE,KAAK;kBAAEQ,MAAM,EAAE,iBAAiB;kBAAEJ,MAAM,EAAE,SAAS;kBAAEH,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,cAAc,EAAE,QAAQ;kBAAEQ,QAAQ,EAAE,MAAM;kBAAEU,UAAU,EAAE,MAAM;kBAAEhB,SAAS,EAAE;gBAA+B,CAAE;gBAAAd,QAAA,EAAC;cAAC;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7X,CAAC,eACNvG,OAAA;cAAKiF,KAAK,EAAE;gBAAE8B,IAAI,EAAE;cAAE,CAAE;cAAAhC,QAAA,gBACtB/E,OAAA;gBAAGiF,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAEU,UAAU,EAAE,KAAK;kBAAEX,KAAK,EAAE,SAAS;kBAAEU,MAAM,EAAE,WAAW;kBAAEnB,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEiB,GAAG,EAAE;gBAAM,CAAE;gBAAA5B,QAAA,GAAC,eACxI,EAAC1E,WAAW,GAAG,iBAAiB,GAAG,gBAAgB;cAAA;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACJvG,OAAA;gBAAGiF,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAED,KAAK,EAAE,SAAS;kBAAEU,MAAM,EAAE;gBAAE,CAAE;gBAAA7B,QAAA,EACzD,CAAA9D,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsH,IAAI,KAAI;cAAW;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDvG,OAAA;UAAKiF,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEkB,GAAG,EAAEjC,QAAQ,GAAG,KAAK,GAAG,KAAK;YAAEa,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAEd,QAAQ,GAAG,MAAM,GAAG,MAAM;YAAEgC,OAAO,EAAEhC,QAAQ,GAAG,KAAK,GAAG,KAAK;YAAEsB,MAAM,EAAE;UAAoB,CAAE;UAAAjB,QAAA,gBAC9L/E,OAAA;YACEgF,OAAO,EAAEA,CAAA;cAAA,IAAAwD,qBAAA;cAAA,QAAAA,qBAAA,GAAMlH,YAAY,CAACE,OAAO,cAAAgH,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CxD,KAAK,EAAE;cACLM,UAAU,EAAE,SAAS;cACrBS,MAAM,EAAE,MAAM;cACdR,YAAY,EAAE,KAAK;cACnBH,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAE,SAAS;cACjBG,UAAU,EAAE,eAAe;cAC3BF,SAAS,EAAE;YACb,CAAE;YACF6C,KAAK,EAAErI,WAAW,GAAG,aAAa,GAAG,cAAe;YAAA0E,QAAA,eAEpD/E,OAAA;cAAMiF,KAAK,EAAE;gBAAEiB,KAAK,EAAE,OAAO;gBAAEC,QAAQ,EAAE,MAAM;gBAAEU,UAAU,EAAE;cAAO,CAAE;cAAA9B,QAAA,EAAC;YAAC;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAITvG,OAAA;YACEoI,GAAG,EAAE7G,QAAS;YACdkB,KAAK,EAAE5B,KAAM;YACb8H,QAAQ,EAAGtG,CAAC,IAAKvB,QAAQ,CAACuB,CAAC,CAACP,MAAM,CAACW,KAAK,CAAE;YAC1CmG,SAAS,EAAEtE,aAAc;YACzBuE,WAAW,EAAExI,WAAW,GAAG,mBAAmB,GAAG,oBAAqB;YACtEyI,IAAI,EAAE,CAAE;YACR7D,KAAK,EAAE;cACL8B,IAAI,EAAE,CAAC;cACPf,MAAM,EAAE,MAAM;cACdT,UAAU,EAAE,aAAa;cACzBwD,OAAO,EAAE,MAAM;cACf5C,QAAQ,EAAEzB,QAAQ,GAAG,MAAM,GAAG,MAAM;cACpCwB,KAAK,EAAE,SAAS;cAChBQ,OAAO,EAAEhC,QAAQ,GAAG,WAAW,GAAG,WAAW;cAC7CsE,UAAU,EAAE;YACd;UAAE;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFvG,OAAA;YACEgF,OAAO,EAAElC,WAAY;YACrBmG,QAAQ,EAAE,CAACpI,KAAK,CAACkC,IAAI,CAAC,CAAC,IAAI,CAAC9B,aAAc;YAC1CgE,KAAK,EAAE;cACLM,UAAU,EAAG1E,KAAK,CAACkC,IAAI,CAAC,CAAC,IAAI9B,aAAa,GAAI,SAAS,GAAG,SAAS;cACnE+E,MAAM,EAAE,MAAM;cACdR,YAAY,EAAE,KAAK;cACnBH,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAG/E,KAAK,CAACkC,IAAI,CAAC,CAAC,IAAI9B,aAAa,GAAI,SAAS,GAAG,aAAa;cACnE8E,UAAU,EAAE;YACd,CAAE;YAAAhB,QAAA,eAEF/E,OAAA;cAAMiF,KAAK,EAAE;gBAAEiB,KAAK,EAAGrF,KAAK,CAACkC,IAAI,CAAC,CAAC,IAAI9B,aAAa,GAAI,OAAO,GAAG,SAAS;gBAAEkF,QAAQ,EAAE;cAAO,CAAE;cAAApB,QAAA,EAAC;YAAC;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENvG,OAAA;UAAOoI,GAAG,EAAE9G,YAAa;UAACU,IAAI,EAAC,MAAM;UAACkH,MAAM,EAAC,SAAS;UAACP,QAAQ,EAAEhH,iBAAkB;UAACsD,KAAK,EAAE;YAAEQ,OAAO,EAAE;UAAO;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAElHvG,OAAA;UAAGiF,KAAK,EAAE;YAAEkB,QAAQ,EAAEzB,QAAQ,GAAG,KAAK,GAAG,MAAM;YAAEwB,KAAK,EAAE,SAAS;YAAEiD,SAAS,EAAE,QAAQ;YAAEvC,MAAM,EAAElC,QAAQ,GAAG,WAAW,GAAG;UAAY,CAAE;UAAAK,QAAA,EACpIL,QAAQ,GACJrE,WAAW,GAAG,uBAAuB,GAAG,wBAAwB,GAChEA,WAAW,GAAG,+BAA+B,GAAG;QAAuC;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE3F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC,gBACN,CACH,eAEDvG,OAAA;MAAA+E,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA,eACV,CAAC;AAEP,CAAC;AAACnG,EAAA,CA5eID,mBAAmB;EAAA,QACCR,WAAW,EAClBC,WAAW,EACXC,WAAW;AAAA;AAAAuJ,EAAA,GAHxBjJ,mBAAmB;AA8ezB,eAAeA,mBAAmB;AAAC,IAAAiJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}