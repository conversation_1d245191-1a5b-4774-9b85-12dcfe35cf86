{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoLessons = () => {\n  _s();\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.user) {\n        console.log('✅ Found user in state.user');\n        return state.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState({});\n  const [newComment, setNewComment] = useState('');\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [replyingTo, setReplyingTo] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = video => {\n    console.log('🖼️ Video object for thumbnail:', video); // Debug log to see available fields\n\n    // Check all possible thumbnail field names from database\n    const possibleThumbnailFields = [video.thumbnail,\n    // Most common field name\n    video.thumbnailUrl,\n    // Alternative field name\n    video.image,\n    // Image field\n    video.poster,\n    // Poster field\n    video.posterUrl,\n    // Poster URL field\n    video.cover,\n    // Cover image field\n    video.coverImage,\n    // Cover image field\n    video.previewImage,\n    // Preview image field\n    video.videoThumbnail // Video thumbnail field\n    ];\n\n    // Find the first available thumbnail\n    for (const thumbnailUrl of possibleThumbnailFields) {\n      if (thumbnailUrl && typeof thumbnailUrl === 'string' && thumbnailUrl.trim() !== '') {\n        console.log('✅ Found thumbnail:', thumbnailUrl);\n        return thumbnailUrl;\n      }\n    }\n\n    // Fallback to YouTube thumbnail for YouTube videos only\n    if (video.videoID && !video.videoID.includes('amazonaws.com') && !video.videoUrl) {\n      let videoId = video.videoID;\n      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n      videoId = match ? match[1] : videoId;\n      console.log('📺 Using YouTube thumbnail for video ID:', videoId);\n      return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n    }\n\n    // Final fallback placeholder\n    console.log('🎬 Using placeholder for video:', video.title);\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n  };\n  const getSubjectName = subject => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Comment helper functions\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    console.log('🔍 Getting comments for video ID:', videoId);\n    console.log('🔍 Available comments:', comments);\n    return comments[videoId] || [];\n  };\n  const formatTimeAgo = timestamp => {\n    if (!timestamp) return 'Just now';\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  };\n\n  // Comment handlers\n  const handleAddComment = async () => {\n    if (!newComment.trim() || currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    console.log('💬 Adding comment with user data:', user);\n\n    // Get user name properly with extensive debugging\n    const userName = (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.username) || (user === null || user === void 0 ? void 0 : user.displayName) || 'Student';\n    const fullName = user !== null && user !== void 0 && user.firstName && user !== null && user !== void 0 && user.lastName ? `${user.firstName} ${user.lastName}` : userName;\n    console.log('👤 User name resolution:');\n    console.log('  - user?.name:', user === null || user === void 0 ? void 0 : user.name);\n    console.log('  - user?.firstName:', user === null || user === void 0 ? void 0 : user.firstName);\n    console.log('  - user?.lastName:', user === null || user === void 0 ? void 0 : user.lastName);\n    console.log('  - user?.username:', user === null || user === void 0 ? void 0 : user.username);\n    console.log('  - user?.displayName:', user === null || user === void 0 ? void 0 : user.displayName);\n    console.log('  - Final fullName:', fullName);\n    const comment = {\n      id: Date.now().toString(),\n      text: newComment.trim(),\n      author: fullName,\n      user: (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id),\n      userId: (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id),\n      userRole: user === null || user === void 0 ? void 0 : user.role,\n      isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin',\n      timestamp: new Date().toISOString(),\n      createdAt: new Date().toISOString(),\n      likes: 0,\n      likedBy: [],\n      // Add user profile data\n      userProfile: {\n        name: fullName,\n        firstName: user === null || user === void 0 ? void 0 : user.firstName,\n        lastName: user === null || user === void 0 ? void 0 : user.lastName,\n        username: user === null || user === void 0 ? void 0 : user.username,\n        email: user === null || user === void 0 ? void 0 : user.email,\n        role: user === null || user === void 0 ? void 0 : user.role,\n        avatar: (user === null || user === void 0 ? void 0 : user.avatar) || (user === null || user === void 0 ? void 0 : user.profilePicture)\n      }\n    };\n    console.log('💬 Created comment object:', comment);\n\n    // Try to save comment to backend\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/videos/${video._id || video.id}/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          text: newComment.trim(),\n          videoId: video._id || video.id,\n          author: fullName,\n          userProfile: comment.userProfile\n        })\n      });\n      if (response.ok) {\n        const savedComment = await response.json();\n        console.log('✅ Comment saved to backend:', savedComment);\n\n        // Use the saved comment from backend\n        setComments(prev => ({\n          ...prev,\n          [video._id || video.id]: [...(prev[video._id || video.id] || []), savedComment.data || savedComment]\n        }));\n      } else {\n        console.log('⚠️ Backend save failed, using local storage');\n        // Fallback to local storage\n        setComments(prev => ({\n          ...prev,\n          [video._id || video.id]: [...(prev[video._id || video.id] || []), comment]\n        }));\n      }\n    } catch (error) {\n      console.log('⚠️ Backend error, using local storage:', error);\n      // Fallback to local storage\n      setComments(prev => ({\n        ...prev,\n        [video._id || video.id]: [...(prev[video._id || video.id] || []), comment]\n      }));\n    }\n    setNewComment('');\n  };\n  const handleLikeComment = commentId => {\n    if (!(user !== null && user !== void 0 && user._id) || currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    setComments(prev => ({\n      ...prev,\n      [video.id]: (prev[video.id] || []).map(comment => {\n        if (comment.id === commentId) {\n          var _comment$likedBy;\n          const isLiked = (_comment$likedBy = comment.likedBy) === null || _comment$likedBy === void 0 ? void 0 : _comment$likedBy.includes(user._id);\n          return {\n            ...comment,\n            likes: isLiked ? comment.likes - 1 : comment.likes + 1,\n            likedBy: isLiked ? comment.likedBy.filter(id => id !== user._id) : [...(comment.likedBy || []), user._id]\n          };\n        }\n        return comment;\n      })\n    }));\n  };\n  const handleDeleteComment = commentId => {\n    if (currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    setComments(prev => ({\n      ...prev,\n      [video.id]: (prev[video.id] || []).filter(comment => comment.id !== commentId)\n    }));\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('🎥 Attempting to fetch videos from database...');\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        var _response, _response2;\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n        if ((_response = response) !== null && _response !== void 0 && _response.success && (_response2 = response) !== null && _response2 !== void 0 && _response2.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n\n          // Debug: Log first video structure to see available thumbnail fields\n          if (videos.length > 0) {\n            console.log('🔍 FIRST VIDEO STRUCTURE:', videos[0]);\n            console.log('🔍 AVAILABLE FIELDS:', Object.keys(videos[0]));\n            console.log('🔍 THUMBNAIL FIELDS CHECK:');\n            console.log('  - thumbnail:', videos[0].thumbnail);\n            console.log('  - thumbnailUrl:', videos[0].thumbnailUrl);\n            console.log('  - image:', videos[0].image);\n            console.log('  - poster:', videos[0].poster);\n            console.log('  - posterUrl:', videos[0].posterUrl);\n            console.log('  - cover:', videos[0].cover);\n            console.log('  - coverImage:', videos[0].coverImage);\n            console.log('  - previewImage:', videos[0].previewImage);\n            console.log('  - videoThumbnail:', videos[0].videoThumbnail);\n          }\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          var _response3, _response3$data, _response4, _response4$data;\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n          if ((_response3 = response) !== null && _response3 !== void 0 && (_response3$data = _response3.data) !== null && _response3$data !== void 0 && _response3$data.success && (_response4 = response) !== null && _response4 !== void 0 && (_response4$data = _response4.data) !== null && _response4$data !== void 0 && _response4$data.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          var _response5, _response5$data, _response6, _response6$data;\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n          if ((_response5 = response) !== null && _response5 !== void 0 && (_response5$data = _response5.data) !== null && _response5$data !== void 0 && _response5$data.success && (_response6 = response) !== null && _response6 !== void 0 && (_response6$data = _response6.data) !== null && _response6$data !== void 0 && _response6$data.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item => {\n              var _item$title;\n              return item.type === 'video' || item.videoUrl || item.videoID || ((_item$title = item.title) === null || _item$title === void 0 ? void 0 : _item$title.toLowerCase().includes('video'));\n            });\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel || !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass || !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject || !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      var _video$title, _video$subject, _video$topic;\n      const matchesSearch = !searchTerm || ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isKiswahili ? 'Masomo ya Video' : 'Video Lessons'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: isKiswahili ? 'Tafuta video...' : 'Search videos...',\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLevel,\n          onChange: e => setSelectedLevel(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"primary\",\n            children: isKiswahili ? 'Msingi' : 'Primary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"secondary\",\n            children: isKiswahili ? 'Sekondari' : 'Secondary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advance\",\n            children: isKiswahili ? 'Juu' : 'Advanced'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedClass,\n          onChange: e => setSelectedClass(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedSubject,\n          onChange: e => setSelectedSubject(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => {\n          var _ref, _ref$charAt;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-item\",\n            children: currentVideoIndex === index ?\n            /*#__PURE__*/\n            /* Video Player - Replaces the thumbnail when playing */\n            _jsxDEV(\"div\", {\n              className: \"inline-video-player\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-style-layout\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-player\",\n                  children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: ref => setVideoRef(ref),\n                    controls: true,\n                    autoPlay: true,\n                    playsInline: true,\n                    preload: \"metadata\",\n                    width: \"100%\",\n                    height: \"100%\",\n                    poster: getThumbnailUrl(video),\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      backgroundColor: '#000',\n                      objectFit: 'contain'\n                    },\n                    onError: e => setVideoError(`Failed to load video: ${video.title}`),\n                    onCanPlay: () => setVideoError(null),\n                    crossOrigin: \"anonymous\",\n                    children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                      src: video.signedVideoUrl || video.videoUrl,\n                      type: \"video/mp4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 549,\n                      columnNumber: 29\n                    }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                      kind: \"subtitles\",\n                      src: subtitle.url,\n                      srcLang: subtitle.language,\n                      label: subtitle.languageName,\n                      default: subtitle.isDefault || index === 0\n                    }, `${subtitle.language}-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 551,\n                      columnNumber: 31\n                    }, this)), \"Your browser does not support the video tag.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 27\n                  }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                    src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                    title: video.title,\n                    frameBorder: \"0\",\n                    allowFullScreen: true,\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      border: 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"video-error\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"error-icon\",\n                      children: \"\\u26A0\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      children: \"Video Unavailable\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: videoError || \"This video cannot be played at the moment.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"youtube-video-title\",\n                    children: video.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-video-meta\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: getSubjectName(video.subject)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 582,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 583,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Class \", video.className || video.class]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 27\n                    }, this), video.level && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 587,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: video.level\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 588,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-video-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `youtube-action-btn ${commentsExpanded ? 'active' : ''}`,\n                      onClick: () => setCommentsExpanded(!commentsExpanded),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 597,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Comments\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 598,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"youtube-action-btn\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83D\\uDC4D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 601,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Like\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 602,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"youtube-action-btn\",\n                      onClick: () => setCurrentVideoIndex(null),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2715\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 608,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Close\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 609,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 604,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 23\n                }, this), commentsExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comments-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comments-header\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [getCurrentVideoComments().length, \" Comments\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 618,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comment-input\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-comment-avatar\",\n                      children: (_ref = (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.username) || 'Student') === null || _ref === void 0 ? void 0 : (_ref$charAt = _ref.charAt(0)) === null || _ref$charAt === void 0 ? void 0 : _ref$charAt.toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 623,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                        className: \"youtube-comment-input-field\",\n                        value: newComment,\n                        onChange: e => setNewComment(e.target.value),\n                        placeholder: \"Add a comment...\",\n                        rows: \"1\",\n                        style: {\n                          minHeight: '20px',\n                          resize: 'none',\n                          overflow: 'hidden'\n                        },\n                        onInput: e => {\n                          e.target.style.height = 'auto';\n                          e.target.style.height = e.target.scrollHeight + 'px';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 627,\n                        columnNumber: 31\n                      }, this), newComment.trim() && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment-actions\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"youtube-comment-btn cancel\",\n                          onClick: () => setNewComment(''),\n                          children: \"Cancel\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 645,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"youtube-comment-btn submit\",\n                          onClick: handleAddComment,\n                          disabled: !newComment.trim(),\n                          children: \"Comment\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 651,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 644,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 626,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comments-list\",\n                    children: getCurrentVideoComments().length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        textAlign: 'center',\n                        padding: '40px 0',\n                        color: '#606060'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '48px',\n                          marginBottom: '16px'\n                        },\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No comments yet. Be the first to share your thoughts!\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 668,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 31\n                    }, this) : getCurrentVideoComments().map(comment => {\n                      var _comment$author, _comment$author$charA, _comment$likedBy2, _comment$likedBy3;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-avatar\",\n                          children: comment.avatar || ((_comment$author = comment.author) === null || _comment$author === void 0 ? void 0 : (_comment$author$charA = _comment$author.charAt(0)) === null || _comment$author$charA === void 0 ? void 0 : _comment$author$charA.toUpperCase()) || \"A\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 673,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-content\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-header\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"youtube-comment-author\",\n                              children: comment.author\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 678,\n                              columnNumber: 39\n                            }, this), (comment.userRole === 'admin' || comment.isAdmin) && /*#__PURE__*/_jsxDEV(MdVerified, {\n                              style: {\n                                color: '#1d9bf0',\n                                fontSize: '12px',\n                                marginLeft: '4px'\n                              },\n                              title: \"Verified Admin\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 680,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"youtube-comment-time\",\n                              children: formatTimeAgo(comment.createdAt || comment.timestamp)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 682,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 677,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-text\",\n                            children: comment.text\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 686,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-actions\",\n                            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => handleLikeComment(comment._id || comment.id),\n                              className: `youtube-comment-action ${(_comment$likedBy2 = comment.likedBy) !== null && _comment$likedBy2 !== void 0 && _comment$likedBy2.includes(user === null || user === void 0 ? void 0 : user._id) ? 'liked' : ''}`,\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                children: (_comment$likedBy3 = comment.likedBy) !== null && _comment$likedBy3 !== void 0 && _comment$likedBy3.includes(user === null || user === void 0 ? void 0 : user._id) ? '👍' : '👍'\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 694,\n                                columnNumber: 41\n                              }, this), comment.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: comment.likes\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 695,\n                                columnNumber: 63\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 690,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"youtube-comment-action\",\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: \"\\uD83D\\uDC4E\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 698,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 697,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"youtube-comment-action\",\n                              children: \"Reply\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 700,\n                              columnNumber: 39\n                            }, this), comment.user === (user === null || user === void 0 ? void 0 : user._id) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-action\",\n                                children: \"Edit\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 705,\n                                columnNumber: 43\n                              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-action\",\n                                onClick: () => {\n                                  if (window.confirm('Are you sure you want to delete this comment?')) {\n                                    handleDeleteComment(comment._id || comment.id);\n                                  }\n                                },\n                                children: \"Delete\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 708,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 689,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 676,\n                          columnNumber: 35\n                        }, this)]\n                      }, comment._id || comment.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 672,\n                        columnNumber: 33\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 664,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 19\n            }, this) :\n            /*#__PURE__*/\n            /* Video Card - Shows thumbnail when not playing */\n            _jsxDEV(\"div\", {\n              className: \"video-card\",\n              onClick: () => handleShowVideo(index),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-thumbnail\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getThumbnailUrl(video),\n                  alt: video.title,\n                  className: \"thumbnail-image\",\n                  loading: \"lazy\",\n                  onError: e => {\n                    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                      let videoId = video.videoID;\n                      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                      videoId = match ? match[1] : videoId;\n                      const fallbacks = [`https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`, `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4='];\n                      const currentSrc = e.target.src;\n                      const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop().split('.')[0]));\n                      if (currentIndex < fallbacks.length - 1) {\n                        e.target.src = fallbacks[currentIndex + 1];\n                      }\n                    } else {\n                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"play-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                    className: \"play-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-duration\",\n                  children: video.duration || \"Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 23\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 27\n                  }, this), \"CC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-tags\",\n                  children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"topic-tag\",\n                    children: video.topic\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 41\n                  }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"shared-tag\",\n                    children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 805,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 807,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 455,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoLessons, \"sUl5UJEZU46h2aSoRLty1l9iUvM=\", false, function () {\n  return [useSelector];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useSelector", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "TbAlertTriangle", "MdVerified", "getStudyMaterial", "getAllVideos", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoLessons", "_s", "user", "state", "console", "log", "Object", "keys", "users", "auth", "storedUser", "localStorage", "getItem", "JSON", "parse", "e", "error", "videos", "setVideos", "loading", "setLoading", "setError", "searchTerm", "setSearchTerm", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "currentVideoIndex", "setCurrentVideoIndex", "videoRef", "setVideoRef", "videoError", "setVideoError", "comments", "setComments", "newComment", "setNewComment", "commentsExpanded", "setCommentsExpanded", "replyingTo", "setReplyingTo", "isKiswahili", "getThumbnailUrl", "video", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "thumbnail", "thumbnailUrl", "image", "poster", "posterUrl", "cover", "coverImage", "previewImage", "videoThumbnail", "trim", "videoID", "includes", "videoUrl", "videoId", "match", "title", "getSubjectName", "subject", "subjectMap", "getCurrentVideoComments", "filteredAndSortedVideos", "_id", "id", "formatTimeAgo", "timestamp", "now", "Date", "time", "diffInSeconds", "Math", "floor", "handleAddComment", "userName", "name", "firstName", "username", "displayName", "fullName", "lastName", "comment", "toString", "text", "author", "userId", "userRole", "role", "isAdmin", "toISOString", "createdAt", "likes", "<PERSON><PERSON><PERSON>", "userProfile", "email", "avatar", "profilePicture", "response", "fetch", "process", "env", "REACT_APP_API_URL", "method", "headers", "body", "stringify", "ok", "savedComment", "json", "prev", "data", "handleLikeComment", "commentId", "map", "_comment$likedBy", "isLiked", "filter", "handleDeleteComment", "handleShowVideo", "index", "signedVideoUrl", "warn", "fetchVideos", "_response", "_response2", "success", "length", "message", "_response3", "_response3$data", "_response4", "_response4$data", "filters", "level", "type", "_response5", "_response5$data", "_response6", "_response6$data", "allData", "item", "_item$title", "toLowerCase", "filtered", "matchesLevel", "matchesClass", "className", "class", "matchesSubject", "err", "_video$title", "_video$subject", "_video$topic", "matchesSearch", "topic", "sort", "a", "b", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "target", "onClick", "_ref", "_ref$charAt", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "style", "backgroundColor", "objectFit", "onError", "onCanPlay", "crossOrigin", "src", "subtitles", "subtitle", "kind", "url", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "char<PERSON>t", "toUpperCase", "flex", "rows", "minHeight", "resize", "overflow", "onInput", "scrollHeight", "disabled", "textAlign", "padding", "color", "fontSize", "marginBottom", "_comment$author", "_comment$author$charA", "_comment$likedBy2", "_comment$likedBy3", "marginLeft", "window", "confirm", "alt", "fallbacks", "currentSrc", "currentIndex", "findIndex", "split", "pop", "duration", "sharedFromClass", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\n\nconst VideoLessons = () => {\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.user) {\n        console.log('✅ Found user in state.user');\n        return state.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState({});\n  const [newComment, setNewComment] = useState('');\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [replyingTo, setReplyingTo] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = (video) => {\n    console.log('🖼️ Video object for thumbnail:', video); // Debug log to see available fields\n\n    // Check all possible thumbnail field names from database\n    const possibleThumbnailFields = [\n      video.thumbnail,      // Most common field name\n      video.thumbnailUrl,   // Alternative field name\n      video.image,          // Image field\n      video.poster,         // Poster field\n      video.posterUrl,      // Poster URL field\n      video.cover,          // Cover image field\n      video.coverImage,     // Cover image field\n      video.previewImage,   // Preview image field\n      video.videoThumbnail  // Video thumbnail field\n    ];\n\n    // Find the first available thumbnail\n    for (const thumbnailUrl of possibleThumbnailFields) {\n      if (thumbnailUrl && typeof thumbnailUrl === 'string' && thumbnailUrl.trim() !== '') {\n        console.log('✅ Found thumbnail:', thumbnailUrl);\n        return thumbnailUrl;\n      }\n    }\n\n    // Fallback to YouTube thumbnail for YouTube videos only\n    if (video.videoID && !video.videoID.includes('amazonaws.com') && !video.videoUrl) {\n      let videoId = video.videoID;\n      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n      videoId = match ? match[1] : videoId;\n      console.log('📺 Using YouTube thumbnail for video ID:', videoId);\n      return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n    }\n\n    // Final fallback placeholder\n    console.log('🎬 Using placeholder for video:', video.title);\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n  };\n\n  const getSubjectName = (subject) => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Comment helper functions\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n    console.log('🔍 Getting comments for video ID:', videoId);\n    console.log('🔍 Available comments:', comments);\n    return comments[videoId] || [];\n  };\n\n  const formatTimeAgo = (timestamp) => {\n    if (!timestamp) return 'Just now';\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  };\n\n  // Comment handlers\n  const handleAddComment = async () => {\n    if (!newComment.trim() || currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n\n    console.log('💬 Adding comment with user data:', user);\n\n    // Get user name properly with extensive debugging\n    const userName = user?.name || user?.firstName || user?.username || user?.displayName || 'Student';\n    const fullName = user?.firstName && user?.lastName\n      ? `${user.firstName} ${user.lastName}`\n      : userName;\n\n    console.log('👤 User name resolution:');\n    console.log('  - user?.name:', user?.name);\n    console.log('  - user?.firstName:', user?.firstName);\n    console.log('  - user?.lastName:', user?.lastName);\n    console.log('  - user?.username:', user?.username);\n    console.log('  - user?.displayName:', user?.displayName);\n    console.log('  - Final fullName:', fullName);\n\n    const comment = {\n      id: Date.now().toString(),\n      text: newComment.trim(),\n      author: fullName,\n      user: user?._id || user?.id,\n      userId: user?._id || user?.id,\n      userRole: user?.role,\n      isAdmin: user?.role === 'admin',\n      timestamp: new Date().toISOString(),\n      createdAt: new Date().toISOString(),\n      likes: 0,\n      likedBy: [],\n      // Add user profile data\n      userProfile: {\n        name: fullName,\n        firstName: user?.firstName,\n        lastName: user?.lastName,\n        username: user?.username,\n        email: user?.email,\n        role: user?.role,\n        avatar: user?.avatar || user?.profilePicture\n      }\n    };\n\n    console.log('💬 Created comment object:', comment);\n\n    // Try to save comment to backend\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/videos/${video._id || video.id}/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n        },\n        body: JSON.stringify({\n          text: newComment.trim(),\n          videoId: video._id || video.id,\n          author: fullName,\n          userProfile: comment.userProfile\n        })\n      });\n\n      if (response.ok) {\n        const savedComment = await response.json();\n        console.log('✅ Comment saved to backend:', savedComment);\n\n        // Use the saved comment from backend\n        setComments(prev => ({\n          ...prev,\n          [video._id || video.id]: [...(prev[video._id || video.id] || []), savedComment.data || savedComment]\n        }));\n      } else {\n        console.log('⚠️ Backend save failed, using local storage');\n        // Fallback to local storage\n        setComments(prev => ({\n          ...prev,\n          [video._id || video.id]: [...(prev[video._id || video.id] || []), comment]\n        }));\n      }\n    } catch (error) {\n      console.log('⚠️ Backend error, using local storage:', error);\n      // Fallback to local storage\n      setComments(prev => ({\n        ...prev,\n        [video._id || video.id]: [...(prev[video._id || video.id] || []), comment]\n      }));\n    }\n\n    setNewComment('');\n  };\n\n  const handleLikeComment = (commentId) => {\n    if (!user?._id || currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    setComments(prev => ({\n      ...prev,\n      [video.id]: (prev[video.id] || []).map(comment => {\n        if (comment.id === commentId) {\n          const isLiked = comment.likedBy?.includes(user._id);\n          return {\n            ...comment,\n            likes: isLiked ? comment.likes - 1 : comment.likes + 1,\n            likedBy: isLiked\n              ? comment.likedBy.filter(id => id !== user._id)\n              : [...(comment.likedBy || []), user._id]\n          };\n        }\n        return comment;\n      })\n    }));\n  };\n\n  const handleDeleteComment = (commentId) => {\n    if (currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    setComments(prev => ({\n      ...prev,\n      [video.id]: (prev[video.id] || []).filter(comment => comment.id !== commentId)\n    }));\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🎥 Attempting to fetch videos from database...');\n\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n\n        if (response?.success && response?.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n\n          // Debug: Log first video structure to see available thumbnail fields\n          if (videos.length > 0) {\n            console.log('🔍 FIRST VIDEO STRUCTURE:', videos[0]);\n            console.log('🔍 AVAILABLE FIELDS:', Object.keys(videos[0]));\n            console.log('🔍 THUMBNAIL FIELDS CHECK:');\n            console.log('  - thumbnail:', videos[0].thumbnail);\n            console.log('  - thumbnailUrl:', videos[0].thumbnailUrl);\n            console.log('  - image:', videos[0].image);\n            console.log('  - poster:', videos[0].poster);\n            console.log('  - posterUrl:', videos[0].posterUrl);\n            console.log('  - cover:', videos[0].cover);\n            console.log('  - coverImage:', videos[0].coverImage);\n            console.log('  - previewImage:', videos[0].previewImage);\n            console.log('  - videoThumbnail:', videos[0].videoThumbnail);\n          }\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item =>\n              item.type === 'video' ||\n              item.videoUrl ||\n              item.videoID ||\n              item.title?.toLowerCase().includes('video')\n            );\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' ||\n                              video.level === selectedLevel ||\n                              !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' ||\n                              video.className === selectedClass ||\n                              video.class === selectedClass ||\n                              !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' ||\n                                video.subject === selectedSubject ||\n                                !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      const matchesSearch = !searchTerm ||\n        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());\n\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n\n  return (\n    <div className=\"video-lessons-container\">\n      <div className=\"video-lessons-header\">\n        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>\n        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"video-controls\">\n        <div className=\"search-section\">\n          <input\n            type=\"text\"\n            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n\n        <div className=\"filter-section\">\n          <select\n            value={selectedLevel}\n            onChange={(e) => setSelectedLevel(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"primary\">{isKiswahili ? 'Msingi' : 'Primary'}</option>\n            <option value=\"secondary\">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>\n            <option value=\"advance\">{isKiswahili ? 'Juu' : 'Advanced'}</option>\n          </select>\n\n          <select\n            value={selectedClass}\n            onChange={(e) => setSelectedClass(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n            {/* Add class options based on selected level */}\n          </select>\n\n          <select\n            value={selectedSubject}\n            onChange={(e) => setSelectedSubject(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n            {/* Add subject options */}\n          </select>\n        </div>\n      </div>\n\n      {/* Video Content */}\n      <div className=\"video-content\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-item\">\n                {currentVideoIndex === index ? (\n                  /* Video Player - Replaces the thumbnail when playing */\n                  <div className=\"inline-video-player\">\n                    <div className=\"youtube-style-layout\">\n                      <div className=\"youtube-video-player\">\n                        {video.videoUrl ? (\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"100%\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            }}\n                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}\n                            onCanPlay={() => setVideoError(null)}\n                            crossOrigin=\"anonymous\"\n                          >\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n                            Your browser does not support the video tag.\n                          </video>\n                        ) : video.videoID ? (\n                          <iframe\n                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                            title={video.title}\n                            frameBorder=\"0\"\n                            allowFullScreen\n                            style={{ width: '100%', height: '100%', border: 'none' }}\n                          ></iframe>\n                        ) : (\n                          <div className=\"video-error\">\n                            <div className=\"error-icon\">⚠️</div>\n                            <h3>Video Unavailable</h3>\n                            <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"youtube-video-info\">\n                        <h1 className=\"youtube-video-title\">{video.title}</h1>\n                        <div className=\"youtube-video-meta\">\n                          <span>{getSubjectName(video.subject)}</span>\n                          <span>•</span>\n                          <span>Class {video.className || video.class}</span>\n                          {video.level && (\n                            <>\n                              <span>•</span>\n                              <span>{video.level}</span>\n                            </>\n                          )}\n                        </div>\n                        <div className=\"youtube-video-actions\">\n                          <button\n                            className={`youtube-action-btn ${commentsExpanded ? 'active' : ''}`}\n                            onClick={() => setCommentsExpanded(!commentsExpanded)}\n                          >\n                            <span>💬</span>\n                            <span>Comments</span>\n                          </button>\n                          <button className=\"youtube-action-btn\">\n                            <span>👍</span>\n                            <span>Like</span>\n                          </button>\n                          <button\n                            className=\"youtube-action-btn\"\n                            onClick={() => setCurrentVideoIndex(null)}\n                          >\n                            <span>✕</span>\n                            <span>Close</span>\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {commentsExpanded && (\n                        <div className=\"youtube-comments-section\">\n                          <div className=\"youtube-comments-header\">\n                            <span>{getCurrentVideoComments().length} Comments</span>\n                          </div>\n\n                          {/* Add Comment */}\n                          <div className=\"youtube-comment-input\">\n                            <div className=\"youtube-comment-avatar\">\n                              {(user?.name || user?.firstName || user?.username || 'Student')?.charAt(0)?.toUpperCase()}\n                            </div>\n                            <div style={{ flex: 1 }}>\n                              <textarea\n                                className=\"youtube-comment-input-field\"\n                                value={newComment}\n                                onChange={(e) => setNewComment(e.target.value)}\n                                placeholder=\"Add a comment...\"\n                                rows=\"1\"\n                                style={{\n                                  minHeight: '20px',\n                                  resize: 'none',\n                                  overflow: 'hidden'\n                                }}\n                                onInput={(e) => {\n                                  e.target.style.height = 'auto';\n                                  e.target.style.height = e.target.scrollHeight + 'px';\n                                }}\n                              />\n                              {newComment.trim() && (\n                                <div className=\"youtube-comment-actions\">\n                                  <button\n                                    className=\"youtube-comment-btn cancel\"\n                                    onClick={() => setNewComment('')}\n                                  >\n                                    Cancel\n                                  </button>\n                                  <button\n                                    className=\"youtube-comment-btn submit\"\n                                    onClick={handleAddComment}\n                                    disabled={!newComment.trim()}\n                                  >\n                                    Comment\n                                  </button>\n                                </div>\n                              )}\n                            </div>\n                          </div>\n\n                          {/* Comments List */}\n                          <div className=\"youtube-comments-list\">\n                            {getCurrentVideoComments().length === 0 ? (\n                              <div style={{ textAlign: 'center', padding: '40px 0', color: '#606060' }}>\n                                <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                                <p>No comments yet. Be the first to share your thoughts!</p>\n                              </div>\n                            ) : (\n                              getCurrentVideoComments().map((comment) => (\n                                <div key={comment._id || comment.id} className=\"youtube-comment\">\n                                  <div className=\"youtube-comment-avatar\">\n                                    {comment.avatar || comment.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                  </div>\n                                  <div className=\"youtube-comment-content\">\n                                    <div className=\"youtube-comment-header\">\n                                      <span className=\"youtube-comment-author\">{comment.author}</span>\n                                      {(comment.userRole === 'admin' || comment.isAdmin) && (\n                                        <MdVerified style={{ color: '#1d9bf0', fontSize: '12px', marginLeft: '4px' }} title=\"Verified Admin\" />\n                                      )}\n                                      <span className=\"youtube-comment-time\">\n                                        {formatTimeAgo(comment.createdAt || comment.timestamp)}\n                                      </span>\n                                    </div>\n                                    <div className=\"youtube-comment-text\">\n                                      {comment.text}\n                                    </div>\n                                    <div className=\"youtube-comment-actions\">\n                                      <button\n                                        onClick={() => handleLikeComment(comment._id || comment.id)}\n                                        className={`youtube-comment-action ${comment.likedBy?.includes(user?._id) ? 'liked' : ''}`}\n                                      >\n                                        <span>{comment.likedBy?.includes(user?._id) ? '👍' : '👍'}</span>\n                                        {comment.likes > 0 && <span>{comment.likes}</span>}\n                                      </button>\n                                      <button className=\"youtube-comment-action\">\n                                        <span>👎</span>\n                                      </button>\n                                      <button className=\"youtube-comment-action\">\n                                        Reply\n                                      </button>\n                                      {comment.user === user?._id && (\n                                        <>\n                                          <button className=\"youtube-comment-action\">\n                                            Edit\n                                          </button>\n                                          <button\n                                            className=\"youtube-comment-action\"\n                                            onClick={() => {\n                                              if (window.confirm('Are you sure you want to delete this comment?')) {\n                                                handleDeleteComment(comment._id || comment.id);\n                                              }\n                                            }}\n                                          >\n                                            Delete\n                                          </button>\n                                        </>\n                                      )}\n                                    </div>\n                                  </div>\n                                </div>\n                              ))\n                            )}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                ) : (\n                  /* Video Card - Shows thumbnail when not playing */\n                  <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                    <div className=\"video-card-thumbnail\">\n                      <img\n                        src={getThumbnailUrl(video)}\n                        alt={video.title}\n                        className=\"thumbnail-image\"\n                        loading=\"lazy\"\n                        onError={(e) => {\n                          if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                            let videoId = video.videoID;\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                            const fallbacks = [\n                              `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n                              `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                              `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                              `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                              'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4='\n                            ];\n                            const currentSrc = e.target.src;\n                            const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop().split('.')[0]));\n                            if (currentIndex < fallbacks.length - 1) {\n                              e.target.src = fallbacks[currentIndex + 1];\n                            }\n                          } else {\n                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n                          }\n                        }}\n                      />\n                      <div className=\"play-overlay\">\n                        <FaPlayCircle className=\"play-icon\" />\n                      </div>\n                      <div className=\"video-duration\">\n                        {video.duration || \"Video\"}\n                      </div>\n                      {video.subtitles && video.subtitles.length > 0 && (\n                        <div className=\"subtitle-badge\">\n                          <TbInfoCircle />\n                          CC\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"video-card-content\">\n                      <h3 className=\"video-title\">{video.title}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                        <span className=\"video-class\">\n                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                            ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)\n                            : `Form ${video.className || video.class}`}\n                        </span>\n                      </div>\n                      <div className=\"video-tags\">\n                        {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                        {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                          <span className=\"shared-tag\">\n                            {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}\n                            {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                              ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)\n                              : `Form ${video.sharedFromClass}`}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAMC,IAAI,GAAGd,WAAW,CAACe,KAAK,IAAI;IAChC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,KAAK,CAAC;MACpDC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,MAAM,CAACC,IAAI,CAACJ,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;;MAEjE;MACA,IAAIA,KAAK,IAAIA,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK,CAACN,IAAI,EAAE;QAC5CE,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,OAAOF,KAAK,CAACK,KAAK,CAACN,IAAI;MACzB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACD,IAAI,EAAE;QACvBE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,OAAOF,KAAK,CAACD,IAAI;MACnB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACM,IAAI,CAACP,IAAI,EAAE;QAC1CE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAOF,KAAK,CAACM,IAAI,CAACP,IAAI;MACxB;MAEAE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMK,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIF,UAAU,EAAE;QACd,IAAI;UACFN,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,OAAOQ,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;QAC/B,CAAC,CAAC,OAAOK,CAAC,EAAE;UACVX,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C;MACF;MAEAD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,OAAO,IAAI;IACb,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,KAAK,CAAC;MACrD,OAAO,IAAI;IACb;EACF,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,KAAK,EAAEK,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4C,eAAe,EAAEC,kBAAkB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM4D,WAAW,GAAGpB,aAAa,KAAK,mBAAmB;;EAEzD;EACA,MAAMqB,eAAe,GAAIC,KAAK,IAAK;IACjC1C,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEyC,KAAK,CAAC,CAAC,CAAC;;IAEvD;IACA,MAAMC,uBAAuB,GAAG,CAC9BD,KAAK,CAACE,SAAS;IAAO;IACtBF,KAAK,CAACG,YAAY;IAAI;IACtBH,KAAK,CAACI,KAAK;IAAW;IACtBJ,KAAK,CAACK,MAAM;IAAU;IACtBL,KAAK,CAACM,SAAS;IAAO;IACtBN,KAAK,CAACO,KAAK;IAAW;IACtBP,KAAK,CAACQ,UAAU;IAAM;IACtBR,KAAK,CAACS,YAAY;IAAI;IACtBT,KAAK,CAACU,cAAc,CAAE;IAAA,CACvB;;IAED;IACA,KAAK,MAAMP,YAAY,IAAIF,uBAAuB,EAAE;MAClD,IAAIE,YAAY,IAAI,OAAOA,YAAY,KAAK,QAAQ,IAAIA,YAAY,CAACQ,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAClFrD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4C,YAAY,CAAC;QAC/C,OAAOA,YAAY;MACrB;IACF;;IAEA;IACA,IAAIH,KAAK,CAACY,OAAO,IAAI,CAACZ,KAAK,CAACY,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAACb,KAAK,CAACc,QAAQ,EAAE;MAChF,IAAIC,OAAO,GAAGf,KAAK,CAACY,OAAO;MAC3B,MAAMI,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;MACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACpCzD,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEwD,OAAO,CAAC;MAChE,OAAQ,8BAA6BA,OAAQ,gBAAe;IAC9D;;IAEA;IACAzD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEyC,KAAK,CAACiB,KAAK,CAAC;IAC3D,OAAO,4cAA4c;EACrd,CAAC;EAED,MAAMC,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,UAAU,GAAG;MACjB,aAAa,EAAEtB,WAAW,GAAG,UAAU,GAAG,aAAa;MACvD,SAAS,EAAEA,WAAW,GAAG,YAAY,GAAG,SAAS;MACjD,WAAW,EAAE,WAAW;MACxB,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG,SAAS;MAC9C,gBAAgB,EAAEA,WAAW,GAAG,kBAAkB,GAAG,gBAAgB;MACrE,QAAQ,EAAEA,WAAW,GAAG,OAAO,GAAG,QAAQ;MAC1C,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,WAAW,GAAG,WAAW;MACpD,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,OAAO,GAAG,WAAW;MAChD,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG;IACvC,CAAC;IACD,OAAOsB,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO;EACvC,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAIrC,iBAAiB,KAAK,IAAI,EAAE,OAAO,EAAE;IACzC,MAAMgB,KAAK,GAAGsB,uBAAuB,CAACtC,iBAAiB,CAAC;IACxD,MAAM+B,OAAO,GAAG,CAAAf,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuB,GAAG,MAAIvB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwB,EAAE;IACvClE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEwD,OAAO,CAAC;IACzDzD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE+B,QAAQ,CAAC;IAC/C,OAAOA,QAAQ,CAACyB,OAAO,CAAC,IAAI,EAAE;EAChC,CAAC;EAED,MAAMU,aAAa,GAAIC,SAAS,IAAK;IACnC,IAAI,CAACA,SAAS,EAAE,OAAO,UAAU;IACjC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,SAAS,CAAC;IAChC,MAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,GAAGE,IAAI,IAAI,IAAI,CAAC;IAErD,IAAIC,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAE,cAAa;IAChF,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAE,YAAW;IACjF,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAE,WAAU;EACxD,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACzC,UAAU,CAACmB,IAAI,CAAC,CAAC,IAAI3B,iBAAiB,KAAK,IAAI,EAAE;IAEtD,MAAMgB,KAAK,GAAGsB,uBAAuB,CAACtC,iBAAiB,CAAC;IAExD1B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEH,IAAI,CAAC;;IAEtD;IACA,MAAM8E,QAAQ,GAAG,CAAA9E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+E,IAAI,MAAI/E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgF,SAAS,MAAIhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiF,QAAQ,MAAIjF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkF,WAAW,KAAI,SAAS;IAClG,MAAMC,QAAQ,GAAGnF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgF,SAAS,IAAIhF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoF,QAAQ,GAC7C,GAAEpF,IAAI,CAACgF,SAAU,IAAGhF,IAAI,CAACoF,QAAS,EAAC,GACpCN,QAAQ;IAEZ5E,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvCD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+E,IAAI,CAAC;IAC1C7E,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgF,SAAS,CAAC;IACpD9E,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,QAAQ,CAAC;IAClDlF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiF,QAAQ,CAAC;IAClD/E,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkF,WAAW,CAAC;IACxDhF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEgF,QAAQ,CAAC;IAE5C,MAAME,OAAO,GAAG;MACdjB,EAAE,EAAEI,IAAI,CAACD,GAAG,CAAC,CAAC,CAACe,QAAQ,CAAC,CAAC;MACzBC,IAAI,EAAEnD,UAAU,CAACmB,IAAI,CAAC,CAAC;MACvBiC,MAAM,EAAEL,QAAQ;MAChBnF,IAAI,EAAE,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,GAAG,MAAInE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,EAAE;MAC3BqB,MAAM,EAAE,CAAAzF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,GAAG,MAAInE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,EAAE;MAC7BsB,QAAQ,EAAE1F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2F,IAAI;MACpBC,OAAO,EAAE,CAAA5F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2F,IAAI,MAAK,OAAO;MAC/BrB,SAAS,EAAE,IAAIE,IAAI,CAAC,CAAC,CAACqB,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAItB,IAAI,CAAC,CAAC,CAACqB,WAAW,CAAC,CAAC;MACnCE,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE,EAAE;MACX;MACAC,WAAW,EAAE;QACXlB,IAAI,EAAEI,QAAQ;QACdH,SAAS,EAAEhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgF,SAAS;QAC1BI,QAAQ,EAAEpF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoF,QAAQ;QACxBH,QAAQ,EAAEjF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiF,QAAQ;QACxBiB,KAAK,EAAElG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,KAAK;QAClBP,IAAI,EAAE3F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2F,IAAI;QAChBQ,MAAM,EAAE,CAAAnG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmG,MAAM,MAAInG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoG,cAAc;MAC9C;IACF,CAAC;IAEDlG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkF,OAAO,CAAC;;IAElD;IACA,IAAI;MACF,MAAMgB,QAAQ,GAAG,MAAMC,KAAK,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,eAAc7D,KAAK,CAACuB,GAAG,IAAIvB,KAAK,CAACwB,EAAG,WAAU,EAAE;QACvIsC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAASlG,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D,CAAC;QACDkG,IAAI,EAAEjG,IAAI,CAACkG,SAAS,CAAC;UACnBtB,IAAI,EAAEnD,UAAU,CAACmB,IAAI,CAAC,CAAC;UACvBI,OAAO,EAAEf,KAAK,CAACuB,GAAG,IAAIvB,KAAK,CAACwB,EAAE;UAC9BoB,MAAM,EAAEL,QAAQ;UAChBc,WAAW,EAAEZ,OAAO,CAACY;QACvB,CAAC;MACH,CAAC,CAAC;MAEF,IAAII,QAAQ,CAACS,EAAE,EAAE;QACf,MAAMC,YAAY,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;QAC1C9G,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE4G,YAAY,CAAC;;QAExD;QACA5E,WAAW,CAAC8E,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP,CAACrE,KAAK,CAACuB,GAAG,IAAIvB,KAAK,CAACwB,EAAE,GAAG,CAAC,IAAI6C,IAAI,CAACrE,KAAK,CAACuB,GAAG,IAAIvB,KAAK,CAACwB,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE2C,YAAY,CAACG,IAAI,IAAIH,YAAY;QACrG,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL7G,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1D;QACAgC,WAAW,CAAC8E,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP,CAACrE,KAAK,CAACuB,GAAG,IAAIvB,KAAK,CAACwB,EAAE,GAAG,CAAC,IAAI6C,IAAI,CAACrE,KAAK,CAACuB,GAAG,IAAIvB,KAAK,CAACwB,EAAE,CAAC,IAAI,EAAE,CAAC,EAAEiB,OAAO;QAC3E,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOvE,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEW,KAAK,CAAC;MAC5D;MACAqB,WAAW,CAAC8E,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACrE,KAAK,CAACuB,GAAG,IAAIvB,KAAK,CAACwB,EAAE,GAAG,CAAC,IAAI6C,IAAI,CAACrE,KAAK,CAACuB,GAAG,IAAIvB,KAAK,CAACwB,EAAE,CAAC,IAAI,EAAE,CAAC,EAAEiB,OAAO;MAC3E,CAAC,CAAC,CAAC;IACL;IAEAhD,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAM8E,iBAAiB,GAAIC,SAAS,IAAK;IACvC,IAAI,EAACpH,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEmE,GAAG,KAAIvC,iBAAiB,KAAK,IAAI,EAAE;IAE9C,MAAMgB,KAAK,GAAGsB,uBAAuB,CAACtC,iBAAiB,CAAC;IACxDO,WAAW,CAAC8E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACrE,KAAK,CAACwB,EAAE,GAAG,CAAC6C,IAAI,CAACrE,KAAK,CAACwB,EAAE,CAAC,IAAI,EAAE,EAAEiD,GAAG,CAAChC,OAAO,IAAI;QAChD,IAAIA,OAAO,CAACjB,EAAE,KAAKgD,SAAS,EAAE;UAAA,IAAAE,gBAAA;UAC5B,MAAMC,OAAO,IAAAD,gBAAA,GAAGjC,OAAO,CAACW,OAAO,cAAAsB,gBAAA,uBAAfA,gBAAA,CAAiB7D,QAAQ,CAACzD,IAAI,CAACmE,GAAG,CAAC;UACnD,OAAO;YACL,GAAGkB,OAAO;YACVU,KAAK,EAAEwB,OAAO,GAAGlC,OAAO,CAACU,KAAK,GAAG,CAAC,GAAGV,OAAO,CAACU,KAAK,GAAG,CAAC;YACtDC,OAAO,EAAEuB,OAAO,GACZlC,OAAO,CAACW,OAAO,CAACwB,MAAM,CAACpD,EAAE,IAAIA,EAAE,KAAKpE,IAAI,CAACmE,GAAG,CAAC,GAC7C,CAAC,IAAIkB,OAAO,CAACW,OAAO,IAAI,EAAE,CAAC,EAAEhG,IAAI,CAACmE,GAAG;UAC3C,CAAC;QACH;QACA,OAAOkB,OAAO;MAChB,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMoC,mBAAmB,GAAIL,SAAS,IAAK;IACzC,IAAIxF,iBAAiB,KAAK,IAAI,EAAE;IAEhC,MAAMgB,KAAK,GAAGsB,uBAAuB,CAACtC,iBAAiB,CAAC;IACxDO,WAAW,CAAC8E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACrE,KAAK,CAACwB,EAAE,GAAG,CAAC6C,IAAI,CAACrE,KAAK,CAACwB,EAAE,CAAC,IAAI,EAAE,EAAEoD,MAAM,CAACnC,OAAO,IAAIA,OAAO,CAACjB,EAAE,KAAKgD,SAAS;IAC/E,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMM,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAM/E,KAAK,GAAGsB,uBAAuB,CAACyD,KAAK,CAAC;IAC5C9F,oBAAoB,CAAC8F,KAAK,CAAC;IAC3B1F,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIW,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEc,QAAQ,KAAKd,KAAK,CAACc,QAAQ,CAACD,QAAQ,CAAC,eAAe,CAAC,IAAIb,KAAK,CAACc,QAAQ,CAACD,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF;QACA;QACA;QACAb,KAAK,CAACgF,cAAc,GAAGhF,KAAK,CAACc,QAAQ;MACvC,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdZ,OAAO,CAAC2H,IAAI,CAAC,8CAA8C,CAAC;QAC5DjF,KAAK,CAACgF,cAAc,GAAGhF,KAAK,CAACc,QAAQ;MACvC;IACF;EACF,CAAC;;EAED;EACA,MAAMoE,WAAW,GAAG7I,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFiC,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC;MAEdjB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAIkG,QAAQ,GAAG,IAAI;MACnB,IAAItF,MAAM,GAAG,EAAE;;MAEf;MACA,IAAI;QAAA,IAAAgH,SAAA,EAAAC,UAAA;QACF9H,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjDkG,QAAQ,GAAG,MAAM5G,YAAY,CAAC,CAAC;QAC/BS,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEkG,QAAQ,CAAC;QAE/C,IAAI,CAAA0B,SAAA,GAAA1B,QAAQ,cAAA0B,SAAA,eAARA,SAAA,CAAUE,OAAO,KAAAD,UAAA,GAAI3B,QAAQ,cAAA2B,UAAA,eAARA,UAAA,CAAUd,IAAI,EAAE;UACvCnG,MAAM,GAAGsF,QAAQ,CAACa,IAAI;UACtBhH,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEY,MAAM,CAACmH,MAAM,CAAC;;UAE7E;UACA,IAAInH,MAAM,CAACmH,MAAM,GAAG,CAAC,EAAE;YACrBhI,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC;YACnDb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,MAAM,CAACC,IAAI,CAACU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3Db,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;YACzCD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC+B,SAAS,CAAC;YAClD5C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACgC,YAAY,CAAC;YACxD7C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACiC,KAAK,CAAC;YAC1C9C,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACkC,MAAM,CAAC;YAC5C/C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACmC,SAAS,CAAC;YAClDhD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACoC,KAAK,CAAC;YAC1CjD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACqC,UAAU,CAAC;YACpDlD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACsC,YAAY,CAAC;YACxDnD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACuC,cAAc,CAAC;UAC9D;QACF;MACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;QACdZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEW,KAAK,CAACqH,OAAO,CAAC;MACtD;;MAEA;MACA,IAAIpH,MAAM,CAACmH,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAE,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACFrI,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,MAAMqI,OAAO,GAAG;YACdC,KAAK,EAAEnH,aAAa;YACpBoH,IAAI,EAAE;UACR,CAAC;UAEDrC,QAAQ,GAAG,MAAM7G,gBAAgB,CAACgJ,OAAO,CAAC;UAC1CtI,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkG,QAAQ,CAAC;UAEnD,IAAI,CAAA+B,UAAA,GAAA/B,QAAQ,cAAA+B,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUlB,IAAI,cAAAmB,eAAA,eAAdA,eAAA,CAAgBJ,OAAO,KAAAK,UAAA,GAAIjC,QAAQ,cAAAiC,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUpB,IAAI,cAAAqB,eAAA,eAAdA,eAAA,CAAgBrB,IAAI,EAAE;YACnDnG,MAAM,GAAGsF,QAAQ,CAACa,IAAI,CAACA,IAAI;YAC3BhH,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEY,MAAM,CAACmH,MAAM,CAAC;UACnF;QACF,CAAC,CAAC,OAAOpH,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,KAAK,CAACqH,OAAO,CAAC;QAC1D;MACF;;MAEA;MACA,IAAIpH,MAAM,CAACmH,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAS,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACF5I,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5DkG,QAAQ,GAAG,MAAM7G,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACrCU,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEkG,QAAQ,CAAC;UAEhE,IAAI,CAAAsC,UAAA,GAAAtC,QAAQ,cAAAsC,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUzB,IAAI,cAAA0B,eAAA,eAAdA,eAAA,CAAgBX,OAAO,KAAAY,UAAA,GAAIxC,QAAQ,cAAAwC,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAU3B,IAAI,cAAA4B,eAAA,eAAdA,eAAA,CAAgB5B,IAAI,EAAE;YACnD;YACA,MAAM6B,OAAO,GAAG1C,QAAQ,CAACa,IAAI,CAACA,IAAI;YAClCnG,MAAM,GAAGgI,OAAO,CAACvB,MAAM,CAACwB,IAAI;cAAA,IAAAC,WAAA;cAAA,OAC1BD,IAAI,CAACN,IAAI,KAAK,OAAO,IACrBM,IAAI,CAACtF,QAAQ,IACbsF,IAAI,CAACxF,OAAO,MAAAyF,WAAA,GACZD,IAAI,CAACnF,KAAK,cAAAoF,WAAA,uBAAVA,WAAA,CAAYC,WAAW,CAAC,CAAC,CAACzF,QAAQ,CAAC,OAAO,CAAC;YAAA,CAC7C,CAAC;YACDvD,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,MAAM,CAACmH,MAAM,CAAC;UAC1E;QACF,CAAC,CAAC,OAAOpH,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEW,KAAK,CAACqH,OAAO,CAAC;QACvE;MACF;;MAEA;MACA,IAAIpH,MAAM,CAACmH,MAAM,GAAG,CAAC,EAAE;QACrB,MAAMiB,QAAQ,GAAGpI,MAAM,CAACyG,MAAM,CAAC5E,KAAK,IAAI;UACtC,MAAMwG,YAAY,GAAG9H,aAAa,KAAK,KAAK,IACxBsB,KAAK,CAAC6F,KAAK,KAAKnH,aAAa,IAC7B,CAACsB,KAAK,CAAC6F,KAAK,CAAC,CAAC;;UAElC,MAAMY,YAAY,GAAG7H,aAAa,KAAK,KAAK,IACxBoB,KAAK,CAAC0G,SAAS,KAAK9H,aAAa,IACjCoB,KAAK,CAAC2G,KAAK,KAAK/H,aAAa,IAC7B,CAACoB,KAAK,CAAC0G,SAAS,CAAC,CAAC;;UAEtC,MAAME,cAAc,GAAG9H,eAAe,KAAK,KAAK,IAC1BkB,KAAK,CAACmB,OAAO,KAAKrC,eAAe,IACjC,CAACkB,KAAK,CAACmB,OAAO,CAAC,CAAC;;UAEtC,OAAOqF,YAAY,IAAIC,YAAY,IAAIG,cAAc;QACvD,CAAC,CAAC;QAEFxI,SAAS,CAACmI,QAAQ,CAAC;QACnBjJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEgJ,QAAQ,CAACjB,MAAM,EAAE,QAAQ,CAAC;QAErE,IAAIiB,QAAQ,CAACjB,MAAM,KAAK,CAAC,EAAE;UACzB/G,QAAQ,CAAC,wEAAwE,CAAC;QACpF;MACF,CAAC,MAAM;QACL;QACAjB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpED,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrCD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAElCgB,QAAQ,CAAC,yFAAyF,CAAC;QACnGH,SAAS,CAAC,EAAE,CAAC;MACf;IAEF,CAAC,CAAC,OAAOyI,GAAG,EAAE;MACZvJ,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAE2I,GAAG,CAAC;MACvDtI,QAAQ,CAAC,0DAA0D,CAAC;MACpEH,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACI,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEnD;EACA,MAAMwC,uBAAuB,GAAGlF,OAAO,CAAC,MAAM;IAC5C,IAAImK,QAAQ,GAAGpI,MAAM,CAACyG,MAAM,CAAC5E,KAAK,IAAI;MAAA,IAAA8G,YAAA,EAAAC,cAAA,EAAAC,YAAA;MACpC,MAAMC,aAAa,GAAG,CAACzI,UAAU,MAAAsI,YAAA,GAC/B9G,KAAK,CAACiB,KAAK,cAAA6F,YAAA,uBAAXA,YAAA,CAAaR,WAAW,CAAC,CAAC,CAACzF,QAAQ,CAACrC,UAAU,CAAC8H,WAAW,CAAC,CAAC,CAAC,OAAAS,cAAA,GAC7D/G,KAAK,CAACmB,OAAO,cAAA4F,cAAA,uBAAbA,cAAA,CAAeT,WAAW,CAAC,CAAC,CAACzF,QAAQ,CAACrC,UAAU,CAAC8H,WAAW,CAAC,CAAC,CAAC,OAAAU,YAAA,GAC/DhH,KAAK,CAACkH,KAAK,cAAAF,YAAA,uBAAXA,YAAA,CAAaV,WAAW,CAAC,CAAC,CAACzF,QAAQ,CAACrC,UAAU,CAAC8H,WAAW,CAAC,CAAC,CAAC;MAE/D,MAAME,YAAY,GAAG9H,aAAa,KAAK,KAAK,IAAIsB,KAAK,CAAC6F,KAAK,KAAKnH,aAAa;MAC7E,MAAM+H,YAAY,GAAG7H,aAAa,KAAK,KAAK,IAAIoB,KAAK,CAAC0G,SAAS,KAAK9H,aAAa,IAAIoB,KAAK,CAAC2G,KAAK,KAAK/H,aAAa;MAClH,MAAMgI,cAAc,GAAG9H,eAAe,KAAK,KAAK,IAAIkB,KAAK,CAACmB,OAAO,KAAKrC,eAAe;MAErF,OAAOmI,aAAa,IAAIT,YAAY,IAAIC,YAAY,IAAIG,cAAc;IACxE,CAAC,CAAC;IAEF,OAAOL,QAAQ,CAACY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIzF,IAAI,CAACyF,CAAC,CAACnE,SAAS,CAAC,GAAG,IAAItB,IAAI,CAACwF,CAAC,CAAClE,SAAS,CAAC,CAAC;EAC/E,CAAC,EAAE,CAAC/E,MAAM,EAAEK,UAAU,EAAEE,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEvE;EACA3C,SAAS,CAAC,MAAM;IACd+I,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA/I,SAAS,CAAC,MAAM;IACd+I,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACxG,aAAa,EAAEE,aAAa,EAAEE,eAAe,EAAEoG,WAAW,CAAC,CAAC;EAEhE,oBACEnI,OAAA;IAAK2J,SAAS,EAAC,yBAAyB;IAAAY,QAAA,gBACtCvK,OAAA;MAAK2J,SAAS,EAAC,sBAAsB;MAAAY,QAAA,gBACnCvK,OAAA;QAAAuK,QAAA,EAAKxH,WAAW,GAAG,iBAAiB,GAAG;MAAe;QAAAyH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5D3K,OAAA;QAAAuK,QAAA,EAAIxH,WAAW,GAAG,iCAAiC,GAAG;MAAoC;QAAAyH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAGN3K,OAAA;MAAK2J,SAAS,EAAC,gBAAgB;MAAAY,QAAA,gBAC7BvK,OAAA;QAAK2J,SAAS,EAAC,gBAAgB;QAAAY,QAAA,eAC7BvK,OAAA;UACE+I,IAAI,EAAC,MAAM;UACX6B,WAAW,EAAE7H,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;UAClE8H,KAAK,EAAEpJ,UAAW;UAClBqJ,QAAQ,EAAG5J,CAAC,IAAKQ,aAAa,CAACR,CAAC,CAAC6J,MAAM,CAACF,KAAK,CAAE;UAC/ClB,SAAS,EAAC;QAAc;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN3K,OAAA;QAAK2J,SAAS,EAAC,gBAAgB;QAAAY,QAAA,gBAC7BvK,OAAA;UACE6K,KAAK,EAAElJ,aAAc;UACrBmJ,QAAQ,EAAG5J,CAAC,IAAKU,gBAAgB,CAACV,CAAC,CAAC6J,MAAM,CAACF,KAAK,CAAE;UAClDlB,SAAS,EAAC,eAAe;UAAAY,QAAA,gBAEzBvK,OAAA;YAAQ6K,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAExH,WAAW,GAAG,QAAQ,GAAG;UAAS;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrE3K,OAAA;YAAQ6K,KAAK,EAAC,WAAW;YAAAN,QAAA,EAAExH,WAAW,GAAG,WAAW,GAAG;UAAW;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5E3K,OAAA;YAAQ6K,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAExH,WAAW,GAAG,KAAK,GAAG;UAAU;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAET3K,OAAA;UACE6K,KAAK,EAAEhJ,aAAc;UACrBiJ,QAAQ,EAAG5J,CAAC,IAAKY,gBAAgB,CAACZ,CAAC,CAAC6J,MAAM,CAACF,KAAK,CAAE;UAClDlB,SAAS,EAAC,eAAe;UAAAY,QAAA,eAEzBvK,OAAA;YAAQ6K,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAExH,WAAW,GAAG,eAAe,GAAG;UAAa;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtE,CAAC,eAET3K,OAAA;UACE6K,KAAK,EAAE9I,eAAgB;UACvB+I,QAAQ,EAAG5J,CAAC,IAAKc,kBAAkB,CAACd,CAAC,CAAC6J,MAAM,CAACF,KAAK,CAAE;UACpDlB,SAAS,EAAC,eAAe;UAAAY,QAAA,eAEzBvK,OAAA;YAAQ6K,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAExH,WAAW,GAAG,aAAa,GAAG;UAAc;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3K,OAAA;MAAK2J,SAAS,EAAC,eAAe;MAAAY,QAAA,EAC3BjJ,OAAO,gBACNtB,OAAA;QAAK2J,SAAS,EAAC,eAAe;QAAAY,QAAA,gBAC5BvK,OAAA;UAAK2J,SAAS,EAAC;QAAiB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC3K,OAAA;UAAAuK,QAAA,EAAIxH,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJxJ,KAAK,gBACPnB,OAAA;QAAK2J,SAAS,EAAC,aAAa;QAAAY,QAAA,gBAC1BvK,OAAA,CAACL,eAAe;UAACgK,SAAS,EAAC;QAAY;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1C3K,OAAA;UAAAuK,QAAA,EAAKxH,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7E3K,OAAA;UAAAuK,QAAA,EAAIpJ;QAAK;UAAAqJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd3K,OAAA;UAAQgL,OAAO,EAAE7C,WAAY;UAACwB,SAAS,EAAC,WAAW;UAAAY,QAAA,EAChDxH,WAAW,GAAG,aAAa,GAAG;QAAW;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJpG,uBAAuB,CAACgE,MAAM,GAAG,CAAC,gBACpCvI,OAAA;QAAK2J,SAAS,EAAC,aAAa;QAAAY,QAAA,EACzBhG,uBAAuB,CAACmD,GAAG,CAAC,CAACzE,KAAK,EAAE+E,KAAK;UAAA,IAAAiD,IAAA,EAAAC,WAAA;UAAA,oBACxClL,OAAA;YAAiB2J,SAAS,EAAC,YAAY;YAAAY,QAAA,EACpCtI,iBAAiB,KAAK+F,KAAK;YAAA;YAC1B;YACAhI,OAAA;cAAK2J,SAAS,EAAC,qBAAqB;cAAAY,QAAA,eAClCvK,OAAA;gBAAK2J,SAAS,EAAC,sBAAsB;gBAAAY,QAAA,gBACnCvK,OAAA;kBAAK2J,SAAS,EAAC,sBAAsB;kBAAAY,QAAA,EAClCtH,KAAK,CAACc,QAAQ,gBACb/D,OAAA;oBACEmL,GAAG,EAAGA,GAAG,IAAK/I,WAAW,CAAC+I,GAAG,CAAE;oBAC/BC,QAAQ;oBACRC,QAAQ;oBACRC,WAAW;oBACXC,OAAO,EAAC,UAAU;oBAClBC,KAAK,EAAC,MAAM;oBACZC,MAAM,EAAC,MAAM;oBACbnI,MAAM,EAAEN,eAAe,CAACC,KAAK,CAAE;oBAC/ByI,KAAK,EAAE;sBACLF,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdE,eAAe,EAAE,MAAM;sBACvBC,SAAS,EAAE;oBACb,CAAE;oBACFC,OAAO,EAAG3K,CAAC,IAAKoB,aAAa,CAAE,yBAAwBW,KAAK,CAACiB,KAAM,EAAC,CAAE;oBACtE4H,SAAS,EAAEA,CAAA,KAAMxJ,aAAa,CAAC,IAAI,CAAE;oBACrCyJ,WAAW,EAAC,WAAW;oBAAAxB,QAAA,gBAEvBvK,OAAA;sBAAQgM,GAAG,EAAE/I,KAAK,CAACgF,cAAc,IAAIhF,KAAK,CAACc,QAAS;sBAACgF,IAAI,EAAC;oBAAW;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACvE1H,KAAK,CAACgJ,SAAS,IAAIhJ,KAAK,CAACgJ,SAAS,CAAC1D,MAAM,GAAG,CAAC,IAAItF,KAAK,CAACgJ,SAAS,CAACvE,GAAG,CAAC,CAACwE,QAAQ,EAAElE,KAAK,kBACpFhI,OAAA;sBAEEmM,IAAI,EAAC,WAAW;sBAChBH,GAAG,EAAEE,QAAQ,CAACE,GAAI;sBAClBC,OAAO,EAAEH,QAAQ,CAACI,QAAS;sBAC3BC,KAAK,EAAEL,QAAQ,CAACM,YAAa;sBAC7BC,OAAO,EAAEP,QAAQ,CAACQ,SAAS,IAAI1E,KAAK,KAAK;oBAAE,GALrC,GAAEkE,QAAQ,CAACI,QAAS,IAAGtE,KAAM,EAAC;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,GACN1H,KAAK,CAACY,OAAO,gBACf7D,OAAA;oBACEgM,GAAG,EAAG,iCAAgC/I,KAAK,CAACY,OAAQ,mBAAmB;oBACvEK,KAAK,EAAEjB,KAAK,CAACiB,KAAM;oBACnByI,WAAW,EAAC,GAAG;oBACfC,eAAe;oBACflB,KAAK,EAAE;sBAAEF,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE,MAAM;sBAAEoB,MAAM,EAAE;oBAAO;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,gBAEV3K,OAAA;oBAAK2J,SAAS,EAAC,aAAa;oBAAAY,QAAA,gBAC1BvK,OAAA;sBAAK2J,SAAS,EAAC,YAAY;sBAAAY,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpC3K,OAAA;sBAAAuK,QAAA,EAAI;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1B3K,OAAA;sBAAAuK,QAAA,EAAIlI,UAAU,IAAI;oBAA4C;sBAAAmI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEN3K,OAAA;kBAAK2J,SAAS,EAAC,oBAAoB;kBAAAY,QAAA,gBACjCvK,OAAA;oBAAI2J,SAAS,EAAC,qBAAqB;oBAAAY,QAAA,EAAEtH,KAAK,CAACiB;kBAAK;oBAAAsG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtD3K,OAAA;oBAAK2J,SAAS,EAAC,oBAAoB;oBAAAY,QAAA,gBACjCvK,OAAA;sBAAAuK,QAAA,EAAOpG,cAAc,CAAClB,KAAK,CAACmB,OAAO;oBAAC;sBAAAoG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5C3K,OAAA;sBAAAuK,QAAA,EAAM;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACd3K,OAAA;sBAAAuK,QAAA,GAAM,QAAM,EAACtH,KAAK,CAAC0G,SAAS,IAAI1G,KAAK,CAAC2G,KAAK;oBAAA;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAClD1H,KAAK,CAAC6F,KAAK,iBACV9I,OAAA,CAAAE,SAAA;sBAAAqK,QAAA,gBACEvK,OAAA;wBAAAuK,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACd3K,OAAA;wBAAAuK,QAAA,EAAOtH,KAAK,CAAC6F;sBAAK;wBAAA0B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,eAC1B,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN3K,OAAA;oBAAK2J,SAAS,EAAC,uBAAuB;oBAAAY,QAAA,gBACpCvK,OAAA;sBACE2J,SAAS,EAAG,sBAAqBhH,gBAAgB,GAAG,QAAQ,GAAG,EAAG,EAAE;sBACpEqI,OAAO,EAAEA,CAAA,KAAMpI,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;sBAAA4H,QAAA,gBAEtDvK,OAAA;wBAAAuK,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACf3K,OAAA;wBAAAuK,QAAA,EAAM;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACT3K,OAAA;sBAAQ2J,SAAS,EAAC,oBAAoB;sBAAAY,QAAA,gBACpCvK,OAAA;wBAAAuK,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACf3K,OAAA;wBAAAuK,QAAA,EAAM;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACT3K,OAAA;sBACE2J,SAAS,EAAC,oBAAoB;sBAC9BqB,OAAO,EAAEA,CAAA,KAAM9I,oBAAoB,CAAC,IAAI,CAAE;sBAAAqI,QAAA,gBAE1CvK,OAAA;wBAAAuK,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACd3K,OAAA;wBAAAuK,QAAA,EAAM;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLhI,gBAAgB,iBACf3C,OAAA;kBAAK2J,SAAS,EAAC,0BAA0B;kBAAAY,QAAA,gBACvCvK,OAAA;oBAAK2J,SAAS,EAAC,yBAAyB;oBAAAY,QAAA,eACtCvK,OAAA;sBAAAuK,QAAA,GAAOjG,uBAAuB,CAAC,CAAC,CAACiE,MAAM,EAAC,WAAS;oBAAA;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC,eAGN3K,OAAA;oBAAK2J,SAAS,EAAC,uBAAuB;oBAAAY,QAAA,gBACpCvK,OAAA;sBAAK2J,SAAS,EAAC,wBAAwB;sBAAAY,QAAA,GAAAU,IAAA,GACnC,CAAA5K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+E,IAAI,MAAI/E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgF,SAAS,MAAIhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiF,QAAQ,KAAI,SAAS,cAAA2F,IAAA,wBAAAC,WAAA,GAA7DD,IAAA,CAAgE6B,MAAM,CAAC,CAAC,CAAC,cAAA5B,WAAA,uBAAzEA,WAAA,CAA2E6B,WAAW,CAAC;oBAAC;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF,CAAC,eACN3K,OAAA;sBAAK0L,KAAK,EAAE;wBAAEsB,IAAI,EAAE;sBAAE,CAAE;sBAAAzC,QAAA,gBACtBvK,OAAA;wBACE2J,SAAS,EAAC,6BAA6B;wBACvCkB,KAAK,EAAEpI,UAAW;wBAClBqI,QAAQ,EAAG5J,CAAC,IAAKwB,aAAa,CAACxB,CAAC,CAAC6J,MAAM,CAACF,KAAK,CAAE;wBAC/CD,WAAW,EAAC,kBAAkB;wBAC9BqC,IAAI,EAAC,GAAG;wBACRvB,KAAK,EAAE;0BACLwB,SAAS,EAAE,MAAM;0BACjBC,MAAM,EAAE,MAAM;0BACdC,QAAQ,EAAE;wBACZ,CAAE;wBACFC,OAAO,EAAGnM,CAAC,IAAK;0BACdA,CAAC,CAAC6J,MAAM,CAACW,KAAK,CAACD,MAAM,GAAG,MAAM;0BAC9BvK,CAAC,CAAC6J,MAAM,CAACW,KAAK,CAACD,MAAM,GAAGvK,CAAC,CAAC6J,MAAM,CAACuC,YAAY,GAAG,IAAI;wBACtD;sBAAE;wBAAA9C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EACDlI,UAAU,CAACmB,IAAI,CAAC,CAAC,iBAChB5D,OAAA;wBAAK2J,SAAS,EAAC,yBAAyB;wBAAAY,QAAA,gBACtCvK,OAAA;0BACE2J,SAAS,EAAC,4BAA4B;0BACtCqB,OAAO,EAAEA,CAAA,KAAMtI,aAAa,CAAC,EAAE,CAAE;0BAAA6H,QAAA,EAClC;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACT3K,OAAA;0BACE2J,SAAS,EAAC,4BAA4B;0BACtCqB,OAAO,EAAE9F,gBAAiB;0BAC1BqI,QAAQ,EAAE,CAAC9K,UAAU,CAACmB,IAAI,CAAC,CAAE;0BAAA2G,QAAA,EAC9B;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN3K,OAAA;oBAAK2J,SAAS,EAAC,uBAAuB;oBAAAY,QAAA,EACnCjG,uBAAuB,CAAC,CAAC,CAACiE,MAAM,KAAK,CAAC,gBACrCvI,OAAA;sBAAK0L,KAAK,EAAE;wBAAE8B,SAAS,EAAE,QAAQ;wBAAEC,OAAO,EAAE,QAAQ;wBAAEC,KAAK,EAAE;sBAAU,CAAE;sBAAAnD,QAAA,gBACvEvK,OAAA;wBAAK0L,KAAK,EAAE;0BAAEiC,QAAQ,EAAE,MAAM;0BAAEC,YAAY,EAAE;wBAAO,CAAE;wBAAArD,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChE3K,OAAA;wBAAAuK,QAAA,EAAG;sBAAqD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,GAENrG,uBAAuB,CAAC,CAAC,CAACoD,GAAG,CAAEhC,OAAO;sBAAA,IAAAmI,eAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,iBAAA;sBAAA,oBACpChO,OAAA;wBAAqC2J,SAAS,EAAC,iBAAiB;wBAAAY,QAAA,gBAC9DvK,OAAA;0BAAK2J,SAAS,EAAC,wBAAwB;0BAAAY,QAAA,EACpC7E,OAAO,CAACc,MAAM,MAAAqH,eAAA,GAAInI,OAAO,CAACG,MAAM,cAAAgI,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBf,MAAM,CAAC,CAAC,CAAC,cAAAgB,qBAAA,uBAAzBA,qBAAA,CAA2Bf,WAAW,CAAC,CAAC,KAAI;wBAAG;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D,CAAC,eACN3K,OAAA;0BAAK2J,SAAS,EAAC,yBAAyB;0BAAAY,QAAA,gBACtCvK,OAAA;4BAAK2J,SAAS,EAAC,wBAAwB;4BAAAY,QAAA,gBACrCvK,OAAA;8BAAM2J,SAAS,EAAC,wBAAwB;8BAAAY,QAAA,EAAE7E,OAAO,CAACG;4BAAM;8BAAA2E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,EAC/D,CAACjF,OAAO,CAACK,QAAQ,KAAK,OAAO,IAAIL,OAAO,CAACO,OAAO,kBAC/CjG,OAAA,CAACJ,UAAU;8BAAC8L,KAAK,EAAE;gCAAEgC,KAAK,EAAE,SAAS;gCAAEC,QAAQ,EAAE,MAAM;gCAAEM,UAAU,EAAE;8BAAM,CAAE;8BAAC/J,KAAK,EAAC;4BAAgB;8BAAAsG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CACvG,eACD3K,OAAA;8BAAM2J,SAAS,EAAC,sBAAsB;8BAAAY,QAAA,EACnC7F,aAAa,CAACgB,OAAO,CAACS,SAAS,IAAIT,OAAO,CAACf,SAAS;4BAAC;8BAAA6F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAClD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eACN3K,OAAA;4BAAK2J,SAAS,EAAC,sBAAsB;4BAAAY,QAAA,EAClC7E,OAAO,CAACE;0BAAI;4BAAA4E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN3K,OAAA;4BAAK2J,SAAS,EAAC,yBAAyB;4BAAAY,QAAA,gBACtCvK,OAAA;8BACEgL,OAAO,EAAEA,CAAA,KAAMxD,iBAAiB,CAAC9B,OAAO,CAAClB,GAAG,IAAIkB,OAAO,CAACjB,EAAE,CAAE;8BAC5DkF,SAAS,EAAG,0BAAyB,CAAAoE,iBAAA,GAAArI,OAAO,CAACW,OAAO,cAAA0H,iBAAA,eAAfA,iBAAA,CAAiBjK,QAAQ,CAACzD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,GAAG,CAAC,GAAG,OAAO,GAAG,EAAG,EAAE;8BAAA+F,QAAA,gBAE3FvK,OAAA;gCAAAuK,QAAA,EAAO,CAAAyD,iBAAA,GAAAtI,OAAO,CAACW,OAAO,cAAA2H,iBAAA,eAAfA,iBAAA,CAAiBlK,QAAQ,CAACzD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,GAAG,CAAC,GAAG,IAAI,GAAG;8BAAI;gCAAAgG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,EAChEjF,OAAO,CAACU,KAAK,GAAG,CAAC,iBAAIpG,OAAA;gCAAAuK,QAAA,EAAO7E,OAAO,CAACU;8BAAK;gCAAAoE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5C,CAAC,eACT3K,OAAA;8BAAQ2J,SAAS,EAAC,wBAAwB;8BAAAY,QAAA,eACxCvK,OAAA;gCAAAuK,QAAA,EAAM;8BAAE;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC,eACT3K,OAAA;8BAAQ2J,SAAS,EAAC,wBAAwB;8BAAAY,QAAA,EAAC;4BAE3C;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,EACRjF,OAAO,CAACrF,IAAI,MAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,GAAG,kBACzBxE,OAAA,CAAAE,SAAA;8BAAAqK,QAAA,gBACEvK,OAAA;gCAAQ2J,SAAS,EAAC,wBAAwB;gCAAAY,QAAA,EAAC;8BAE3C;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eACT3K,OAAA;gCACE2J,SAAS,EAAC,wBAAwB;gCAClCqB,OAAO,EAAEA,CAAA,KAAM;kCACb,IAAIkD,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;oCACnErG,mBAAmB,CAACpC,OAAO,CAAClB,GAAG,IAAIkB,OAAO,CAACjB,EAAE,CAAC;kCAChD;gCACF,CAAE;gCAAA8F,QAAA,EACH;8BAED;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC;4BAAA,eACT,CACH;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA,GAjDEjF,OAAO,CAAClB,GAAG,IAAIkB,OAAO,CAACjB,EAAE;wBAAA+F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAkD9B,CAAC;oBAAA,CACP;kBACF;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACA3K,OAAA;cAAK2J,SAAS,EAAC,YAAY;cAACqB,OAAO,EAAEA,CAAA,KAAMjD,eAAe,CAACC,KAAK,CAAE;cAAAuC,QAAA,gBAChEvK,OAAA;gBAAK2J,SAAS,EAAC,sBAAsB;gBAAAY,QAAA,gBACnCvK,OAAA;kBACEgM,GAAG,EAAEhJ,eAAe,CAACC,KAAK,CAAE;kBAC5BmL,GAAG,EAAEnL,KAAK,CAACiB,KAAM;kBACjByF,SAAS,EAAC,iBAAiB;kBAC3BrI,OAAO,EAAC,MAAM;kBACduK,OAAO,EAAG3K,CAAC,IAAK;oBACd,IAAI+B,KAAK,CAACY,OAAO,IAAI,CAACZ,KAAK,CAACY,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;sBAC7D,IAAIE,OAAO,GAAGf,KAAK,CAACY,OAAO;sBAC3B,MAAMI,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;sBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;sBACpC,MAAMqK,SAAS,GAAG,CACf,8BAA6BrK,OAAQ,oBAAmB,EACxD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,4cAA4c,CAC7c;sBACD,MAAMsK,UAAU,GAAGpN,CAAC,CAAC6J,MAAM,CAACiB,GAAG;sBAC/B,MAAMuC,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACpC,GAAG,IAAIkC,UAAU,CAACxK,QAAQ,CAACsI,GAAG,CAACqC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACxG,IAAIF,YAAY,GAAGF,SAAS,CAAC9F,MAAM,GAAG,CAAC,EAAE;wBACvCrH,CAAC,CAAC6J,MAAM,CAACiB,GAAG,GAAGqC,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;sBAC5C;oBACF,CAAC,MAAM;sBACLrN,CAAC,CAAC6J,MAAM,CAACiB,GAAG,GAAG,4cAA4c;oBAC7d;kBACF;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF3K,OAAA;kBAAK2J,SAAS,EAAC,cAAc;kBAAAY,QAAA,eAC3BvK,OAAA,CAACR,YAAY;oBAACmK,SAAS,EAAC;kBAAW;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACN3K,OAAA;kBAAK2J,SAAS,EAAC,gBAAgB;kBAAAY,QAAA,EAC5BtH,KAAK,CAAC0L,QAAQ,IAAI;gBAAO;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EACL1H,KAAK,CAACgJ,SAAS,IAAIhJ,KAAK,CAACgJ,SAAS,CAAC1D,MAAM,GAAG,CAAC,iBAC5CvI,OAAA;kBAAK2J,SAAS,EAAC,gBAAgB;kBAAAY,QAAA,gBAC7BvK,OAAA,CAACN,YAAY;oBAAA8K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,MAElB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3K,OAAA;gBAAK2J,SAAS,EAAC,oBAAoB;gBAAAY,QAAA,gBACjCvK,OAAA;kBAAI2J,SAAS,EAAC,aAAa;kBAAAY,QAAA,EAAEtH,KAAK,CAACiB;gBAAK;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9C3K,OAAA;kBAAK2J,SAAS,EAAC,YAAY;kBAAAY,QAAA,gBACzBvK,OAAA;oBAAM2J,SAAS,EAAC,eAAe;oBAAAY,QAAA,EAAEpG,cAAc,CAAClB,KAAK,CAACmB,OAAO;kBAAC;oBAAAoG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtE3K,OAAA;oBAAM2J,SAAS,EAAC,aAAa;oBAAAY,QAAA,EAC1B5I,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEoB,WAAW,GAAI,aAAYE,KAAK,CAAC0G,SAAS,IAAI1G,KAAK,CAAC2G,KAAM,EAAC,GAAI,SAAQ3G,KAAK,CAAC0G,SAAS,IAAI1G,KAAK,CAAC2G,KAAM,EAAC,GACvG,QAAO3G,KAAK,CAAC0G,SAAS,IAAI1G,KAAK,CAAC2G,KAAM;kBAAC;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN3K,OAAA;kBAAK2J,SAAS,EAAC,YAAY;kBAAAY,QAAA,GACxBtH,KAAK,CAACkH,KAAK,iBAAInK,OAAA;oBAAM2J,SAAS,EAAC,WAAW;oBAAAY,QAAA,EAAEtH,KAAK,CAACkH;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/D1H,KAAK,CAAC2L,eAAe,IAAI3L,KAAK,CAAC2L,eAAe,MAAM3L,KAAK,CAAC0G,SAAS,IAAI1G,KAAK,CAAC2G,KAAK,CAAC,iBAClF5J,OAAA;oBAAM2J,SAAS,EAAC,YAAY;oBAAAY,QAAA,GACzBxH,WAAW,GAAG,qBAAqB,GAAG,cAAc,EACpDpB,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEoB,WAAW,GAAI,aAAYE,KAAK,CAAC2L,eAAgB,EAAC,GAAI,SAAQ3L,KAAK,CAAC2L,eAAgB,EAAC,GACrF,QAAO3L,KAAK,CAAC2L,eAAgB,EAAC;kBAAA;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN,GAnRO3C,KAAK;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoRV,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEN3K,OAAA;QAAK2J,SAAS,EAAC,aAAa;QAAAY,QAAA,gBAC1BvK,OAAA,CAACP,eAAe;UAACkK,SAAS,EAAC;QAAY;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1C3K,OAAA;UAAAuK,QAAA,EAAKxH,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1E3K,OAAA;UAAAuK,QAAA,EAAIxH,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJ3K,OAAA;UAAG2J,SAAS,EAAC,YAAY;UAAAY,QAAA,EAAExH,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvK,EAAA,CApyBID,YAAY;EAAA,QAEHZ,WAAW;AAAA;AAAAsP,EAAA,GAFpB1O,YAAY;AAsyBlB,eAAeA,YAAY;AAAC,IAAA0O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}