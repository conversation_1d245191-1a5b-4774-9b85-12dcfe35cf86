{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ContentRenderer.js\";\nimport React from 'react';\nimport { InlineMath, BlockMath } from 'react-katex';\nimport 'katex/dist/katex.min.css';\n\n// Ensure KaTeX CSS is loaded\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst katexCSS = `\n.katex {\n  font-size: 1.1em !important;\n  line-height: 1.2 !important;\n}\n.katex-display {\n  margin: 1em 0 !important;\n  text-align: center !important;\n}\n`;\n\n// Inject CSS if not already present\nif (typeof document !== 'undefined' && !document.getElementById('katex-custom-styles')) {\n  const style = document.createElement('style');\n  style.id = 'katex-custom-styles';\n  style.textContent = katexCSS;\n  document.head.appendChild(style);\n}\nconst ContentRenderer = ({\n  text\n}) => {\n  // Handle undefined, null, or empty text\n  if (!text || typeof text !== 'string') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 16\n    }, this);\n  }\n\n  // Enhanced formatting for AI responses\n  const formatAIResponse = content => {\n    // Split into lines for processing\n    const lines = content.split('\\n');\n    const formattedLines = [];\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i].trim();\n\n      // Skip empty lines but preserve spacing\n      if (!line) {\n        formattedLines.push( /*#__PURE__*/_jsxDEV(\"br\", {}, `br-${i}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 37\n        }, this));\n        continue;\n      }\n\n      // Handle numbered lists (1., 2., etc.)\n      if (/^\\d+\\.\\s/.test(line)) {\n        const content = line.replace(/^\\d+\\.\\s/, '');\n        formattedLines.push( /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '8px 0',\n            paddingLeft: '16px',\n            position: 'relative',\n            lineHeight: '1.6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              left: '0',\n              fontWeight: 'bold',\n              color: '#3b82f6'\n            },\n            children: [line.match(/^\\d+/)[0], \".\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 25\n          }, this)]\n        }, `numbered-${i}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this));\n        continue;\n      }\n\n      // Handle bullet points (-, *, •)\n      if (/^[-*•]\\s/.test(line)) {\n        const content = line.replace(/^[-*•]\\s/, '');\n        formattedLines.push( /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '6px 0',\n            paddingLeft: '16px',\n            position: 'relative',\n            lineHeight: '1.6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              left: '0',\n              color: '#3b82f6',\n              fontWeight: 'bold'\n            },\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 25\n          }, this)]\n        }, `bullet-${i}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 21\n        }, this));\n        continue;\n      }\n\n      // Handle headers (##, ###)\n      if (/^#{2,3}\\s/.test(line)) {\n        const level = line.match(/^#+/)[0].length;\n        const content = line.replace(/^#+\\s/, '');\n        formattedLines.push( /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: level === 2 ? '18px' : '16px',\n            fontWeight: 'bold',\n            color: '#1f2937',\n            margin: '16px 0 8px 0',\n            borderBottom: level === 2 ? '2px solid #e5e7eb' : 'none',\n            paddingBottom: level === 2 ? '4px' : '0'\n          },\n          children: content\n        }, `header-${i}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 21\n        }, this));\n        continue;\n      }\n\n      // Regular text - just remove ** symbols\n      const processedLine = line.replace(/\\*\\*/g, '');\n      formattedLines.push( /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          margin: '4px 0',\n          lineHeight: '1.6'\n        },\n        children: processedLine\n      }, `text-${i}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this));\n    }\n    return formattedLines;\n  };\n  const inlineMathRegex = /\\\\\\(.*?\\\\\\)/g;\n  const blockMathRegex = /\\\\\\[.*?\\\\\\]/gs;\n  const boldTextRegex = /\\*\\*.*?\\*\\*/g;\n  // console.log('Text: ', text);\n  let modifiedText = text.replace(blockMathRegex, match => match.replace(/\\n/g, '~~NEWLINE~~'));\n  const lines = modifiedText.split('\\n');\n  // console.log('Lines with symbol: ', lines);\n  const restoredLines = lines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\n  // console.log('Lines: ', restoredLines);\n\n  const inlineMathSymbol = \"~~INLINEMATH~~\";\n  const blockMathSymbol = \"~~BLOCKMATH~~\";\n  const boldSymbol = \"~~BOLD~~\";\n  let newModifiedText = text.replace(blockMathRegex, match => {\n    return `~~BLOCKMATH~~${match.replace(/\\n/g, '~~NEWLINE~~')}~~BLOCKMATH~~`;\n  });\n  newModifiedText = newModifiedText.replace(inlineMathRegex, match => {\n    return `~~INLINEMATH~~${match}~~INLINEMATH~~`;\n  });\n  newModifiedText = newModifiedText.replace(boldTextRegex, match => {\n    // console.log('Bold Part: ', match);\n    return `~~BOLD~~${match.replace(/\\*\\*/g, '')}~~BOLD~~`;\n  });\n  const newLines = newModifiedText.split('\\n');\n  const newRestoredLines = newLines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\n\n  // console.log('New Modified Text: ', newModifiedText);\n\n  const newRegex = /(~~INLINEMATH~~\\\\?\\(.*?\\\\?\\)~~INLINEMATH~~|~~BLOCKMATH~~\\\\?\\[.*?\\\\?\\]~~BLOCKMATH~~|~~BOLD~~.*?~~BOLD~~)/;\n\n  // Debug logging removed to prevent React rendering issues\n\n  // Check if text contains mathematical expressions\n  const hasMath = inlineMathRegex.test(text) || blockMathRegex.test(text);\n\n  // If no math, use enhanced AI formatting\n  if (!hasMath) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 'inherit',\n        lineHeight: 'inherit'\n      },\n      children: formatAIResponse(text)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Original math rendering logic for mathematical content\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: newRestoredLines.map((line, lineIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: line.trim() === '' ? /*#__PURE__*/_jsxDEV(\"br\", {}, `br-${lineIndex}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 25\n      }, this) : line.split(newRegex).map((part, index) => {\n        if (part.startsWith(boldSymbol) && part.endsWith(boldSymbol)) {\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: part.replace(/~~BOLD~~/g, '').split(newRegex).map((nestedPart, n_index) => {\n              if (nestedPart.startsWith(inlineMathSymbol) && nestedPart.endsWith(inlineMathSymbol)) {\n                return /*#__PURE__*/_jsxDEV(InlineMath, {\n                  children: nestedPart.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')\n                }, `${lineIndex}-${index}-${n_index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 53\n                }, this);\n              } else if (nestedPart.startsWith(blockMathSymbol) && nestedPart.endsWith(blockMathSymbol)) {\n                return /*#__PURE__*/_jsxDEV(BlockMath, {\n                  children: nestedPart.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')\n                }, `${lineIndex}-${index}-${n_index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 53\n                }, this);\n              } else {\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    whiteSpace: 'pre-wrap'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: nestedPart\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 57\n                  }, this)\n                }, `${lineIndex}-${index}-${n_index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 53\n                }, this);\n              }\n            })\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 37\n          }, this);\n        } else if (part.startsWith(inlineMathSymbol) && part.endsWith(inlineMathSymbol)) {\n          return /*#__PURE__*/_jsxDEV(InlineMath, {\n            children: part.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 37\n          }, this);\n        } else if (part.startsWith(blockMathSymbol) && part.endsWith(blockMathSymbol)) {\n          return /*#__PURE__*/_jsxDEV(BlockMath, {\n            children: part.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 37\n          }, this);\n        } else {\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              whiteSpace: 'pre-wrap'\n            },\n            children: part\n          }, `${lineIndex}-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 37\n          }, this);\n        }\n      })\n    }, lineIndex, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 9\n  }, this);\n};\n_c = ContentRenderer;\nexport default ContentRenderer;\nvar _c;\n$RefreshReg$(_c, \"ContentRenderer\");", "map": {"version": 3, "names": ["React", "InlineMath", "BlockMath", "jsxDEV", "_jsxDEV", "katexCSS", "document", "getElementById", "style", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "Content<PERSON><PERSON><PERSON>", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatAIResponse", "content", "lines", "split", "formattedLines", "i", "length", "line", "trim", "push", "test", "replace", "margin", "paddingLeft", "position", "lineHeight", "children", "left", "fontWeight", "color", "match", "level", "fontSize", "borderBottom", "paddingBottom", "processedLine", "inlineMathRegex", "blockMathRegex", "boldTextRegex", "modifiedText", "restoredLines", "map", "inlineMathSymbol", "blockMathSymbol", "boldSymbol", "newModifiedText", "newLines", "newRestoredLines", "newRegex", "<PERSON><PERSON><PERSON>", "lineIndex", "part", "index", "startsWith", "endsWith", "Fragment", "nested<PERSON><PERSON>", "n_index", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ContentRenderer.js"], "sourcesContent": ["import React from 'react';\r\nimport { InlineMath, BlockMath } from 'react-katex';\r\nimport 'katex/dist/katex.min.css';\r\n\r\n// Ensure KaTeX CSS is loaded\r\nconst katexCSS = `\r\n.katex {\r\n  font-size: 1.1em !important;\r\n  line-height: 1.2 !important;\r\n}\r\n.katex-display {\r\n  margin: 1em 0 !important;\r\n  text-align: center !important;\r\n}\r\n`;\r\n\r\n// Inject CSS if not already present\r\nif (typeof document !== 'undefined' && !document.getElementById('katex-custom-styles')) {\r\n  const style = document.createElement('style');\r\n  style.id = 'katex-custom-styles';\r\n  style.textContent = katexCSS;\r\n  document.head.appendChild(style);\r\n}\r\n\r\nconst ContentRenderer = ({ text }) => {\r\n    // Handle undefined, null, or empty text\r\n    if (!text || typeof text !== 'string') {\r\n        return <div></div>;\r\n    }\r\n\r\n    // Enhanced formatting for AI responses\r\n    const formatAIResponse = (content) => {\r\n        // Split into lines for processing\r\n        const lines = content.split('\\n');\r\n        const formattedLines = [];\r\n\r\n        for (let i = 0; i < lines.length; i++) {\r\n            const line = lines[i].trim();\r\n\r\n            // Skip empty lines but preserve spacing\r\n            if (!line) {\r\n                formattedLines.push(<br key={`br-${i}`} />);\r\n                continue;\r\n            }\r\n\r\n            // Handle numbered lists (1., 2., etc.)\r\n            if (/^\\d+\\.\\s/.test(line)) {\r\n                const content = line.replace(/^\\d+\\.\\s/, '');\r\n                formattedLines.push(\r\n                    <div key={`numbered-${i}`} style={{\r\n                        margin: '8px 0',\r\n                        paddingLeft: '16px',\r\n                        position: 'relative',\r\n                        lineHeight: '1.6'\r\n                    }}>\r\n                        <span style={{\r\n                            position: 'absolute',\r\n                            left: '0',\r\n                            fontWeight: 'bold',\r\n                            color: '#3b82f6'\r\n                        }}>\r\n                            {line.match(/^\\d+/)[0]}.\r\n                        </span>\r\n                        <span>{content}</span>\r\n                    </div>\r\n                );\r\n                continue;\r\n            }\r\n\r\n            // Handle bullet points (-, *, •)\r\n            if (/^[-*•]\\s/.test(line)) {\r\n                const content = line.replace(/^[-*•]\\s/, '');\r\n                formattedLines.push(\r\n                    <div key={`bullet-${i}`} style={{\r\n                        margin: '6px 0',\r\n                        paddingLeft: '16px',\r\n                        position: 'relative',\r\n                        lineHeight: '1.6'\r\n                    }}>\r\n                        <span style={{\r\n                            position: 'absolute',\r\n                            left: '0',\r\n                            color: '#3b82f6',\r\n                            fontWeight: 'bold'\r\n                        }}>\r\n                            •\r\n                        </span>\r\n                        <span>{content}</span>\r\n                    </div>\r\n                );\r\n                continue;\r\n            }\r\n\r\n            // Handle headers (##, ###)\r\n            if (/^#{2,3}\\s/.test(line)) {\r\n                const level = line.match(/^#+/)[0].length;\r\n                const content = line.replace(/^#+\\s/, '');\r\n                formattedLines.push(\r\n                    <div key={`header-${i}`} style={{\r\n                        fontSize: level === 2 ? '18px' : '16px',\r\n                        fontWeight: 'bold',\r\n                        color: '#1f2937',\r\n                        margin: '16px 0 8px 0',\r\n                        borderBottom: level === 2 ? '2px solid #e5e7eb' : 'none',\r\n                        paddingBottom: level === 2 ? '4px' : '0'\r\n                    }}>\r\n                        {content}\r\n                    </div>\r\n                );\r\n                continue;\r\n            }\r\n\r\n            // Regular text - just remove ** symbols\r\n            const processedLine = line.replace(/\\*\\*/g, '');\r\n            formattedLines.push(\r\n                <div key={`text-${i}`} style={{\r\n                    margin: '4px 0',\r\n                    lineHeight: '1.6'\r\n                }}>\r\n                    {processedLine}\r\n                </div>\r\n            );\r\n        }\r\n\r\n        return formattedLines;\r\n    };\r\n\r\n    const inlineMathRegex = /\\\\\\(.*?\\\\\\)/g;\r\n    const blockMathRegex = /\\\\\\[.*?\\\\\\]/gs;\r\n    const boldTextRegex = /\\*\\*.*?\\*\\*/g;\r\n    // console.log('Text: ', text);\r\n    let modifiedText = text.replace(blockMathRegex, match => match.replace(/\\n/g, '~~NEWLINE~~'));\r\n    const lines = modifiedText.split('\\n');\r\n    // console.log('Lines with symbol: ', lines);\r\n    const restoredLines = lines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n    // console.log('Lines: ', restoredLines);\r\n\r\n\r\n\r\n\r\n    const inlineMathSymbol = \"~~INLINEMATH~~\";\r\n    const blockMathSymbol = \"~~BLOCKMATH~~\";\r\n    const boldSymbol = \"~~BOLD~~\";\r\n\r\n    let newModifiedText = text.replace(blockMathRegex, match => {\r\n        return `~~BLOCKMATH~~${match.replace(/\\n/g, '~~NEWLINE~~')}~~BLOCKMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(inlineMathRegex, match => {\r\n        return `~~INLINEMATH~~${match}~~INLINEMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(boldTextRegex, match => {\r\n        // console.log('Bold Part: ', match);\r\n        return `~~BOLD~~${match.replace(/\\*\\*/g, '')}~~BOLD~~`;\r\n    });\r\n\r\n    const newLines = newModifiedText.split('\\n');\r\n\r\n    const newRestoredLines = newLines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n\r\n    // console.log('New Modified Text: ', newModifiedText);\r\n\r\n    const newRegex = /(~~INLINEMATH~~\\\\?\\(.*?\\\\?\\)~~INLINEMATH~~|~~BLOCKMATH~~\\\\?\\[.*?\\\\?\\]~~BLOCKMATH~~|~~BOLD~~.*?~~BOLD~~)/;\r\n\r\n    // Debug logging removed to prevent React rendering issues\r\n\r\n    // Check if text contains mathematical expressions\r\n    const hasMath = inlineMathRegex.test(text) || blockMathRegex.test(text);\r\n\r\n    // If no math, use enhanced AI formatting\r\n    if (!hasMath) {\r\n        return (\r\n            <div style={{ fontSize: 'inherit', lineHeight: 'inherit' }}>\r\n                {formatAIResponse(text)}\r\n            </div>\r\n        );\r\n    }\r\n\r\n    // Original math rendering logic for mathematical content\r\n    return (\r\n        <div>\r\n            {newRestoredLines.map((line, lineIndex) => (\r\n                <div key={lineIndex}>\r\n                    {line.trim() === '' ?\r\n                        <br key={`br-${lineIndex}`} />\r\n                        :\r\n                        line.split(newRegex).map((part, index) => {\r\n                            if (part.startsWith(boldSymbol) && part.endsWith(boldSymbol)) {\r\n                                return (\r\n                                    <React.Fragment key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BOLD~~/g, '').split(newRegex).map((nestedPart, n_index) => {\r\n                                            if (nestedPart.startsWith(inlineMathSymbol) && nestedPart.endsWith(inlineMathSymbol)) {\r\n                                                return (\r\n                                                    <InlineMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                                    </InlineMath>\r\n                                                );\r\n                                            } else if (nestedPart.startsWith(blockMathSymbol) && nestedPart.endsWith(blockMathSymbol)) {\r\n                                                return (\r\n                                                    <BlockMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                                    </BlockMath>\r\n                                                );\r\n                                            } else {\r\n                                                return (\r\n                                                    <span key={`${lineIndex}-${index}-${n_index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                                        <strong>{nestedPart}</strong>\r\n                                                    </span>\r\n                                                );\r\n                                            }\r\n                                        })}\r\n                                    </React.Fragment>\r\n                                );\r\n                            } else if (part.startsWith(inlineMathSymbol) && part.endsWith(inlineMathSymbol)) {\r\n                                return (\r\n                                    <InlineMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                    </InlineMath>\r\n                                );\r\n                            } else if (part.startsWith(blockMathSymbol) && part.endsWith(blockMathSymbol)) {\r\n                                return (\r\n                                    <BlockMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                    </BlockMath>\r\n                                );\r\n                            } else {\r\n                                return (\r\n                                    <span key={`${lineIndex}-${index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                        {part}\r\n                                    </span>\r\n                                );\r\n                            }\r\n                        })}\r\n                </div>\r\n            ))}\r\n        </div>\r\n\r\n    )\r\n};\r\n\r\nexport default ContentRenderer;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,SAAS,QAAQ,aAAa;AACnD,OAAO,0BAA0B;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,QAAQ,GAAI;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC,EAAE;EACtF,MAAMC,KAAK,GAAGF,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EAC7CD,KAAK,CAACE,EAAE,GAAG,qBAAqB;EAChCF,KAAK,CAACG,WAAW,GAAGN,QAAQ;EAC5BC,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;AAClC;AAEA,MAAMM,eAAe,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAClC;EACA,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACnC,oBAAOX,OAAA;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAU,CAAC;EACtB;;EAEA;EACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IAClC;IACA,MAAMC,KAAK,GAAGD,OAAO,CAACE,KAAK,CAAC,IAAI,CAAC;IACjC,MAAMC,cAAc,GAAG,EAAE;IAEzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC,MAAME,IAAI,GAAGL,KAAK,CAACG,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;;MAE5B;MACA,IAAI,CAACD,IAAI,EAAE;QACPH,cAAc,CAACK,IAAI,eAACzB,OAAA,WAAU,MAAKqB,CAAE,EAAC;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,CAAC;QAC3C;MACJ;;MAEA;MACA,IAAI,UAAU,CAACW,IAAI,CAACH,IAAI,CAAC,EAAE;QACvB,MAAMN,OAAO,GAAGM,IAAI,CAACI,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;QAC5CP,cAAc,CAACK,IAAI,eACfzB,OAAA;UAA2BI,KAAK,EAAE;YAC9BwB,MAAM,EAAE,OAAO;YACfC,WAAW,EAAE,MAAM;YACnBC,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE;UAChB,CAAE;UAAAC,QAAA,gBACEhC,OAAA;YAAMI,KAAK,EAAE;cACT0B,QAAQ,EAAE,UAAU;cACpBG,IAAI,EAAE,GAAG;cACTC,UAAU,EAAE,MAAM;cAClBC,KAAK,EAAE;YACX,CAAE;YAAAH,QAAA,GACGT,IAAI,CAACa,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC,GAC3B;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPf,OAAA;YAAAgC,QAAA,EAAOf;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAdf,YAAWM,CAAE,EAAC;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAepB,CACT,CAAC;QACD;MACJ;;MAEA;MACA,IAAI,UAAU,CAACW,IAAI,CAACH,IAAI,CAAC,EAAE;QACvB,MAAMN,OAAO,GAAGM,IAAI,CAACI,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;QAC5CP,cAAc,CAACK,IAAI,eACfzB,OAAA;UAAyBI,KAAK,EAAE;YAC5BwB,MAAM,EAAE,OAAO;YACfC,WAAW,EAAE,MAAM;YACnBC,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE;UAChB,CAAE;UAAAC,QAAA,gBACEhC,OAAA;YAAMI,KAAK,EAAE;cACT0B,QAAQ,EAAE,UAAU;cACpBG,IAAI,EAAE,GAAG;cACTE,KAAK,EAAE,SAAS;cAChBD,UAAU,EAAE;YAChB,CAAE;YAAAF,QAAA,EAAC;UAEH;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPf,OAAA;YAAAgC,QAAA,EAAOf;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAdf,UAASM,CAAE,EAAC;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAelB,CACT,CAAC;QACD;MACJ;;MAEA;MACA,IAAI,WAAW,CAACW,IAAI,CAACH,IAAI,CAAC,EAAE;QACxB,MAAMc,KAAK,GAAGd,IAAI,CAACa,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACd,MAAM;QACzC,MAAML,OAAO,GAAGM,IAAI,CAACI,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;QACzCP,cAAc,CAACK,IAAI,eACfzB,OAAA;UAAyBI,KAAK,EAAE;YAC5BkC,QAAQ,EAAED,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM;YACvCH,UAAU,EAAE,MAAM;YAClBC,KAAK,EAAE,SAAS;YAChBP,MAAM,EAAE,cAAc;YACtBW,YAAY,EAAEF,KAAK,KAAK,CAAC,GAAG,mBAAmB,GAAG,MAAM;YACxDG,aAAa,EAAEH,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG;UACzC,CAAE;UAAAL,QAAA,EACGf;QAAO,GARD,UAASI,CAAE,EAAC;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASlB,CACT,CAAC;QACD;MACJ;;MAEA;MACA,MAAM0B,aAAa,GAAGlB,IAAI,CAACI,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MAC/CP,cAAc,CAACK,IAAI,eACfzB,OAAA;QAAuBI,KAAK,EAAE;UAC1BwB,MAAM,EAAE,OAAO;UACfG,UAAU,EAAE;QAChB,CAAE;QAAAC,QAAA,EACGS;MAAa,GAJP,QAAOpB,CAAE,EAAC;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKhB,CACT,CAAC;IACL;IAEA,OAAOK,cAAc;EACzB,CAAC;EAED,MAAMsB,eAAe,GAAG,cAAc;EACtC,MAAMC,cAAc,GAAG,eAAe;EACtC,MAAMC,aAAa,GAAG,cAAc;EACpC;EACA,IAAIC,YAAY,GAAGlC,IAAI,CAACgB,OAAO,CAACgB,cAAc,EAAEP,KAAK,IAAIA,KAAK,CAACT,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;EAC7F,MAAMT,KAAK,GAAG2B,YAAY,CAAC1B,KAAK,CAAC,IAAI,CAAC;EACtC;EACA,MAAM2B,aAAa,GAAG5B,KAAK,CAAC6B,GAAG,CAACxB,IAAI,IAAIA,IAAI,CAACI,OAAO,CAAC,cAAc,EAAG,MAAK,CAAC,CAAC;EAC7E;;EAKA,MAAMqB,gBAAgB,GAAG,gBAAgB;EACzC,MAAMC,eAAe,GAAG,eAAe;EACvC,MAAMC,UAAU,GAAG,UAAU;EAE7B,IAAIC,eAAe,GAAGxC,IAAI,CAACgB,OAAO,CAACgB,cAAc,EAAEP,KAAK,IAAI;IACxD,OAAQ,gBAAeA,KAAK,CAACT,OAAO,CAAC,KAAK,EAAE,aAAa,CAAE,eAAc;EAC7E,CAAC,CAAC;EAEFwB,eAAe,GAAGA,eAAe,CAACxB,OAAO,CAACe,eAAe,EAAEN,KAAK,IAAI;IAChE,OAAQ,iBAAgBA,KAAM,gBAAe;EACjD,CAAC,CAAC;EAEFe,eAAe,GAAGA,eAAe,CAACxB,OAAO,CAACiB,aAAa,EAAER,KAAK,IAAI;IAC9D;IACA,OAAQ,WAAUA,KAAK,CAACT,OAAO,CAAC,OAAO,EAAE,EAAE,CAAE,UAAS;EAC1D,CAAC,CAAC;EAEF,MAAMyB,QAAQ,GAAGD,eAAe,CAAChC,KAAK,CAAC,IAAI,CAAC;EAE5C,MAAMkC,gBAAgB,GAAGD,QAAQ,CAACL,GAAG,CAACxB,IAAI,IAAIA,IAAI,CAACI,OAAO,CAAC,cAAc,EAAG,MAAK,CAAC,CAAC;;EAEnF;;EAEA,MAAM2B,QAAQ,GAAG,yGAAyG;;EAE1H;;EAEA;EACA,MAAMC,OAAO,GAAGb,eAAe,CAAChB,IAAI,CAACf,IAAI,CAAC,IAAIgC,cAAc,CAACjB,IAAI,CAACf,IAAI,CAAC;;EAEvE;EACA,IAAI,CAAC4C,OAAO,EAAE;IACV,oBACIvD,OAAA;MAAKI,KAAK,EAAE;QAAEkC,QAAQ,EAAE,SAAS;QAAEP,UAAU,EAAE;MAAU,CAAE;MAAAC,QAAA,EACtDhB,gBAAgB,CAACL,IAAI;IAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAEd;;EAEA;EACA,oBACIf,OAAA;IAAAgC,QAAA,EACKqB,gBAAgB,CAACN,GAAG,CAAC,CAACxB,IAAI,EAAEiC,SAAS,kBAClCxD,OAAA;MAAAgC,QAAA,EACKT,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,gBACfxB,OAAA,WAAU,MAAKwD,SAAU,EAAC;QAAA5C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,GAE9BQ,IAAI,CAACJ,KAAK,CAACmC,QAAQ,CAAC,CAACP,GAAG,CAAC,CAACU,IAAI,EAAEC,KAAK,KAAK;QACtC,IAAID,IAAI,CAACE,UAAU,CAACT,UAAU,CAAC,IAAIO,IAAI,CAACG,QAAQ,CAACV,UAAU,CAAC,EAAE;UAC1D,oBACIlD,OAAA,CAACJ,KAAK,CAACiE,QAAQ;YAAA7B,QAAA,EACVyB,IAAI,CAAC9B,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACR,KAAK,CAACmC,QAAQ,CAAC,CAACP,GAAG,CAAC,CAACe,UAAU,EAAEC,OAAO,KAAK;cACxE,IAAID,UAAU,CAACH,UAAU,CAACX,gBAAgB,CAAC,IAAIc,UAAU,CAACF,QAAQ,CAACZ,gBAAgB,CAAC,EAAE;gBAClF,oBACIhD,OAAA,CAACH,UAAU;kBAAAmC,QAAA,EACN8B,UAAU,CAACnC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,cAAc,EAAE,EAAE;gBAAC,GADxD,GAAE6B,SAAU,IAAGE,KAAM,IAAGK,OAAQ,EAAC;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvC,CAAC;cAErB,CAAC,MAAM,IAAI+C,UAAU,CAACH,UAAU,CAACV,eAAe,CAAC,IAAIa,UAAU,CAACF,QAAQ,CAACX,eAAe,CAAC,EAAE;gBACvF,oBACIjD,OAAA,CAACF,SAAS;kBAAAkC,QAAA,EACL8B,UAAU,CAACnC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,EAAE;gBAAC,GADrD,GAAE6B,SAAU,IAAGE,KAAM,IAAGK,OAAQ,EAAC;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvC,CAAC;cAEpB,CAAC,MAAM;gBACH,oBACIf,OAAA;kBAA+CI,KAAK,EAAE;oBAAE4D,UAAU,EAAE;kBAAW,CAAE;kBAAAhC,QAAA,eAC7EhC,OAAA;oBAAAgC,QAAA,EAAS8B;kBAAU;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC,GADrB,GAAEyC,SAAU,IAAGE,KAAM,IAAGK,OAAQ,EAAC;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEvC,CAAC;cAEf;YACJ,CAAC;UAAC,GArBgB,GAAEyC,SAAU,IAAGE,KAAM,EAAC;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsB5B,CAAC;QAEzB,CAAC,MAAM,IAAI0C,IAAI,CAACE,UAAU,CAACX,gBAAgB,CAAC,IAAIS,IAAI,CAACG,QAAQ,CAACZ,gBAAgB,CAAC,EAAE;UAC7E,oBACIhD,OAAA,CAACH,UAAU;YAAAmC,QAAA,EACNyB,IAAI,CAAC9B,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,cAAc,EAAE,EAAE;UAAC,GADlD,GAAE6B,SAAU,IAAGE,KAAM,EAAC;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC;QAErB,CAAC,MAAM,IAAI0C,IAAI,CAACE,UAAU,CAACV,eAAe,CAAC,IAAIQ,IAAI,CAACG,QAAQ,CAACX,eAAe,CAAC,EAAE;UAC3E,oBACIjD,OAAA,CAACF,SAAS;YAAAkC,QAAA,EACLyB,IAAI,CAAC9B,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,EAAE;UAAC,GAD/C,GAAE6B,SAAU,IAAGE,KAAM,EAAC;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC;QAEpB,CAAC,MAAM;UACH,oBACIf,OAAA;YAAoCI,KAAK,EAAE;cAAE4D,UAAU,EAAE;YAAW,CAAE;YAAAhC,QAAA,EACjEyB;UAAI,GADG,GAAED,SAAU,IAAGE,KAAM,EAAC;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE5B,CAAC;QAEf;MACJ,CAAC;IAAC,GAlDAyC,SAAS;MAAA5C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAmDd,CACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAGd,CAAC;AAACkD,EAAA,GAvNIvD,eAAe;AAyNrB,eAAeA,eAAe;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}