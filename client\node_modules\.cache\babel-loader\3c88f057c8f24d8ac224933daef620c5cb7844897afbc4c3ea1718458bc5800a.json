{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo, useRef } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { getVideoComments, addVideoComment, addCommentReply, likeComment, deleteVideoComment } from \"../../../apicalls/videoComments\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { message } from \"antd\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\nimport { MdVerified } from 'react-icons/md';\n\n// Temporary fix: Use simple text/symbols instead of React Icons to avoid chunk loading issues\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst IconComponents = {\n  FaPlayCircle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\u25B6\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 23\n  }, this),\n  FaGraduationCap: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\uD83C\\uDF93\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 26\n  }, this),\n  FaTimes: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u2715\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 18\n  }, this),\n  FaExpand: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u26F6\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 19\n  }, this),\n  FaCompress: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u26F6\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 21\n  }, this),\n  TbVideo: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\uD83D\\uDCF9\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 18\n  }, this),\n  TbInfoCircle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '16px'\n    },\n    children: \"\\u2139\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 23\n  }, this),\n  TbAlertTriangle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '16px'\n    },\n    children: \"\\u26A0\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 26\n  }, this),\n  TbFilter: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\uD83D\\uDD0D\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 19\n  }, this),\n  TbSortAscending: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u2191\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 26\n  }, this),\n  TbSearch: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\uD83D\\uDD0D\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 19\n  }, this),\n  TbX: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '16px'\n    },\n    children: \"\\u2715\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 14\n  }, this),\n  TbDownload: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u21BB\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 21\n  }, this)\n};\n\n// Destructure for easy use\nconst {\n  FaPlayCircle,\n  FaGraduationCap,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  TbVideo,\n  TbFilter,\n  TbSortAscending,\n  TbSearch,\n  TbX,\n  TbDownload,\n  TbAlertTriangle,\n  TbInfoCircle\n} = IconComponents;\nfunction VideoLessons() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili,\n    getClassName,\n    getSubjectName\n  } = useLanguage();\n  const dispatch = useDispatch();\n\n  // State management with localStorage persistence\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState((user === null || user === void 0 ? void 0 : user.level) || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(() => {\n    // Restore from localStorage or use user's class as default\n    return localStorage.getItem('video-lessons-selected-class') || (user === null || user === void 0 ? void 0 : user.class) || \"all\";\n  });\n  const [selectedSubject, setSelectedSubject] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-selected-subject') || \"all\";\n  });\n  const [searchTerm, setSearchTerm] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-search-term') || \"\";\n  });\n  const [sortBy, setSortBy] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-sort-by') || \"newest\";\n  });\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state - store comments per video\n  const [videoComments, setVideoComments] = useState({});\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n  const [showComments, setShowComments] = useState(true);\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [editingComment, setEditingComment] = useState(null);\n  const [editCommentText, setEditCommentText] = useState(\"\");\n\n  // Get comments for current video\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n    if (!currentVideo) return [];\n\n    // Try both id and _id fields\n    const videoId = currentVideo.id || currentVideo._id;\n    return videoComments[videoId] || [];\n  };\n\n  // Set comments for current video\n  const setCurrentVideoComments = comments => {\n    if (currentVideoIndex === null) return;\n    const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n    if (!currentVideo) return;\n\n    // Use the same videoId logic as getCurrentVideoComments\n    const videoId = currentVideo.id || currentVideo._id;\n    setVideoComments(prev => ({\n      ...prev,\n      [videoId]: comments\n    }));\n  };\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\" || selectedLevel === \"primary_kiswahili\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"primary_kiswahili\") return primaryKiswahiliSubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n      const filters = {\n        level: selectedLevel,\n        className: \"all\",\n        // Get all classes for the level\n        subject: \"all\",\n        // Get all subjects for the level\n        content: \"videos\"\n      };\n      const response = await getStudyMaterial(filters);\n      if (response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        const videoData = response.data.data || [];\n        setVideos(videoData);\n\n        // Load comments for all videos\n        await loadAllVideoComments(videoData);\n      } else {\n        var _response$data2;\n        setError((response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        return videoClass === selectedClass;\n      });\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video => {\n        var _video$title, _video$subject, _video$topic;\n        return ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchLower)) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchLower)) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n    console.log('✅ Final filtered videos:', sorted.length);\n    if (sorted.length > 0) {\n      console.log('📹 Sample filtered video:', sorted[0]);\n    }\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Load comments for this video if not already loaded\n    const videoId = (video === null || video === void 0 ? void 0 : video.id) || (video === null || video === void 0 ? void 0 : video._id);\n    if (videoId && !videoComments[videoId]) {\n      loadVideoComments(videoId);\n    }\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async videoUrl => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = video => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Handle filter changes with localStorage persistence\n  const handleClassChange = value => {\n    setSelectedClass(value);\n    localStorage.setItem('video-lessons-selected-class', value);\n  };\n  const handleSubjectChange = value => {\n    setSelectedSubject(value);\n    localStorage.setItem('video-lessons-selected-subject', value);\n  };\n  const handleSearchChange = value => {\n    setSearchTerm(value);\n    localStorage.setItem('video-lessons-search-term', value);\n  };\n  const handleSortChange = value => {\n    setSortBy(value);\n    localStorage.setItem('video-lessons-sort-by', value);\n  };\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level) {\n      setSelectedLevel(user.level);\n    }\n    // Only set user's class as default if no saved preference exists\n    if (user !== null && user !== void 0 && user.class && !localStorage.getItem('video-lessons-selected-class')) {\n      handleClassChange(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    handleSearchChange(\"\");\n  };\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n  const handleClearAll = () => {\n    handleSearchChange(\"\");\n    handleSubjectChange(\"all\");\n    handleClassChange(\"all\");\n    handleSortChange(\"newest\");\n    fetchVideos();\n  };\n\n  // Load comments for all videos\n  const loadAllVideoComments = async videoList => {\n    try {\n      console.log('📹 Loading comments for all videos:', videoList.length);\n      const commentsMap = {};\n\n      // Load comments for each video\n      for (const video of videoList) {\n        const videoId = video.id || video._id;\n        if (videoId) {\n          try {\n            const response = await getVideoComments(videoId);\n            if (response.success) {\n              commentsMap[videoId] = response.data.comments;\n              console.log(`📝 Loaded ${response.data.comments.length} comments for video ${videoId}`);\n            }\n          } catch (error) {\n            console.error(`Error loading comments for video ${videoId}:`, error);\n          }\n        }\n      }\n      setVideoComments(commentsMap);\n      console.log('✅ All video comments loaded:', commentsMap);\n    } catch (error) {\n      console.error(\"Error loading all video comments:\", error);\n    }\n  };\n\n  // Load comments for current video\n  const loadVideoComments = async videoId => {\n    try {\n      const response = await getVideoComments(videoId);\n      if (response.success) {\n        setVideoComments(prev => ({\n          ...prev,\n          [videoId]: response.data.comments\n        }));\n      }\n    } catch (error) {\n      console.error(\"Error loading comments:\", error);\n    }\n  };\n\n  // Comment functions\n  const handleAddComment = async () => {\n    if (newComment.trim()) {\n      const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n      if (!currentVideo) return;\n      try {\n        console.log('📹 Current video object:', currentVideo);\n        console.log('📹 Video keys:', Object.keys(currentVideo || {}));\n        console.log('📹 Video id field:', currentVideo === null || currentVideo === void 0 ? void 0 : currentVideo.id);\n        console.log('📹 Video _id field:', currentVideo === null || currentVideo === void 0 ? void 0 : currentVideo._id);\n\n        // Use _id if id doesn't exist\n        const videoId = currentVideo.id || currentVideo._id;\n        const commentData = {\n          videoId: videoId,\n          text: newComment.trim()\n        };\n        console.log('📝 Sending video comment:', commentData);\n        console.log('📝 Comment data keys:', Object.keys(commentData));\n        console.log('📝 videoId value:', videoId, '(type:', typeof videoId, ')');\n        console.log('📝 text value:', newComment.trim(), '(type:', typeof newComment.trim(), ')');\n        const response = await addVideoComment(commentData);\n        if (response.success) {\n          // Add comment to local state immediately for better UX\n          const comment = {\n            _id: response.data._id,\n            text: response.data.text,\n            author: response.data.author,\n            avatar: response.data.avatar,\n            createdAt: response.data.createdAt,\n            replies: [],\n            likes: 0,\n            likedBy: []\n          };\n          const currentComments = getCurrentVideoComments();\n          setCurrentVideoComments([comment, ...currentComments]);\n          setNewComment(\"\");\n          message.success(\"Comment added successfully!\");\n        } else {\n          message.error(response.message || \"Failed to add comment\");\n        }\n      } catch (error) {\n        console.error(\"Error adding comment:\", error);\n        message.error(\"Failed to add comment\");\n      }\n    }\n  };\n  const handleAddReply = async commentId => {\n    if (replyText.trim()) {\n      try {\n        const response = await addCommentReply(commentId, {\n          text: replyText.trim()\n        });\n        if (response.success) {\n          // Update local state with the new reply\n          const currentComments = getCurrentVideoComments();\n          const updatedComments = currentComments.map(comment => comment._id === commentId || comment.id === commentId ? {\n            ...comment,\n            replies: response.data.replies\n          } : comment);\n          setCurrentVideoComments(updatedComments);\n          setReplyText(\"\");\n          setReplyingTo(null);\n          message.success(\"Reply added successfully!\");\n        } else {\n          message.error(response.message || \"Failed to add reply\");\n        }\n      } catch (error) {\n        console.error(\"Error adding reply:\", error);\n        message.error(\"Failed to add reply\");\n      }\n    }\n  };\n  const handleLikeComment = async (commentId, isReply = false, replyId = null) => {\n    try {\n      const response = await likeComment(commentId, {\n        isReply,\n        replyId\n      });\n      if (response.success) {\n        // Update local state with the updated comment\n        const currentComments = getCurrentVideoComments();\n        const updatedComments = currentComments.map(comment => comment._id === commentId || comment.id === commentId ? response.data : comment);\n        setCurrentVideoComments(updatedComments);\n      } else {\n        message.error(response.message || \"Failed to update like\");\n      }\n    } catch (error) {\n      console.error(\"Error updating like:\", error);\n      message.error(\"Failed to update like\");\n    }\n  };\n  const handleDeleteComment = async commentId => {\n    try {\n      const response = await deleteVideoComment(commentId);\n      if (response.success) {\n        // Remove comment from local state\n        const currentComments = getCurrentVideoComments();\n        const updatedComments = currentComments.filter(comment => comment._id !== commentId && comment.id !== commentId);\n        setCurrentVideoComments(updatedComments);\n        message.success(\"Comment deleted successfully!\");\n      } else {\n        message.error(response.message || \"Failed to delete comment\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting comment:\", error);\n      message.error(\"Failed to delete comment\");\n    }\n  };\n  const handleEditComment = comment => {\n    setEditingComment(comment._id || comment.id);\n    setEditCommentText(comment.text);\n  };\n  const handleSaveEditComment = async () => {\n    if (!editCommentText.trim()) {\n      message.error(\"Comment cannot be empty\");\n      return;\n    }\n    try {\n      // TODO: Add API call to update comment\n      // const response = await updateVideoComment(editingComment, { text: editCommentText.trim() });\n\n      // For now, update local state\n      const currentComments = getCurrentVideoComments();\n      const updatedComments = currentComments.map(comment => {\n        if ((comment._id || comment.id) === editingComment) {\n          return {\n            ...comment,\n            text: editCommentText.trim()\n          };\n        }\n        return comment;\n      });\n      setCurrentVideoComments(updatedComments);\n      setEditingComment(null);\n      setEditCommentText(\"\");\n      message.success(\"Comment updated successfully!\");\n    } catch (error) {\n      console.error(\"Error updating comment:\", error);\n      message.error(\"Failed to update comment\");\n    }\n  };\n  const handleCancelEdit = () => {\n    setEditingComment(null);\n    setEditCommentText(\"\");\n  };\n  const formatTimeAgo = timestamp => {\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return time.toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-main\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-icon\",\n            children: /*#__PURE__*/_jsxDEV(TbVideo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"Video Lessons\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Watch educational videos to enhance your learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"level-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-level\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-label\",\n              children: \"Level:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-value\",\n              children: selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-class\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-label\",\n              children: \"Your Class:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-value\",\n              children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'secondary' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'advance' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : 'Not Set'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 603,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 17\n              }, this), isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedClass,\n              onChange: e => handleClassChange(e.target.value),\n              className: \"control-select class-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 17\n              }, this), availableClasses.map(cls => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cls,\n                children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${cls}` : `Class ${cls}` : `Form ${cls}`\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 17\n              }, this), \"Subject\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedSubject,\n              onChange: e => handleSubjectChange(e.target.value),\n              className: \"control-select subject-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 17\n              }, this), availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSortAscending, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 17\n              }, this), \"Sort\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => handleSortChange(e.target.value),\n              className: \"control-select sort-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"newest\",\n                children: \"Newest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"oldest\",\n                children: \"Oldest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"title\",\n                children: \"Title A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"subject\",\n                children: \"Subject A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-container\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 702,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search videos by title, subject, or topic...\",\n              value: searchTerm,\n              onChange: e => handleSearchChange(e.target.value),\n              className: \"search-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 15\n            }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleClearSearch,\n              className: \"clear-search-btn\",\n              children: [/*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 19\n              }, this), \"Clear Search\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            className: \"refresh-btn\",\n            children: [/*#__PURE__*/_jsxDEV(TbDownload, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 15\n            }, this), \"Refresh All\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 700,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 729,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 727,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 733,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 736,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 732,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-card\",\n          onClick: () => handleShowVideo(index),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card-thumbnail\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: getThumbnailUrl(video),\n              alt: video.title,\n              className: \"thumbnail-image\",\n              loading: \"lazy\",\n              onError: e => {\n                // Fallback logic for failed thumbnails\n                if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                  // For YouTube videos, try different quality thumbnails\n                  let videoId = video.videoID;\n                  if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                  }\n                  const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                  const currentSrc = e.target.src;\n                  const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                  if (currentIndex < fallbacks.length - 1) {\n                    e.target.src = fallbacks[currentIndex + 1];\n                  }\n                } else {\n                  e.target.src = '/api/placeholder/320/180';\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"play-overlay\",\n              children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                className: \"play-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 778,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-duration\",\n              children: video.duration || \"Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 19\n            }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"subtitle-badge\",\n              children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 23\n              }, this), \"CC\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 744,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"video-title\",\n              children: video.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-subject\",\n                children: getSubjectName(video.subject)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 795,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-class\",\n                children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 796,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 794,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-tags\",\n              children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"topic-tag\",\n                children: video.topic\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 37\n              }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"shared-tag\",\n                children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 741,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 820,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 821,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 634,\n      columnNumber: 7\n    }, this), showVideoIndices.length > 0 && currentVideoIndex !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `video-overlay ${isVideoExpanded ? 'expanded' : ''}`,\n      onClick: e => {\n        if (e.target === e.currentTarget) handleHideVideo();\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `video-modal ${isVideoExpanded ? 'expanded' : ''}`,\n        children: ((_user$name, _user$name$charAt) => {\n          const video = filteredAndSortedVideos[currentVideoIndex];\n          if (!video) return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Video not found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 834,\n            columnNumber: 34\n          }, this);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"youtube-style-layout\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                marginBottom: '16px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"control-btn close-btn\",\n                onClick: handleHideVideo,\n                title: \"Close Video\",\n                style: {\n                  background: 'rgba(0,0,0,0.1)',\n                  border: 'none',\n                  borderRadius: '50%',\n                  width: '40px',\n                  height: '40px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '18px'\n                  },\n                  children: \"\\u2715\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"youtube-video-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-player\",\n                children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'relative',\n                    width: '100%',\n                    height: '100%'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: ref => setVideoRef(ref),\n                    controls: true,\n                    playsInline: true,\n                    preload: \"none\",\n                    width: \"100%\",\n                    height: \"100%\",\n                    poster: getThumbnailUrl(video),\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      backgroundColor: '#000',\n                      objectFit: 'contain'\n                    },\n                    loading: \"lazy\",\n                    onError: e => {\n                      setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                    },\n                    onCanPlay: () => {\n                      setVideoError(null);\n                    },\n                    onLoadStart: () => {\n                      console.log('🎬 Video loading started');\n                    },\n                    crossOrigin: \"anonymous\",\n                    children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                      src: video.signedVideoUrl || video.videoUrl,\n                      type: \"video/mp4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 894,\n                      columnNumber: 29\n                    }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                      kind: \"subtitles\",\n                      src: subtitle.url,\n                      srcLang: subtitle.language,\n                      label: subtitle.languageName,\n                      default: subtitle.isDefault || index === 0\n                    }, `${subtitle.language}-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 898,\n                      columnNumber: 31\n                    }, this)), \"Your browser does not support the video tag.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 27\n                  }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"subtitle-indicator\",\n                    children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {\n                      className: \"subtitle-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 914,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Subtitles available in \", video.subtitles.length, \" language(s)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 915,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 913,\n                    columnNumber: 29\n                  }, this), videoError && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"video-error-overlay\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"error-content\",\n                      children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n                        className: \"error-icon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 923,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: videoError\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 924,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => setVideoError(null),\n                        className: \"dismiss-error-btn\",\n                        children: \"Dismiss\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 925,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 922,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 921,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 866,\n                  columnNumber: 23\n                }, this) : video.videoID ?\n                /*#__PURE__*/\n                // Fallback to YouTube embed if no videoUrl\n                _jsxDEV(\"iframe\", {\n                  src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                  title: video.title,\n                  frameBorder: \"0\",\n                  allowFullScreen: true,\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    border: 'none'\n                  },\n                  onLoad: () => console.log('✅ YouTube iframe loaded')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 934,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-icon\",\n                    children: \"\\u26A0\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 944,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: \"Video Unavailable\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 945,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: videoError || \"This video cannot be played at the moment.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-actions\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: video.signedVideoUrl || video.videoUrl,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      className: \"external-link-btn\",\n                      children: \"\\uD83D\\uDCF1 Open in New Tab\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 948,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 947,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 943,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"youtube-video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: video.subject\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 965,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u2022\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 966,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Class \", video.className]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 967,\n                    columnNumber: 25\n                  }, this), video.level && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 970,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: video.level\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 971,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 964,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `youtube-action-btn ${commentsExpanded ? 'active' : ''}`,\n                    onClick: () => setCommentsExpanded(!commentsExpanded),\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\uD83D\\uDCAC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 980,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Comments\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 981,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 976,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"youtube-action-btn\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\uD83D\\uDC4D\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 984,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Like\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 985,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 983,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"youtube-action-btn\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\uD83D\\uDCE4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 988,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Share\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 989,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 987,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 975,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 962,\n                columnNumber: 21\n              }, this), commentsExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-comments-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comments-header\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [getCurrentVideoComments().length, \" Comments\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 998,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 997,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comment-input\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comment-avatar\",\n                    children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || \"A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1003,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                      className: \"youtube-comment-input-field\",\n                      value: newComment,\n                      onChange: e => setNewComment(e.target.value),\n                      placeholder: \"Add a comment...\",\n                      rows: \"1\",\n                      style: {\n                        minHeight: '20px',\n                        resize: 'none',\n                        overflow: 'hidden'\n                      },\n                      onInput: e => {\n                        e.target.style.height = 'auto';\n                        e.target.style.height = e.target.scrollHeight + 'px';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1007,\n                      columnNumber: 29\n                    }, this), newComment.trim() && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-comment-actions\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"youtube-comment-btn cancel\",\n                        onClick: () => setNewComment(''),\n                        children: \"Cancel\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1025,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"youtube-comment-btn submit\",\n                        onClick: handleAddComment,\n                        disabled: !newComment.trim(),\n                        children: \"Comment\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1031,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1024,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1006,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1002,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comments-list\",\n                  children: getCurrentVideoComments().length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      textAlign: 'center',\n                      padding: '40px 0',\n                      color: '#606060'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '48px',\n                        marginBottom: '16px'\n                      },\n                      children: \"\\uD83D\\uDCAC\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1048,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"No comments yet. Be the first to share your thoughts!\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1049,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1047,\n                    columnNumber: 29\n                  }, this) : getCurrentVideoComments().map(comment => {\n                    var _comment$author, _comment$author$charA, _comment$likedBy, _comment$likedBy2;\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-comment\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment-avatar\",\n                        children: comment.avatar || ((_comment$author = comment.author) === null || _comment$author === void 0 ? void 0 : (_comment$author$charA = _comment$author.charAt(0)) === null || _comment$author$charA === void 0 ? void 0 : _comment$author$charA.toUpperCase()) || \"A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1054,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment-content\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-header\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"youtube-comment-author\",\n                            children: comment.author\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1059,\n                            columnNumber: 35\n                          }, this), (comment.userRole === 'admin' || comment.isAdmin) && /*#__PURE__*/_jsxDEV(MdVerified, {\n                            style: {\n                              color: '#1d9bf0',\n                              fontSize: '12px',\n                              marginLeft: '4px'\n                            },\n                            title: \"Verified Admin\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1061,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"youtube-comment-time\",\n                            children: formatTimeAgo(comment.createdAt || comment.timestamp)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1063,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1058,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-text\",\n                          children: comment.text\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1067,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-actions\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => handleLikeComment(comment._id || comment.id),\n                            className: `youtube-comment-action ${(_comment$likedBy = comment.likedBy) !== null && _comment$likedBy !== void 0 && _comment$likedBy.includes(user === null || user === void 0 ? void 0 : user._id) ? 'liked' : ''}`,\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: (_comment$likedBy2 = comment.likedBy) !== null && _comment$likedBy2 !== void 0 && _comment$likedBy2.includes(user === null || user === void 0 ? void 0 : user._id) ? '👍' : '👍'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1076,\n                              columnNumber: 37\n                            }, this), comment.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: comment.likes\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1077,\n                              columnNumber: 59\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1072,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => setReplyingTo(replyingTo === (comment._id || comment.id) ? null : comment._id || comment.id),\n                            className: \"youtube-comment-action\",\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"\\uD83D\\uDC4E\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1083,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1079,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => setReplyingTo(replyingTo === (comment._id || comment.id) ? null : comment._id || comment.id),\n                            className: \"youtube-comment-action\",\n                            children: \"Reply\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1085,\n                            columnNumber: 35\n                          }, this), comment.user === (user === null || user === void 0 ? void 0 : user._id) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"youtube-comment-action\",\n                              children: \"Edit\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1094,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"youtube-comment-action\",\n                              onClick: () => {\n                                if (window.confirm('Are you sure you want to delete this comment?')) {\n                                  handleDeleteComment(comment._id || comment.id);\n                                }\n                              },\n                              children: \"Delete\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1097,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1071,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1057,\n                        columnNumber: 31\n                      }, this)]\n                    }, comment._id || comment.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1053,\n                      columnNumber: 29\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1045,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 996,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 837,\n            columnNumber: 17\n          }, this);\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 831,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 828,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 601,\n    columnNumber: 5\n  }, this);\n}\n_s(VideoLessons, \"sLseqW6O5jdZfEEQY3uxq9LLVvI=\", false, function () {\n  return [useSelector, useLanguage, useDispatch];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "useRef", "motion", "AnimatePresence", "getStudyMaterial", "getVideoComments", "addVideoComment", "addCommentReply", "likeComment", "deleteVideoComment", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "message", "primarySubjects", "primaryKiswahiliSubjects", "secondarySubjects", "advanceSubjects", "useLanguage", "MdVerified", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "IconComponents", "FaPlayCircle", "style", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FaGraduationCap", "FaTimes", "FaExpand", "FaCompress", "TbVideo", "TbInfoCircle", "TbAlertTriangle", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbSearch", "TbX", "TbDownload", "VideoLessons", "_s", "user", "state", "t", "isKiswahili", "getClassName", "getSubjectName", "dispatch", "videos", "setVideos", "loading", "setLoading", "error", "setError", "selectedLevel", "setSelectedLevel", "level", "selectedClass", "setSelectedClass", "localStorage", "getItem", "class", "selectedSubject", "setSelectedSubject", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "currentVideoIndex", "setCurrentVideoIndex", "showVideoIndices", "setShowVideoIndices", "isVideoExpanded", "setIsVideoExpanded", "videoError", "setVideoError", "videoRef", "setVideoRef", "videoComments", "setVideoComments", "newComment", "setNewComment", "replyingTo", "setReplyingTo", "replyText", "setReplyText", "showComments", "setShowComments", "commentsExpanded", "setCommentsExpanded", "editingComment", "setEditingComment", "editCommentText", "setEditCommentText", "getCurrentVideoComments", "currentVideo", "filteredAndSortedVideos", "videoId", "id", "_id", "setCurrentVideoComments", "comments", "prev", "availableClasses", "availableSubjects", "fetchVideos", "_response$data", "filters", "className", "subject", "content", "response", "data", "success", "videoData", "loadAllVideoComments", "_response$data2", "console", "filtered", "filter", "video", "videoClass", "trim", "searchLower", "toLowerCase", "_video$title", "_video$subject", "_video$topic", "title", "includes", "topic", "sorted", "sort", "a", "b", "Date", "createdAt", "localeCompare", "log", "length", "handleShowVideo", "index", "loadVideoComments", "videoUrl", "signedUrl", "getSignedVideoUrl", "signedVideoUrl", "warn", "handleHideVideo", "pause", "toggleVideoExpansion", "fetch", "encodeURIComponent", "method", "headers", "ok", "Error", "status", "json", "getThumbnailUrl", "thumbnail", "videoID", "match", "handleClassChange", "value", "setItem", "handleSubjectChange", "handleSearchChange", "handleSortChange", "handleClearSearch", "handleRefresh", "handleClearAll", "videoList", "commentsMap", "handleAddComment", "Object", "keys", "commentData", "text", "comment", "author", "avatar", "replies", "likes", "<PERSON><PERSON><PERSON>", "currentComments", "handleAddReply", "commentId", "updatedComments", "map", "handleLikeComment", "isReply", "replyId", "handleDeleteComment", "handleEditComment", "handleSaveEditComment", "handleCancelEdit", "formatTimeAgo", "timestamp", "now", "time", "diffInSeconds", "Math", "floor", "toLocaleDateString", "char<PERSON>t", "toUpperCase", "slice", "onChange", "e", "target", "cls", "type", "placeholder", "onClick", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "duration", "subtitles", "sharedFromClass", "currentTarget", "_user$name", "_user$name$charAt", "display", "justifyContent", "marginBottom", "background", "border", "borderRadius", "width", "height", "alignItems", "cursor", "position", "ref", "controls", "playsInline", "preload", "poster", "backgroundColor", "objectFit", "onCanPlay", "onLoadStart", "crossOrigin", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "onLoad", "href", "rel", "name", "flex", "rows", "minHeight", "resize", "overflow", "onInput", "scrollHeight", "disabled", "textAlign", "padding", "color", "_comment$author", "_comment$author$charA", "_comment$likedBy", "_comment$likedBy2", "userRole", "isAdmin", "marginLeft", "window", "confirm", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo, useRef } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { getVideoComments, addVideoComment, addCommentReply, likeComment, deleteVideoComment } from \"../../../apicalls/videoComments\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { message } from \"antd\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\nimport { MdVerified } from 'react-icons/md';\n\n// Temporary fix: Use simple text/symbols instead of React Icons to avoid chunk loading issues\nconst IconComponents = {\n  FaPlayCircle: () => <span style={{fontSize: '24px'}}>▶️</span>,\n  FaGraduationCap: () => <span style={{fontSize: '24px'}}>🎓</span>,\n  FaTimes: () => <span style={{fontSize: '18px'}}>✕</span>,\n  FaExpand: () => <span style={{fontSize: '18px'}}>⛶</span>,\n  FaCompress: () => <span style={{fontSize: '18px'}}>⛶</span>,\n  TbVideo: () => <span style={{fontSize: '24px'}}>📹</span>,\n  TbInfoCircle: () => <span style={{fontSize: '16px'}}>ℹ️</span>,\n  TbAlertTriangle: () => <span style={{fontSize: '16px'}}>⚠️</span>,\n  TbFilter: () => <span style={{fontSize: '18px'}}>🔍</span>,\n  TbSortAscending: () => <span style={{fontSize: '18px'}}>↑</span>,\n  TbSearch: () => <span style={{fontSize: '18px'}}>🔍</span>,\n  TbX: () => <span style={{fontSize: '16px'}}>✕</span>,\n  TbDownload: () => <span style={{fontSize: '18px'}}>↻</span>\n};\n\n// Destructure for easy use\nconst {\n  FaPlayCircle,\n  FaGraduationCap,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  TbVideo,\n  TbFilter,\n  TbSortAscending,\n  TbSearch,\n  TbX,\n  TbDownload,\n  TbAlertTriangle,\n  TbInfoCircle\n} = IconComponents;\n\nfunction VideoLessons() {\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili, getClassName, getSubjectName } = useLanguage();\n  const dispatch = useDispatch();\n\n  // State management with localStorage persistence\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState(user?.level || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(() => {\n    // Restore from localStorage or use user's class as default\n    return localStorage.getItem('video-lessons-selected-class') || user?.class || \"all\";\n  });\n  const [selectedSubject, setSelectedSubject] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-selected-subject') || \"all\";\n  });\n  const [searchTerm, setSearchTerm] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-search-term') || \"\";\n  });\n  const [sortBy, setSortBy] = useState(() => {\n    // Restore from localStorage\n    return localStorage.getItem('video-lessons-sort-by') || \"newest\";\n  });\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state - store comments per video\n  const [videoComments, setVideoComments] = useState({});\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n  const [showComments, setShowComments] = useState(true);\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [editingComment, setEditingComment] = useState(null);\n  const [editCommentText, setEditCommentText] = useState(\"\");\n\n  // Get comments for current video\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n    if (!currentVideo) return [];\n\n    // Try both id and _id fields\n    const videoId = currentVideo.id || currentVideo._id;\n    return videoComments[videoId] || [];\n  };\n\n  // Set comments for current video\n  const setCurrentVideoComments = (comments) => {\n    if (currentVideoIndex === null) return;\n    const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n    if (!currentVideo) return;\n\n    // Use the same videoId logic as getCurrentVideoComments\n    const videoId = currentVideo.id || currentVideo._id;\n    setVideoComments(prev => ({\n      ...prev,\n      [videoId]: comments\n    }));\n  };\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\" || selectedLevel === \"primary_kiswahili\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"primary_kiswahili\") return primaryKiswahiliSubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n\n      const filters = {\n        level: selectedLevel,\n        className: \"all\", // Get all classes for the level\n        subject: \"all\", // Get all subjects for the level\n        content: \"videos\"\n      };\n\n      const response = await getStudyMaterial(filters);\n\n      if (response?.data?.success) {\n        const videoData = response.data.data || [];\n        setVideos(videoData);\n\n        // Load comments for all videos\n        await loadAllVideoComments(videoData);\n      } else {\n        setError(response?.data?.message || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n\n\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        return videoClass === selectedClass;\n      });\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video =>\n        video.title?.toLowerCase().includes(searchLower) ||\n        video.subject?.toLowerCase().includes(searchLower) ||\n        video.topic?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n\n    console.log('✅ Final filtered videos:', sorted.length);\n    if (sorted.length > 0) {\n      console.log('📹 Sample filtered video:', sorted[0]);\n    }\n\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Load comments for this video if not already loaded\n    const videoId = video?.id || video?._id;\n    if (videoId && !videoComments[videoId]) {\n      loadVideoComments(videoId);\n    }\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    \n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    \n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Handle filter changes with localStorage persistence\n  const handleClassChange = (value) => {\n    setSelectedClass(value);\n    localStorage.setItem('video-lessons-selected-class', value);\n  };\n\n  const handleSubjectChange = (value) => {\n    setSelectedSubject(value);\n    localStorage.setItem('video-lessons-selected-subject', value);\n  };\n\n  const handleSearchChange = (value) => {\n    setSearchTerm(value);\n    localStorage.setItem('video-lessons-search-term', value);\n  };\n\n  const handleSortChange = (value) => {\n    setSortBy(value);\n    localStorage.setItem('video-lessons-sort-by', value);\n  };\n\n  useEffect(() => {\n    if (user?.level) {\n      setSelectedLevel(user.level);\n    }\n    // Only set user's class as default if no saved preference exists\n    if (user?.class && !localStorage.getItem('video-lessons-selected-class')) {\n      handleClassChange(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    handleSearchChange(\"\");\n  };\n\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n\n  const handleClearAll = () => {\n    handleSearchChange(\"\");\n    handleSubjectChange(\"all\");\n    handleClassChange(\"all\");\n    handleSortChange(\"newest\");\n    fetchVideos();\n  };\n\n  // Load comments for all videos\n  const loadAllVideoComments = async (videoList) => {\n    try {\n      console.log('📹 Loading comments for all videos:', videoList.length);\n      const commentsMap = {};\n\n      // Load comments for each video\n      for (const video of videoList) {\n        const videoId = video.id || video._id;\n        if (videoId) {\n          try {\n            const response = await getVideoComments(videoId);\n            if (response.success) {\n              commentsMap[videoId] = response.data.comments;\n              console.log(`📝 Loaded ${response.data.comments.length} comments for video ${videoId}`);\n            }\n          } catch (error) {\n            console.error(`Error loading comments for video ${videoId}:`, error);\n          }\n        }\n      }\n\n      setVideoComments(commentsMap);\n      console.log('✅ All video comments loaded:', commentsMap);\n    } catch (error) {\n      console.error(\"Error loading all video comments:\", error);\n    }\n  };\n\n  // Load comments for current video\n  const loadVideoComments = async (videoId) => {\n    try {\n      const response = await getVideoComments(videoId);\n      if (response.success) {\n        setVideoComments(prev => ({\n          ...prev,\n          [videoId]: response.data.comments\n        }));\n      }\n    } catch (error) {\n      console.error(\"Error loading comments:\", error);\n    }\n  };\n\n  // Comment functions\n  const handleAddComment = async () => {\n    if (newComment.trim()) {\n      const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n      if (!currentVideo) return;\n\n      try {\n        console.log('📹 Current video object:', currentVideo);\n        console.log('📹 Video keys:', Object.keys(currentVideo || {}));\n        console.log('📹 Video id field:', currentVideo?.id);\n        console.log('📹 Video _id field:', currentVideo?._id);\n\n        // Use _id if id doesn't exist\n        const videoId = currentVideo.id || currentVideo._id;\n\n        const commentData = {\n          videoId: videoId,\n          text: newComment.trim()\n        };\n\n        console.log('📝 Sending video comment:', commentData);\n        console.log('📝 Comment data keys:', Object.keys(commentData));\n        console.log('📝 videoId value:', videoId, '(type:', typeof videoId, ')');\n        console.log('📝 text value:', newComment.trim(), '(type:', typeof newComment.trim(), ')');\n\n        const response = await addVideoComment(commentData);\n\n        if (response.success) {\n          // Add comment to local state immediately for better UX\n          const comment = {\n            _id: response.data._id,\n            text: response.data.text,\n            author: response.data.author,\n            avatar: response.data.avatar,\n            createdAt: response.data.createdAt,\n            replies: [],\n            likes: 0,\n            likedBy: []\n          };\n          const currentComments = getCurrentVideoComments();\n          setCurrentVideoComments([comment, ...currentComments]);\n          setNewComment(\"\");\n          message.success(\"Comment added successfully!\");\n        } else {\n          message.error(response.message || \"Failed to add comment\");\n        }\n      } catch (error) {\n        console.error(\"Error adding comment:\", error);\n        message.error(\"Failed to add comment\");\n      }\n    }\n  };\n\n\n\n  const handleAddReply = async (commentId) => {\n    if (replyText.trim()) {\n      try {\n        const response = await addCommentReply(commentId, {\n          text: replyText.trim()\n        });\n\n        if (response.success) {\n          // Update local state with the new reply\n          const currentComments = getCurrentVideoComments();\n          const updatedComments = currentComments.map(comment =>\n            comment._id === commentId || comment.id === commentId\n              ? { ...comment, replies: response.data.replies }\n              : comment\n          );\n          setCurrentVideoComments(updatedComments);\n          setReplyText(\"\");\n          setReplyingTo(null);\n          message.success(\"Reply added successfully!\");\n        } else {\n          message.error(response.message || \"Failed to add reply\");\n        }\n      } catch (error) {\n        console.error(\"Error adding reply:\", error);\n        message.error(\"Failed to add reply\");\n      }\n    }\n  };\n\n  const handleLikeComment = async (commentId, isReply = false, replyId = null) => {\n    try {\n      const response = await likeComment(commentId, {\n        isReply,\n        replyId\n      });\n\n      if (response.success) {\n        // Update local state with the updated comment\n        const currentComments = getCurrentVideoComments();\n        const updatedComments = currentComments.map(comment =>\n          comment._id === commentId || comment.id === commentId\n            ? response.data\n            : comment\n        );\n        setCurrentVideoComments(updatedComments);\n      } else {\n        message.error(response.message || \"Failed to update like\");\n      }\n    } catch (error) {\n      console.error(\"Error updating like:\", error);\n      message.error(\"Failed to update like\");\n    }\n  };\n\n  const handleDeleteComment = async (commentId) => {\n    try {\n      const response = await deleteVideoComment(commentId);\n\n      if (response.success) {\n        // Remove comment from local state\n        const currentComments = getCurrentVideoComments();\n        const updatedComments = currentComments.filter(comment =>\n          comment._id !== commentId && comment.id !== commentId\n        );\n        setCurrentVideoComments(updatedComments);\n        message.success(\"Comment deleted successfully!\");\n      } else {\n        message.error(response.message || \"Failed to delete comment\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting comment:\", error);\n      message.error(\"Failed to delete comment\");\n    }\n  };\n\n  const handleEditComment = (comment) => {\n    setEditingComment(comment._id || comment.id);\n    setEditCommentText(comment.text);\n  };\n\n  const handleSaveEditComment = async () => {\n    if (!editCommentText.trim()) {\n      message.error(\"Comment cannot be empty\");\n      return;\n    }\n\n    try {\n      // TODO: Add API call to update comment\n      // const response = await updateVideoComment(editingComment, { text: editCommentText.trim() });\n\n      // For now, update local state\n      const currentComments = getCurrentVideoComments();\n      const updatedComments = currentComments.map(comment => {\n        if ((comment._id || comment.id) === editingComment) {\n          return { ...comment, text: editCommentText.trim() };\n        }\n        return comment;\n      });\n      setCurrentVideoComments(updatedComments);\n\n      setEditingComment(null);\n      setEditCommentText(\"\");\n      message.success(\"Comment updated successfully!\");\n    } catch (error) {\n      console.error(\"Error updating comment:\", error);\n      message.error(\"Failed to update comment\");\n    }\n  };\n\n  const handleCancelEdit = () => {\n    setEditingComment(null);\n    setEditCommentText(\"\");\n  };\n\n  const formatTimeAgo = (timestamp) => {\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return time.toLocaleDateString();\n  };\n\n  return (\n    <div className=\"video-lessons-container\">\n      {/* Enhanced Header with Level Display */}\n      <div className=\"video-lessons-header\">\n        <div className=\"header-content\">\n          <div className=\"header-main\">\n            <div className=\"header-icon\">\n              <TbVideo />\n            </div>\n            <div className=\"header-text\">\n              <h1>Video Lessons</h1>\n              <p>Watch educational videos to enhance your learning</p>\n            </div>\n          </div>\n\n          {/* Level and Class Display */}\n          <div className=\"level-display\">\n            <div className=\"current-level\">\n              <span className=\"level-label\">Level:</span>\n              <span className=\"level-value\">{selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</span>\n            </div>\n            <div className=\"current-class\">\n              <span className=\"class-label\">Your Class:</span>\n              <span className=\"class-value\">\n                {user?.level === 'primary' ? `Class ${user?.class || 'N/A'}` :\n                 user?.level === 'secondary' ? `Form ${user?.class || 'N/A'}` :\n                 user?.level === 'advance' ? `Form ${user?.class || 'N/A'}` :\n                 'Not Set'}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"video-lessons-content\">\n        {/* Enhanced Filters and Controls */}\n        <div className=\"video-controls\">\n          <div className=\"controls-row\">\n            {/* Class Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                {isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class'}\n              </label>\n              <select\n                value={selectedClass}\n                onChange={(e) => handleClassChange(e.target.value)}\n                className=\"control-select class-select\"\n              >\n                <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n                {availableClasses.map((cls) => (\n                  <option key={cls} value={cls}>\n                    {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                      (isKiswahili ? `Darasa la ${cls}` : `Class ${cls}`) :\n                      `Form ${cls}`}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Subject Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Subject\n              </label>\n              <select\n                value={selectedSubject}\n                onChange={(e) => handleSubjectChange(e.target.value)}\n                className=\"control-select subject-select\"\n              >\n                <option value=\"all\">All Subjects</option>\n                {availableSubjects.map((subject) => (\n                  <option key={subject} value={subject}>\n                    {subject}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbSortAscending />\n                Sort\n              </label>\n              <select\n                value={sortBy}\n                onChange={(e) => handleSortChange(e.target.value)}\n                className=\"control-select sort-select\"\n              >\n                <option value=\"newest\">Newest First</option>\n                <option value=\"oldest\">Oldest First</option>\n                <option value=\"title\">Title A-Z</option>\n                <option value=\"subject\">Subject A-Z</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Search Row */}\n          <div className=\"search-row\">\n            <div className=\"search-container\">\n              <TbSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search videos by title, subject, or topic...\"\n                value={searchTerm}\n                onChange={(e) => handleSearchChange(e.target.value)}\n                className=\"search-input\"\n              />\n              {searchTerm && (\n                <button onClick={handleClearSearch} className=\"clear-search-btn\">\n                  <TbX />\n                  Clear Search\n                </button>\n              )}\n            </div>\n\n            <button onClick={handleRefresh} className=\"refresh-btn\">\n              <TbDownload />\n              Refresh All\n            </button>\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                <div className=\"video-card-thumbnail\">\n                  <img\n                    src={getThumbnailUrl(video)}\n                    alt={video.title}\n                    className=\"thumbnail-image\"\n                    loading=\"lazy\"\n                    onError={(e) => {\n                      // Fallback logic for failed thumbnails\n                      if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                        // For YouTube videos, try different quality thumbnails\n                        let videoId = video.videoID;\n                        if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                        }\n\n                        const fallbacks = [\n                          `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                          '/api/placeholder/320/180'\n                        ];\n\n                        const currentSrc = e.target.src;\n                        const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n\n                        if (currentIndex < fallbacks.length - 1) {\n                          e.target.src = fallbacks[currentIndex + 1];\n                        }\n                      } else {\n                        e.target.src = '/api/placeholder/320/180';\n                      }\n                    }}\n                  />\n                  <div className=\"play-overlay\">\n                    <FaPlayCircle className=\"play-icon\" />\n                  </div>\n                  <div className=\"video-duration\">\n                    {video.duration || \"Video\"}\n                  </div>\n                  {video.subtitles && video.subtitles.length > 0 && (\n                    <div className=\"subtitle-badge\">\n                      <TbInfoCircle />\n                      CC\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"video-card-content\">\n                  <h3 className=\"video-title\">{video.title}</h3>\n                  <div className=\"video-meta\">\n                    <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                    <span className=\"video-class\">\n                      {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                        (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`) :\n                        `Form ${video.className || video.class}`}\n                    </span>\n                  </div>\n                  <div className=\"video-tags\">\n                    {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                    {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                      <span className=\"shared-tag\">\n                        {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}{selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                          (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`) :\n                          `Form ${video.sharedFromClass}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedVideos[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <div className=\"youtube-style-layout\">\n                  {/* Close button */}\n                  <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '16px' }}>\n                    <button\n                      className=\"control-btn close-btn\"\n                      onClick={handleHideVideo}\n                      title=\"Close Video\"\n                      style={{\n                        background: 'rgba(0,0,0,0.1)',\n                        border: 'none',\n                        borderRadius: '50%',\n                        width: '40px',\n                        height: '40px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        cursor: 'pointer'\n                      }}\n                    >\n                      <span style={{ fontSize: '18px' }}>✕</span>\n                    </button>\n                  </div>\n\n                  {/* Video Section */}\n                  <div className=\"youtube-video-section\">\n\n                    {/* YouTube-style Video Player */}\n                    <div className=\"youtube-video-player\">\n                    {video.videoUrl ? (\n                      <div style={{ position: 'relative', width: '100%', height: '100%' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            playsInline\n                            preload=\"none\"\n                            width=\"100%\"\n                            height=\"100%\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            }}\n                            loading=\"lazy\"\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadStart={() => {\n                              console.log('🎬 Video loading started');\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            Your browser does not support the video tag.\n                          </video>\n\n                          {/* Subtitle indicator */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div className=\"subtitle-indicator\">\n                              <TbInfoCircle className=\"subtitle-icon\" />\n                              <span>Subtitles available in {video.subtitles.length} language(s)</span>\n                            </div>\n                          )}\n\n                          {/* Video error display */}\n                          {videoError && (\n                            <div className=\"video-error-overlay\">\n                              <div className=\"error-content\">\n                                <TbAlertTriangle className=\"error-icon\" />\n                                <p>{videoError}</p>\n                                <button onClick={() => setVideoError(null)} className=\"dismiss-error-btn\">\n                                  Dismiss\n                                </button>\n                              </div>\n                            </div>\n                          )}\n                      </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        style={{ width: '100%', height: '100%', border: 'none' }}\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                    </div>\n\n                    {/* YouTube-style Video Info */}\n                    <div className=\"youtube-video-info\">\n                      <h1 className=\"youtube-video-title\">{video.title}</h1>\n                      <div className=\"youtube-video-meta\">\n                        <span>{video.subject}</span>\n                        <span>•</span>\n                        <span>Class {video.className}</span>\n                        {video.level && (\n                          <>\n                            <span>•</span>\n                            <span>{video.level}</span>\n                          </>\n                        )}\n                      </div>\n                      <div className=\"youtube-video-actions\">\n                        <button\n                          className={`youtube-action-btn ${commentsExpanded ? 'active' : ''}`}\n                          onClick={() => setCommentsExpanded(!commentsExpanded)}\n                        >\n                          <span>💬</span>\n                          <span>Comments</span>\n                        </button>\n                        <button className=\"youtube-action-btn\">\n                          <span>👍</span>\n                          <span>Like</span>\n                        </button>\n                        <button className=\"youtube-action-btn\">\n                          <span>📤</span>\n                          <span>Share</span>\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* YouTube-style Comments Section */}\n                    {commentsExpanded && (\n                      <div className=\"youtube-comments-section\">\n                        <div className=\"youtube-comments-header\">\n                          <span>{getCurrentVideoComments().length} Comments</span>\n                        </div>\n\n                        {/* Add Comment */}\n                        <div className=\"youtube-comment-input\">\n                          <div className=\"youtube-comment-avatar\">\n                            {user?.name?.charAt(0)?.toUpperCase() || \"A\"}\n                          </div>\n                          <div style={{ flex: 1 }}>\n                            <textarea\n                              className=\"youtube-comment-input-field\"\n                              value={newComment}\n                              onChange={(e) => setNewComment(e.target.value)}\n                              placeholder=\"Add a comment...\"\n                              rows=\"1\"\n                              style={{\n                                minHeight: '20px',\n                                resize: 'none',\n                                overflow: 'hidden'\n                              }}\n                              onInput={(e) => {\n                                e.target.style.height = 'auto';\n                                e.target.style.height = e.target.scrollHeight + 'px';\n                              }}\n                            />\n                            {newComment.trim() && (\n                              <div className=\"youtube-comment-actions\">\n                                <button\n                                  className=\"youtube-comment-btn cancel\"\n                                  onClick={() => setNewComment('')}\n                                >\n                                  Cancel\n                                </button>\n                                <button\n                                  className=\"youtube-comment-btn submit\"\n                                  onClick={handleAddComment}\n                                  disabled={!newComment.trim()}\n                                >\n                                  Comment\n                                </button>\n                              </div>\n                            )}\n                          </div>\n                        </div>\n\n\n                        {/* YouTube-style Comments List */}\n                        <div className=\"youtube-comments-list\">\n                          {getCurrentVideoComments().length === 0 ? (\n                            <div style={{ textAlign: 'center', padding: '40px 0', color: '#606060' }}>\n                              <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                              <p>No comments yet. Be the first to share your thoughts!</p>\n                            </div>\n                          ) : (\n                            getCurrentVideoComments().map((comment) => (\n                            <div key={comment._id || comment.id} className=\"youtube-comment\">\n                              <div className=\"youtube-comment-avatar\">\n                                {comment.avatar || comment.author?.charAt(0)?.toUpperCase() || \"A\"}\n                              </div>\n                              <div className=\"youtube-comment-content\">\n                                <div className=\"youtube-comment-header\">\n                                  <span className=\"youtube-comment-author\">{comment.author}</span>\n                                  {(comment.userRole === 'admin' || comment.isAdmin) && (\n                                    <MdVerified style={{ color: '#1d9bf0', fontSize: '12px', marginLeft: '4px' }} title=\"Verified Admin\" />\n                                  )}\n                                  <span className=\"youtube-comment-time\">\n                                    {formatTimeAgo(comment.createdAt || comment.timestamp)}\n                                  </span>\n                                </div>\n                                <div className=\"youtube-comment-text\">\n                                  {comment.text}\n                                </div>\n\n                                <div className=\"youtube-comment-actions\">\n                                  <button\n                                    onClick={() => handleLikeComment(comment._id || comment.id)}\n                                    className={`youtube-comment-action ${comment.likedBy?.includes(user?._id) ? 'liked' : ''}`}\n                                  >\n                                    <span>{comment.likedBy?.includes(user?._id) ? '👍' : '👍'}</span>\n                                    {comment.likes > 0 && <span>{comment.likes}</span>}\n                                  </button>\n                                  <button\n                                    onClick={() => setReplyingTo(replyingTo === (comment._id || comment.id) ? null : (comment._id || comment.id))}\n                                    className=\"youtube-comment-action\"\n                                  >\n                                    <span>👎</span>\n                                  </button>\n                                  <button\n                                    onClick={() => setReplyingTo(replyingTo === (comment._id || comment.id) ? null : (comment._id || comment.id))}\n                                    className=\"youtube-comment-action\"\n                                  >\n                                    Reply\n                                  </button>\n                                  {/* Edit/Delete for comment author */}\n                                  {comment.user === user?._id && (\n                                    <>\n                                      <button className=\"youtube-comment-action\">\n                                        Edit\n                                      </button>\n                                      <button\n                                        className=\"youtube-comment-action\"\n                                        onClick={() => {\n                                          if (window.confirm('Are you sure you want to delete this comment?')) {\n                                            handleDeleteComment(comment._id || comment.id);\n                                          }\n                                        }}\n                                      >\n                                        Delete\n                                      </button>\n                                    </>\n                                  )}\n                                </div>\n                              </div>\n                            </div>\n                            ))\n                          )}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAChF,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,WAAW,EAAEC,kBAAkB,QAAQ,iCAAiC;AACrI,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,eAAe,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAC1H,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,UAAU,QAAQ,gBAAgB;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,YAAY,EAAEA,CAAA,kBAAMJ,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9DC,eAAe,EAAEA,CAAA,kBAAMZ,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACjEE,OAAO,EAAEA,CAAA,kBAAMb,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACxDG,QAAQ,EAAEA,CAAA,kBAAMd,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzDI,UAAU,EAAEA,CAAA,kBAAMf,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC3DK,OAAO,EAAEA,CAAA,kBAAMhB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzDM,YAAY,EAAEA,CAAA,kBAAMjB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9DO,eAAe,EAAEA,CAAA,kBAAMlB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACjEQ,QAAQ,EAAEA,CAAA,kBAAMnB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC1DS,eAAe,EAAEA,CAAA,kBAAMpB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAChEU,QAAQ,EAAEA,CAAA,kBAAMrB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC1DW,GAAG,EAAEA,CAAA,kBAAMtB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACpDY,UAAU,EAAEA,CAAA,kBAAMvB,OAAA;IAAMK,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM;AAC5D,CAAC;;AAED;AACA,MAAM;EACJP,YAAY;EACZQ,eAAe;EACfC,OAAO;EACPC,QAAQ;EACRC,UAAU;EACVC,OAAO;EACPG,QAAQ;EACRC,eAAe;EACfC,QAAQ;EACRC,GAAG;EACHC,UAAU;EACVL,eAAe;EACfD;AACF,CAAC,GAAGd,cAAc;AAElB,SAASqB,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC,WAAW;IAAEC,YAAY;IAAEC;EAAe,CAAC,GAAGlC,WAAW,CAAC,CAAC;EACtE,MAAMmC,QAAQ,GAAG5C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC4D,OAAO,EAAEC,UAAU,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8D,KAAK,EAAEC,QAAQ,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,CAAAmD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,KAAI,SAAS,CAAC;EAC5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpE,QAAQ,CAAC,MAAM;IACvD;IACA,OAAOqE,YAAY,CAACC,OAAO,CAAC,8BAA8B,CAAC,KAAInB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,KAAK,KAAI,KAAK;EACrF,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzE,QAAQ,CAAC,MAAM;IAC3D;IACA,OAAOqE,YAAY,CAACC,OAAO,CAAC,gCAAgC,CAAC,IAAI,KAAK;EACxE,CAAC,CAAC;EACF,MAAM,CAACI,UAAU,EAAEC,aAAa,CAAC,GAAG3E,QAAQ,CAAC,MAAM;IACjD;IACA,OAAOqE,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,IAAI,EAAE;EAChE,CAAC,CAAC;EACF,MAAM,CAACM,MAAM,EAAEC,SAAS,CAAC,GAAG7E,QAAQ,CAAC,MAAM;IACzC;IACA,OAAOqE,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,IAAI,QAAQ;EAClE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACQ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkF,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoF,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACsF,QAAQ,EAAEC,WAAW,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM,CAACwF,aAAa,EAAEC,gBAAgB,CAAC,GAAGzF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC0F,UAAU,EAAEC,aAAa,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4F,UAAU,EAAEC,aAAa,CAAC,GAAG7F,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC8F,SAAS,EAAEC,YAAY,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgG,YAAY,EAAEC,eAAe,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoG,cAAc,EAAEC,iBAAiB,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsG,eAAe,EAAEC,kBAAkB,CAAC,GAAGvG,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAMwG,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI1B,iBAAiB,KAAK,IAAI,EAAE,OAAO,EAAE;IACzC,MAAM2B,YAAY,GAAGC,uBAAuB,CAAC5B,iBAAiB,CAAC;IAC/D,IAAI,CAAC2B,YAAY,EAAE,OAAO,EAAE;;IAE5B;IACA,MAAME,OAAO,GAAGF,YAAY,CAACG,EAAE,IAAIH,YAAY,CAACI,GAAG;IACnD,OAAOrB,aAAa,CAACmB,OAAO,CAAC,IAAI,EAAE;EACrC,CAAC;;EAED;EACA,MAAMG,uBAAuB,GAAIC,QAAQ,IAAK;IAC5C,IAAIjC,iBAAiB,KAAK,IAAI,EAAE;IAChC,MAAM2B,YAAY,GAAGC,uBAAuB,CAAC5B,iBAAiB,CAAC;IAC/D,IAAI,CAAC2B,YAAY,EAAE;;IAEnB;IACA,MAAME,OAAO,GAAGF,YAAY,CAACG,EAAE,IAAIH,YAAY,CAACI,GAAG;IACnDpB,gBAAgB,CAACuB,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACL,OAAO,GAAGI;IACb,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAG9G,OAAO,CAAC,MAAM;IACrC,IAAI6D,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACpH,IAAIA,aAAa,KAAK,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC9D,IAAIA,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAClD,OAAO,EAAE;EACX,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMkD,iBAAiB,GAAG/G,OAAO,CAAC,MAAM;IACtC,IAAI6D,aAAa,KAAK,SAAS,EAAE,OAAO9C,eAAe;IACvD,IAAI8C,aAAa,KAAK,mBAAmB,EAAE,OAAO7C,wBAAwB;IAC1E,IAAI6C,aAAa,KAAK,WAAW,EAAE,OAAO5C,iBAAiB;IAC3D,IAAI4C,aAAa,KAAK,SAAS,EAAE,OAAO3C,eAAe;IACvD,OAAO,EAAE;EACX,CAAC,EAAE,CAAC2C,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMmD,WAAW,GAAGjH,WAAW,CAAC,YAAY;IAC1C,IAAI;MAAA,IAAAkH,cAAA;MACFvD,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdN,QAAQ,CAACzC,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMqG,OAAO,GAAG;QACdnD,KAAK,EAAEF,aAAa;QACpBsD,SAAS,EAAE,KAAK;QAAE;QAClBC,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;MACX,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMlH,gBAAgB,CAAC8G,OAAO,CAAC;MAEhD,IAAII,QAAQ,aAARA,QAAQ,gBAAAL,cAAA,GAARK,QAAQ,CAAEC,IAAI,cAAAN,cAAA,eAAdA,cAAA,CAAgBO,OAAO,EAAE;QAC3B,MAAMC,SAAS,GAAGH,QAAQ,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE;QAC1C/D,SAAS,CAACiE,SAAS,CAAC;;QAEpB;QACA,MAAMC,oBAAoB,CAACD,SAAS,CAAC;MACvC,CAAC,MAAM;QAAA,IAAAE,eAAA;QACL/D,QAAQ,CAAC,CAAA0D,QAAQ,aAARA,QAAQ,wBAAAK,eAAA,GAARL,QAAQ,CAAEC,IAAI,cAAAI,eAAA,uBAAdA,eAAA,CAAgB7G,OAAO,KAAI,wBAAwB,CAAC;QAC7D0C,SAAS,CAAC,EAAE,CAAC;MACf;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdiE,OAAO,CAACjE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,0CAA0C,CAAC;MACpDJ,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;MACjBJ,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACiD,aAAa,EAAEP,QAAQ,CAAC,CAAC;;EAE7B;EACA,MAAMiD,uBAAuB,GAAGvG,OAAO,CAAC,MAAM;IAG5C,IAAI6H,QAAQ,GAAGtE,MAAM;;IAErB;IACAsE,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAChE,KAAK,KAAKF,aAAa,CAAC;;IAElE;IACA,IAAIG,aAAa,KAAK,KAAK,EAAE;MAC3B6D,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAI;QAClC;QACA,MAAMC,UAAU,GAAGD,KAAK,CAACZ,SAAS,IAAIY,KAAK,CAAC3D,KAAK;QACjD,OAAO4D,UAAU,KAAKhE,aAAa;MACrC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIK,eAAe,KAAK,KAAK,EAAE;MAC7BwD,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACX,OAAO,KAAK/C,eAAe,CAAC;IACxE;;IAEA;IACA,IAAIE,UAAU,CAAC0D,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,WAAW,GAAG3D,UAAU,CAAC4D,WAAW,CAAC,CAAC;MAC5CN,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK;QAAA,IAAAK,YAAA,EAAAC,cAAA,EAAAC,YAAA;QAAA,OAC9B,EAAAF,YAAA,GAAAL,KAAK,CAACQ,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaD,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAG,cAAA,GAChDN,KAAK,CAACX,OAAO,cAAAiB,cAAA,uBAAbA,cAAA,CAAeF,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAI,YAAA,GAClDP,KAAK,CAACU,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaH,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC;MAAA,CAClD,CAAC;IACH;;IAEA;IACA,MAAMQ,MAAM,GAAG,CAAC,GAAGb,QAAQ,CAAC,CAACc,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C,QAAQpE,MAAM;QACZ,KAAK,QAAQ;UACX,OAAO,IAAIqE,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,QAAQ;UACX,OAAO,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,OAAO;UACV,OAAO,CAACH,CAAC,CAACL,KAAK,IAAI,EAAE,EAAES,aAAa,CAACH,CAAC,CAACN,KAAK,IAAI,EAAE,CAAC;QACrD,KAAK,SAAS;UACZ,OAAO,CAACK,CAAC,CAACxB,OAAO,IAAI,EAAE,EAAE4B,aAAa,CAACH,CAAC,CAACzB,OAAO,IAAI,EAAE,CAAC;QACzD;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;IAEFQ,OAAO,CAACqB,GAAG,CAAC,0BAA0B,EAAEP,MAAM,CAACQ,MAAM,CAAC;IACtD,IAAIR,MAAM,CAACQ,MAAM,GAAG,CAAC,EAAE;MACrBtB,OAAO,CAACqB,GAAG,CAAC,2BAA2B,EAAEP,MAAM,CAAC,CAAC,CAAC,CAAC;IACrD;IAEA,OAAOA,MAAM;EACf,CAAC,EAAE,CAACnF,MAAM,EAAEgB,UAAU,EAAEE,MAAM,EAAEZ,aAAa,EAAEG,aAAa,EAAEK,eAAe,CAAC,CAAC;;EAE/E;EACA,MAAM8E,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMrB,KAAK,GAAGxB,uBAAuB,CAAC6C,KAAK,CAAC;IAE5CxE,oBAAoB,CAACwE,KAAK,CAAC;IAC3BtE,mBAAmB,CAAC,CAACsE,KAAK,CAAC,CAAC;IAC5BpE,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAMsB,OAAO,GAAG,CAAAuB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEtB,EAAE,MAAIsB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAErB,GAAG;IACvC,IAAIF,OAAO,IAAI,CAACnB,aAAa,CAACmB,OAAO,CAAC,EAAE;MACtC6C,iBAAiB,CAAC7C,OAAO,CAAC;IAC5B;;IAEA;IACA,IAAIuB,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEuB,QAAQ,KAAKvB,KAAK,CAACuB,QAAQ,CAACd,QAAQ,CAAC,eAAe,CAAC,IAAIT,KAAK,CAACuB,QAAQ,CAACd,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF,MAAMe,SAAS,GAAG,MAAMC,iBAAiB,CAACzB,KAAK,CAACuB,QAAQ,CAAC;QACzDvB,KAAK,CAAC0B,cAAc,GAAGF,SAAS;MAClC,CAAC,CAAC,OAAO5F,KAAK,EAAE;QACdiE,OAAO,CAAC8B,IAAI,CAAC,8CAA8C,CAAC;QAC5D3B,KAAK,CAAC0B,cAAc,GAAG1B,KAAK,CAACuB,QAAQ;MACvC;IACF;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5B7E,mBAAmB,CAAC,EAAE,CAAC;IACvBF,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACyE,KAAK,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC7E,kBAAkB,CAAC,CAACD,eAAe,CAAC;EACtC,CAAC;;EAED;EACA,MAAMyE,iBAAiB,GAAG,MAAOF,QAAQ,IAAK;IAC5C,IAAI,CAACA,QAAQ,EAAE,OAAOA,QAAQ;;IAE9B;IACA,IAAIA,QAAQ,CAACd,QAAQ,CAAC,eAAe,CAAC,IAAIc,QAAQ,CAACd,QAAQ,CAAC,KAAK,CAAC,EAAE;MAClE,IAAI;QACF,MAAMlB,QAAQ,GAAG,MAAMwC,KAAK,CAAE,6DAA4DC,kBAAkB,CAACT,QAAQ,CAAE,EAAC,EAAE;UACxHU,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAAC3C,QAAQ,CAAC4C,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsB7C,QAAQ,CAAC8C,MAAO,EAAC,CAAC;QAC3D;QAEA,MAAM7C,IAAI,GAAG,MAAMD,QAAQ,CAAC+C,IAAI,CAAC,CAAC;QAElC,IAAI9C,IAAI,CAACC,OAAO,IAAID,IAAI,CAACgC,SAAS,EAAE;UAClC3B,OAAO,CAACqB,GAAG,CAAC,+BAA+B,CAAC;UAC5C,OAAO1B,IAAI,CAACgC,SAAS;QACvB,CAAC,MAAM;UACL3B,OAAO,CAAC8B,IAAI,CAAC,+CAA+C,EAAEnC,IAAI,CAAC;UACnE,OAAO+B,QAAQ;QACjB;MACF,CAAC,CAAC,OAAO3F,KAAK,EAAE;QACdiE,OAAO,CAACjE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,OAAO2F,QAAQ;MACjB;IACF;IAEA,OAAOA,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMgB,eAAe,GAAIvC,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACwC,SAAS,EAAE;MACnB,OAAOxC,KAAK,CAACwC,SAAS;IACxB;IAEA,IAAIxC,KAAK,CAACyC,OAAO,IAAI,CAACzC,KAAK,CAACyC,OAAO,CAAChC,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC7D,IAAIhC,OAAO,GAAGuB,KAAK,CAACyC,OAAO;MAC3B,IAAIhE,OAAO,CAACgC,QAAQ,CAAC,aAAa,CAAC,IAAIhC,OAAO,CAACgC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnE,MAAMiC,KAAK,GAAGjE,OAAO,CAACiE,KAAK,CAAC,oDAAoD,CAAC;QACjFjE,OAAO,GAAGiE,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGjE,OAAO;MACtC;MACA,OAAQ,8BAA6BA,OAAQ,oBAAmB;IAClE;IAEA,OAAO,0BAA0B;EACnC,CAAC;;EAED;EACA1G,SAAS,CAAC,MAAM;IACdkH,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM0D,iBAAiB,GAAIC,KAAK,IAAK;IACnC1G,gBAAgB,CAAC0G,KAAK,CAAC;IACvBzG,YAAY,CAAC0G,OAAO,CAAC,8BAA8B,EAAED,KAAK,CAAC;EAC7D,CAAC;EAED,MAAME,mBAAmB,GAAIF,KAAK,IAAK;IACrCrG,kBAAkB,CAACqG,KAAK,CAAC;IACzBzG,YAAY,CAAC0G,OAAO,CAAC,gCAAgC,EAAED,KAAK,CAAC;EAC/D,CAAC;EAED,MAAMG,kBAAkB,GAAIH,KAAK,IAAK;IACpCnG,aAAa,CAACmG,KAAK,CAAC;IACpBzG,YAAY,CAAC0G,OAAO,CAAC,2BAA2B,EAAED,KAAK,CAAC;EAC1D,CAAC;EAED,MAAMI,gBAAgB,GAAIJ,KAAK,IAAK;IAClCjG,SAAS,CAACiG,KAAK,CAAC;IAChBzG,YAAY,CAAC0G,OAAO,CAAC,uBAAuB,EAAED,KAAK,CAAC;EACtD,CAAC;EAED7K,SAAS,CAAC,MAAM;IACd,IAAIkD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEe,KAAK,EAAE;MACfD,gBAAgB,CAACd,IAAI,CAACe,KAAK,CAAC;IAC9B;IACA;IACA,IAAIf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoB,KAAK,IAAI,CAACF,YAAY,CAACC,OAAO,CAAC,8BAA8B,CAAC,EAAE;MACxEuG,iBAAiB,CAAC1H,IAAI,CAACoB,KAAK,CAAC;IAC/B;EACF,CAAC,EAAE,CAACpB,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMgI,iBAAiB,GAAGA,CAAA,KAAM;IAC9BF,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;EAED,MAAMG,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACAjE,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMkE,cAAc,GAAGA,CAAA,KAAM;IAC3BJ,kBAAkB,CAAC,EAAE,CAAC;IACtBD,mBAAmB,CAAC,KAAK,CAAC;IAC1BH,iBAAiB,CAAC,KAAK,CAAC;IACxBK,gBAAgB,CAAC,QAAQ,CAAC;IAC1B/D,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMU,oBAAoB,GAAG,MAAOyD,SAAS,IAAK;IAChD,IAAI;MACFvD,OAAO,CAACqB,GAAG,CAAC,qCAAqC,EAAEkC,SAAS,CAACjC,MAAM,CAAC;MACpE,MAAMkC,WAAW,GAAG,CAAC,CAAC;;MAEtB;MACA,KAAK,MAAMrD,KAAK,IAAIoD,SAAS,EAAE;QAC7B,MAAM3E,OAAO,GAAGuB,KAAK,CAACtB,EAAE,IAAIsB,KAAK,CAACrB,GAAG;QACrC,IAAIF,OAAO,EAAE;UACX,IAAI;YACF,MAAMc,QAAQ,GAAG,MAAMjH,gBAAgB,CAACmG,OAAO,CAAC;YAChD,IAAIc,QAAQ,CAACE,OAAO,EAAE;cACpB4D,WAAW,CAAC5E,OAAO,CAAC,GAAGc,QAAQ,CAACC,IAAI,CAACX,QAAQ;cAC7CgB,OAAO,CAACqB,GAAG,CAAE,aAAY3B,QAAQ,CAACC,IAAI,CAACX,QAAQ,CAACsC,MAAO,uBAAsB1C,OAAQ,EAAC,CAAC;YACzF;UACF,CAAC,CAAC,OAAO7C,KAAK,EAAE;YACdiE,OAAO,CAACjE,KAAK,CAAE,oCAAmC6C,OAAQ,GAAE,EAAE7C,KAAK,CAAC;UACtE;QACF;MACF;MAEA2B,gBAAgB,CAAC8F,WAAW,CAAC;MAC7BxD,OAAO,CAACqB,GAAG,CAAC,8BAA8B,EAAEmC,WAAW,CAAC;IAC1D,CAAC,CAAC,OAAOzH,KAAK,EAAE;MACdiE,OAAO,CAACjE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC;;EAED;EACA,MAAM0F,iBAAiB,GAAG,MAAO7C,OAAO,IAAK;IAC3C,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMjH,gBAAgB,CAACmG,OAAO,CAAC;MAChD,IAAIc,QAAQ,CAACE,OAAO,EAAE;QACpBlC,gBAAgB,CAACuB,IAAI,KAAK;UACxB,GAAGA,IAAI;UACP,CAACL,OAAO,GAAGc,QAAQ,CAACC,IAAI,CAACX;QAC3B,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOjD,KAAK,EAAE;MACdiE,OAAO,CAACjE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAM0H,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI9F,UAAU,CAAC0C,IAAI,CAAC,CAAC,EAAE;MACrB,MAAM3B,YAAY,GAAGC,uBAAuB,CAAC5B,iBAAiB,CAAC;MAC/D,IAAI,CAAC2B,YAAY,EAAE;MAEnB,IAAI;QACFsB,OAAO,CAACqB,GAAG,CAAC,0BAA0B,EAAE3C,YAAY,CAAC;QACrDsB,OAAO,CAACqB,GAAG,CAAC,gBAAgB,EAAEqC,MAAM,CAACC,IAAI,CAACjF,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9DsB,OAAO,CAACqB,GAAG,CAAC,oBAAoB,EAAE3C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,EAAE,CAAC;QACnDmB,OAAO,CAACqB,GAAG,CAAC,qBAAqB,EAAE3C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEI,GAAG,CAAC;;QAErD;QACA,MAAMF,OAAO,GAAGF,YAAY,CAACG,EAAE,IAAIH,YAAY,CAACI,GAAG;QAEnD,MAAM8E,WAAW,GAAG;UAClBhF,OAAO,EAAEA,OAAO;UAChBiF,IAAI,EAAElG,UAAU,CAAC0C,IAAI,CAAC;QACxB,CAAC;QAEDL,OAAO,CAACqB,GAAG,CAAC,2BAA2B,EAAEuC,WAAW,CAAC;QACrD5D,OAAO,CAACqB,GAAG,CAAC,uBAAuB,EAAEqC,MAAM,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC;QAC9D5D,OAAO,CAACqB,GAAG,CAAC,mBAAmB,EAAEzC,OAAO,EAAE,QAAQ,EAAE,OAAOA,OAAO,EAAE,GAAG,CAAC;QACxEoB,OAAO,CAACqB,GAAG,CAAC,gBAAgB,EAAE1D,UAAU,CAAC0C,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC;QAEzF,MAAMX,QAAQ,GAAG,MAAMhH,eAAe,CAACkL,WAAW,CAAC;QAEnD,IAAIlE,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMkE,OAAO,GAAG;YACdhF,GAAG,EAAEY,QAAQ,CAACC,IAAI,CAACb,GAAG;YACtB+E,IAAI,EAAEnE,QAAQ,CAACC,IAAI,CAACkE,IAAI;YACxBE,MAAM,EAAErE,QAAQ,CAACC,IAAI,CAACoE,MAAM;YAC5BC,MAAM,EAAEtE,QAAQ,CAACC,IAAI,CAACqE,MAAM;YAC5B7C,SAAS,EAAEzB,QAAQ,CAACC,IAAI,CAACwB,SAAS;YAClC8C,OAAO,EAAE,EAAE;YACXC,KAAK,EAAE,CAAC;YACRC,OAAO,EAAE;UACX,CAAC;UACD,MAAMC,eAAe,GAAG3F,uBAAuB,CAAC,CAAC;UACjDM,uBAAuB,CAAC,CAAC+E,OAAO,EAAE,GAAGM,eAAe,CAAC,CAAC;UACtDxG,aAAa,CAAC,EAAE,CAAC;UACjB1E,OAAO,CAAC0G,OAAO,CAAC,6BAA6B,CAAC;QAChD,CAAC,MAAM;UACL1G,OAAO,CAAC6C,KAAK,CAAC2D,QAAQ,CAACxG,OAAO,IAAI,uBAAuB,CAAC;QAC5D;MACF,CAAC,CAAC,OAAO6C,KAAK,EAAE;QACdiE,OAAO,CAACjE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C7C,OAAO,CAAC6C,KAAK,CAAC,uBAAuB,CAAC;MACxC;IACF;EACF,CAAC;EAID,MAAMsI,cAAc,GAAG,MAAOC,SAAS,IAAK;IAC1C,IAAIvG,SAAS,CAACsC,IAAI,CAAC,CAAC,EAAE;MACpB,IAAI;QACF,MAAMX,QAAQ,GAAG,MAAM/G,eAAe,CAAC2L,SAAS,EAAE;UAChDT,IAAI,EAAE9F,SAAS,CAACsC,IAAI,CAAC;QACvB,CAAC,CAAC;QAEF,IAAIX,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMwE,eAAe,GAAG3F,uBAAuB,CAAC,CAAC;UACjD,MAAM8F,eAAe,GAAGH,eAAe,CAACI,GAAG,CAACV,OAAO,IACjDA,OAAO,CAAChF,GAAG,KAAKwF,SAAS,IAAIR,OAAO,CAACjF,EAAE,KAAKyF,SAAS,GACjD;YAAE,GAAGR,OAAO;YAAEG,OAAO,EAAEvE,QAAQ,CAACC,IAAI,CAACsE;UAAQ,CAAC,GAC9CH,OACN,CAAC;UACD/E,uBAAuB,CAACwF,eAAe,CAAC;UACxCvG,YAAY,CAAC,EAAE,CAAC;UAChBF,aAAa,CAAC,IAAI,CAAC;UACnB5E,OAAO,CAAC0G,OAAO,CAAC,2BAA2B,CAAC;QAC9C,CAAC,MAAM;UACL1G,OAAO,CAAC6C,KAAK,CAAC2D,QAAQ,CAACxG,OAAO,IAAI,qBAAqB,CAAC;QAC1D;MACF,CAAC,CAAC,OAAO6C,KAAK,EAAE;QACdiE,OAAO,CAACjE,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C7C,OAAO,CAAC6C,KAAK,CAAC,qBAAqB,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAM0I,iBAAiB,GAAG,MAAAA,CAAOH,SAAS,EAAEI,OAAO,GAAG,KAAK,EAAEC,OAAO,GAAG,IAAI,KAAK;IAC9E,IAAI;MACF,MAAMjF,QAAQ,GAAG,MAAM9G,WAAW,CAAC0L,SAAS,EAAE;QAC5CI,OAAO;QACPC;MACF,CAAC,CAAC;MAEF,IAAIjF,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMwE,eAAe,GAAG3F,uBAAuB,CAAC,CAAC;QACjD,MAAM8F,eAAe,GAAGH,eAAe,CAACI,GAAG,CAACV,OAAO,IACjDA,OAAO,CAAChF,GAAG,KAAKwF,SAAS,IAAIR,OAAO,CAACjF,EAAE,KAAKyF,SAAS,GACjD5E,QAAQ,CAACC,IAAI,GACbmE,OACN,CAAC;QACD/E,uBAAuB,CAACwF,eAAe,CAAC;MAC1C,CAAC,MAAM;QACLrL,OAAO,CAAC6C,KAAK,CAAC2D,QAAQ,CAACxG,OAAO,IAAI,uBAAuB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACdiE,OAAO,CAACjE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C7C,OAAO,CAAC6C,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC;EAED,MAAM6I,mBAAmB,GAAG,MAAON,SAAS,IAAK;IAC/C,IAAI;MACF,MAAM5E,QAAQ,GAAG,MAAM7G,kBAAkB,CAACyL,SAAS,CAAC;MAEpD,IAAI5E,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMwE,eAAe,GAAG3F,uBAAuB,CAAC,CAAC;QACjD,MAAM8F,eAAe,GAAGH,eAAe,CAAClE,MAAM,CAAC4D,OAAO,IACpDA,OAAO,CAAChF,GAAG,KAAKwF,SAAS,IAAIR,OAAO,CAACjF,EAAE,KAAKyF,SAC9C,CAAC;QACDvF,uBAAuB,CAACwF,eAAe,CAAC;QACxCrL,OAAO,CAAC0G,OAAO,CAAC,+BAA+B,CAAC;MAClD,CAAC,MAAM;QACL1G,OAAO,CAAC6C,KAAK,CAAC2D,QAAQ,CAACxG,OAAO,IAAI,0BAA0B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACdiE,OAAO,CAACjE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C7C,OAAO,CAAC6C,KAAK,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;EAED,MAAM8I,iBAAiB,GAAIf,OAAO,IAAK;IACrCxF,iBAAiB,CAACwF,OAAO,CAAChF,GAAG,IAAIgF,OAAO,CAACjF,EAAE,CAAC;IAC5CL,kBAAkB,CAACsF,OAAO,CAACD,IAAI,CAAC;EAClC,CAAC;EAED,MAAMiB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACvG,eAAe,CAAC8B,IAAI,CAAC,CAAC,EAAE;MAC3BnH,OAAO,CAAC6C,KAAK,CAAC,yBAAyB,CAAC;MACxC;IACF;IAEA,IAAI;MACF;MACA;;MAEA;MACA,MAAMqI,eAAe,GAAG3F,uBAAuB,CAAC,CAAC;MACjD,MAAM8F,eAAe,GAAGH,eAAe,CAACI,GAAG,CAACV,OAAO,IAAI;QACrD,IAAI,CAACA,OAAO,CAAChF,GAAG,IAAIgF,OAAO,CAACjF,EAAE,MAAMR,cAAc,EAAE;UAClD,OAAO;YAAE,GAAGyF,OAAO;YAAED,IAAI,EAAEtF,eAAe,CAAC8B,IAAI,CAAC;UAAE,CAAC;QACrD;QACA,OAAOyD,OAAO;MAChB,CAAC,CAAC;MACF/E,uBAAuB,CAACwF,eAAe,CAAC;MAExCjG,iBAAiB,CAAC,IAAI,CAAC;MACvBE,kBAAkB,CAAC,EAAE,CAAC;MACtBtF,OAAO,CAAC0G,OAAO,CAAC,+BAA+B,CAAC;IAClD,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACdiE,OAAO,CAACjE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C7C,OAAO,CAAC6C,KAAK,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;EAED,MAAMgJ,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzG,iBAAiB,CAAC,IAAI,CAAC;IACvBE,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;EAED,MAAMwG,aAAa,GAAIC,SAAS,IAAK;IACnC,MAAMC,GAAG,GAAG,IAAIhE,IAAI,CAAC,CAAC;IACtB,MAAMiE,IAAI,GAAG,IAAIjE,IAAI,CAAC+D,SAAS,CAAC;IAChC,MAAMG,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,GAAG,GAAGC,IAAI,IAAI,IAAI,CAAC;IAErD,IAAIC,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAE,OAAM;IACzE,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAE,OAAM;IAC5E,IAAIA,aAAa,GAAG,MAAM,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAE,OAAM;IAC9E,OAAOD,IAAI,CAACI,kBAAkB,CAAC,CAAC;EAClC,CAAC;EAED,oBACE7L,OAAA;IAAK6F,SAAS,EAAC,yBAAyB;IAAAtF,QAAA,gBAEtCP,OAAA;MAAK6F,SAAS,EAAC,sBAAsB;MAAAtF,QAAA,eACnCP,OAAA;QAAK6F,SAAS,EAAC,gBAAgB;QAAAtF,QAAA,gBAC7BP,OAAA;UAAK6F,SAAS,EAAC,aAAa;UAAAtF,QAAA,gBAC1BP,OAAA;YAAK6F,SAAS,EAAC,aAAa;YAAAtF,QAAA,eAC1BP,OAAA,CAACgB,OAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNX,OAAA;YAAK6F,SAAS,EAAC,aAAa;YAAAtF,QAAA,gBAC1BP,OAAA;cAAAO,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBX,OAAA;cAAAO,QAAA,EAAG;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNX,OAAA;UAAK6F,SAAS,EAAC,eAAe;UAAAtF,QAAA,gBAC5BP,OAAA;YAAK6F,SAAS,EAAC,eAAe;YAAAtF,QAAA,gBAC5BP,OAAA;cAAM6F,SAAS,EAAC,aAAa;cAAAtF,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CX,OAAA;cAAM6F,SAAS,EAAC,aAAa;cAAAtF,QAAA,EAAEgC,aAAa,CAACuJ,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGxJ,aAAa,CAACyJ,KAAK,CAAC,CAAC;YAAC;cAAAxL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eACNX,OAAA;YAAK6F,SAAS,EAAC,eAAe;YAAAtF,QAAA,gBAC5BP,OAAA;cAAM6F,SAAS,EAAC,aAAa;cAAAtF,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDX,OAAA;cAAM6F,SAAS,EAAC,aAAa;cAAAtF,QAAA,EAC1B,CAAAmB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAK,SAAS,GAAI,SAAQ,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,KAAK,KAAI,KAAM,EAAC,GAC3D,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAK,WAAW,GAAI,QAAO,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,KAAK,KAAI,KAAM,EAAC,GAC5D,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAK,SAAS,GAAI,QAAO,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,KAAK,KAAI,KAAM,EAAC,GAC1D;YAAS;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENX,OAAA;MAAK6F,SAAS,EAAC,uBAAuB;MAAAtF,QAAA,gBAEpCP,OAAA;QAAK6F,SAAS,EAAC,gBAAgB;QAAAtF,QAAA,gBAC7BP,OAAA;UAAK6F,SAAS,EAAC,cAAc;UAAAtF,QAAA,gBAE3BP,OAAA;YAAK6F,SAAS,EAAC,eAAe;YAAAtF,QAAA,gBAC5BP,OAAA;cAAO6F,SAAS,EAAC,eAAe;cAAAtF,QAAA,gBAC9BP,OAAA,CAACmB,QAAQ;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACXkB,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACRX,OAAA;cACEqJ,KAAK,EAAE3G,aAAc;cACrBuJ,QAAQ,EAAGC,CAAC,IAAK9C,iBAAiB,CAAC8C,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;cACnDxD,SAAS,EAAC,6BAA6B;cAAAtF,QAAA,gBAEvCP,OAAA;gBAAQqJ,KAAK,EAAC,KAAK;gBAAA9I,QAAA,EAAEsB,WAAW,GAAG,eAAe,GAAG;cAAa;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,EAC3E6E,gBAAgB,CAACsF,GAAG,CAAEsB,GAAG,iBACxBpM,OAAA;gBAAkBqJ,KAAK,EAAE+C,GAAI;gBAAA7L,QAAA,EAC1BgC,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAClEV,WAAW,GAAI,aAAYuK,GAAI,EAAC,GAAI,SAAQA,GAAI,EAAC,GACjD,QAAOA,GAAI;cAAC,GAHJA,GAAG;gBAAA5L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIR,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNX,OAAA;YAAK6F,SAAS,EAAC,eAAe;YAAAtF,QAAA,gBAC5BP,OAAA;cAAO6F,SAAS,EAAC,eAAe;cAAAtF,QAAA,gBAC9BP,OAAA,CAACmB,QAAQ;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRX,OAAA;cACEqJ,KAAK,EAAEtG,eAAgB;cACvBkJ,QAAQ,EAAGC,CAAC,IAAK3C,mBAAmB,CAAC2C,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;cACrDxD,SAAS,EAAC,+BAA+B;cAAAtF,QAAA,gBAEzCP,OAAA;gBAAQqJ,KAAK,EAAC,KAAK;gBAAA9I,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxC8E,iBAAiB,CAACqF,GAAG,CAAEhF,OAAO,iBAC7B9F,OAAA;gBAAsBqJ,KAAK,EAAEvD,OAAQ;gBAAAvF,QAAA,EAClCuF;cAAO,GADGA,OAAO;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNX,OAAA;YAAK6F,SAAS,EAAC,eAAe;YAAAtF,QAAA,gBAC5BP,OAAA;cAAO6F,SAAS,EAAC,eAAe;cAAAtF,QAAA,gBAC9BP,OAAA,CAACoB,eAAe;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRX,OAAA;cACEqJ,KAAK,EAAElG,MAAO;cACd8I,QAAQ,EAAGC,CAAC,IAAKzC,gBAAgB,CAACyC,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;cAClDxD,SAAS,EAAC,4BAA4B;cAAAtF,QAAA,gBAEtCP,OAAA;gBAAQqJ,KAAK,EAAC,QAAQ;gBAAA9I,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CX,OAAA;gBAAQqJ,KAAK,EAAC,QAAQ;gBAAA9I,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CX,OAAA;gBAAQqJ,KAAK,EAAC,OAAO;gBAAA9I,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCX,OAAA;gBAAQqJ,KAAK,EAAC,SAAS;gBAAA9I,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNX,OAAA;UAAK6F,SAAS,EAAC,YAAY;UAAAtF,QAAA,gBACzBP,OAAA;YAAK6F,SAAS,EAAC,kBAAkB;YAAAtF,QAAA,gBAC/BP,OAAA,CAACqB,QAAQ;cAACwE,SAAS,EAAC;YAAa;cAAArF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpCX,OAAA;cACEqM,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,8CAA8C;cAC1DjD,KAAK,EAAEpG,UAAW;cAClBgJ,QAAQ,EAAGC,CAAC,IAAK1C,kBAAkB,CAAC0C,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;cACpDxD,SAAS,EAAC;YAAc;cAAArF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,EACDsC,UAAU,iBACTjD,OAAA;cAAQuM,OAAO,EAAE7C,iBAAkB;cAAC7D,SAAS,EAAC,kBAAkB;cAAAtF,QAAA,gBAC9DP,OAAA,CAACsB,GAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAET;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENX,OAAA;YAAQuM,OAAO,EAAE5C,aAAc;YAAC9D,SAAS,EAAC,aAAa;YAAAtF,QAAA,gBACrDP,OAAA,CAACuB,UAAU;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLwB,OAAO,gBACNnC,OAAA;QAAK6F,SAAS,EAAC,eAAe;QAAAtF,QAAA,gBAC5BP,OAAA;UAAK6F,SAAS,EAAC;QAAiB;UAAArF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCX,OAAA;UAAAO,QAAA,EAAIsB,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJ0B,KAAK,gBACPrC,OAAA;QAAK6F,SAAS,EAAC,aAAa;QAAAtF,QAAA,gBAC1BP,OAAA,CAACkB,eAAe;UAAC2E,SAAS,EAAC;QAAY;UAAArF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CX,OAAA;UAAAO,QAAA,EAAKsB,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7EX,OAAA;UAAAO,QAAA,EAAI8B;QAAK;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdX,OAAA;UAAQuM,OAAO,EAAE7G,WAAY;UAACG,SAAS,EAAC,WAAW;UAAAtF,QAAA,EAChDsB,WAAW,GAAG,aAAa,GAAG;QAAW;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJsE,uBAAuB,CAAC2C,MAAM,GAAG,CAAC,gBACpC5H,OAAA;QAAK6F,SAAS,EAAC,aAAa;QAAAtF,QAAA,EACzB0E,uBAAuB,CAAC6F,GAAG,CAAC,CAACrE,KAAK,EAAEqB,KAAK,kBACxC9H,OAAA;UAAiB6F,SAAS,EAAC,YAAY;UAAC0G,OAAO,EAAEA,CAAA,KAAM1E,eAAe,CAACC,KAAK,CAAE;UAAAvH,QAAA,gBAC5EP,OAAA;YAAK6F,SAAS,EAAC,sBAAsB;YAAAtF,QAAA,gBACnCP,OAAA;cACEwM,GAAG,EAAExD,eAAe,CAACvC,KAAK,CAAE;cAC5BgG,GAAG,EAAEhG,KAAK,CAACQ,KAAM;cACjBpB,SAAS,EAAC,iBAAiB;cAC3B1D,OAAO,EAAC,MAAM;cACduK,OAAO,EAAGR,CAAC,IAAK;gBACd;gBACA,IAAIzF,KAAK,CAACyC,OAAO,IAAI,CAACzC,KAAK,CAACyC,OAAO,CAAChC,QAAQ,CAAC,eAAe,CAAC,EAAE;kBAC7D;kBACA,IAAIhC,OAAO,GAAGuB,KAAK,CAACyC,OAAO;kBAC3B,IAAIhE,OAAO,CAACgC,QAAQ,CAAC,aAAa,CAAC,IAAIhC,OAAO,CAACgC,QAAQ,CAAC,UAAU,CAAC,EAAE;oBACnE,MAAMiC,KAAK,GAAGjE,OAAO,CAACiE,KAAK,CAAC,oDAAoD,CAAC;oBACjFjE,OAAO,GAAGiE,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGjE,OAAO;kBACtC;kBAEA,MAAMyH,SAAS,GAAG,CACf,8BAA6BzH,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;kBAED,MAAM0H,UAAU,GAAGV,CAAC,CAACC,MAAM,CAACK,GAAG;kBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACC,GAAG,IAAIH,UAAU,CAAC1F,QAAQ,CAAC6F,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;kBAE1F,IAAIJ,YAAY,GAAGF,SAAS,CAAC/E,MAAM,GAAG,CAAC,EAAE;oBACvCsE,CAAC,CAACC,MAAM,CAACK,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;kBAC5C;gBACF,CAAC,MAAM;kBACLX,CAAC,CAACC,MAAM,CAACK,GAAG,GAAG,0BAA0B;gBAC3C;cACF;YAAE;cAAAhM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFX,OAAA;cAAK6F,SAAS,EAAC,cAAc;cAAAtF,QAAA,eAC3BP,OAAA,CAACI,YAAY;gBAACyF,SAAS,EAAC;cAAW;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACNX,OAAA;cAAK6F,SAAS,EAAC,gBAAgB;cAAAtF,QAAA,EAC5BkG,KAAK,CAACyG,QAAQ,IAAI;YAAO;cAAA1M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACL8F,KAAK,CAAC0G,SAAS,IAAI1G,KAAK,CAAC0G,SAAS,CAACvF,MAAM,GAAG,CAAC,iBAC5C5H,OAAA;cAAK6F,SAAS,EAAC,gBAAgB;cAAAtF,QAAA,gBAC7BP,OAAA,CAACiB,YAAY;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,MAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENX,OAAA;YAAK6F,SAAS,EAAC,oBAAoB;YAAAtF,QAAA,gBACjCP,OAAA;cAAI6F,SAAS,EAAC,aAAa;cAAAtF,QAAA,EAAEkG,KAAK,CAACQ;YAAK;cAAAzG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9CX,OAAA;cAAK6F,SAAS,EAAC,YAAY;cAAAtF,QAAA,gBACzBP,OAAA;gBAAM6F,SAAS,EAAC,eAAe;gBAAAtF,QAAA,EAAEwB,cAAc,CAAC0E,KAAK,CAACX,OAAO;cAAC;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtEX,OAAA;gBAAM6F,SAAS,EAAC,aAAa;gBAAAtF,QAAA,EAC1BgC,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAClEV,WAAW,GAAI,aAAY4E,KAAK,CAACZ,SAAS,IAAIY,KAAK,CAAC3D,KAAM,EAAC,GAAI,SAAQ2D,KAAK,CAACZ,SAAS,IAAIY,KAAK,CAAC3D,KAAM,EAAC,GACvG,QAAO2D,KAAK,CAACZ,SAAS,IAAIY,KAAK,CAAC3D,KAAM;cAAC;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNX,OAAA;cAAK6F,SAAS,EAAC,YAAY;cAAAtF,QAAA,GACxBkG,KAAK,CAACU,KAAK,iBAAInH,OAAA;gBAAM6F,SAAS,EAAC,WAAW;gBAAAtF,QAAA,EAAEkG,KAAK,CAACU;cAAK;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC/D8F,KAAK,CAAC2G,eAAe,IAAI3G,KAAK,CAAC2G,eAAe,MAAM3G,KAAK,CAACZ,SAAS,IAAIY,KAAK,CAAC3D,KAAK,CAAC,iBAClF9C,OAAA;gBAAM6F,SAAS,EAAC,YAAY;gBAAAtF,QAAA,GACzBsB,WAAW,GAAG,qBAAqB,GAAG,cAAc,EAAEU,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GACxHV,WAAW,GAAI,aAAY4E,KAAK,CAAC2G,eAAgB,EAAC,GAAI,SAAQ3G,KAAK,CAAC2G,eAAgB,EAAC,GACrF,QAAO3G,KAAK,CAAC2G,eAAgB,EAAC;cAAA;gBAAA5M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GArEEmH,KAAK;UAAAtH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENX,OAAA;QAAK6F,SAAS,EAAC,aAAa;QAAAtF,QAAA,gBAC1BP,OAAA,CAACY,eAAe;UAACiF,SAAS,EAAC;QAAY;UAAArF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CX,OAAA;UAAAO,QAAA,EAAKsB,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1EX,OAAA;UAAAO,QAAA,EAAIsB,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJX,OAAA;UAAG6F,SAAS,EAAC,YAAY;UAAAtF,QAAA,EAAEsB,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL4C,gBAAgB,CAACqE,MAAM,GAAG,CAAC,IAAIvE,iBAAiB,KAAK,IAAI,iBACxDrD,OAAA;MAAK6F,SAAS,EAAG,iBAAgBpC,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;MAAC8I,OAAO,EAAGL,CAAC,IAAK;QACpF,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACmB,aAAa,EAAEhF,eAAe,CAAC,CAAC;MACrD,CAAE;MAAA9H,QAAA,eACAP,OAAA;QAAK6F,SAAS,EAAG,eAAcpC,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;QAAAlD,QAAA,EAChE,CAAC,CAAA+M,UAAA,EAAAC,iBAAA,KAAM;UACN,MAAM9G,KAAK,GAAGxB,uBAAuB,CAAC5B,iBAAiB,CAAC;UACxD,IAAI,CAACoD,KAAK,EAAE,oBAAOzG,OAAA;YAAAO,QAAA,EAAK;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;UAE7C,oBACEX,OAAA;YAAK6F,SAAS,EAAC,sBAAsB;YAAAtF,QAAA,gBAEnCP,OAAA;cAAKK,KAAK,EAAE;gBAAEmN,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEC,YAAY,EAAE;cAAO,CAAE;cAAAnN,QAAA,eAChFP,OAAA;gBACE6F,SAAS,EAAC,uBAAuB;gBACjC0G,OAAO,EAAElE,eAAgB;gBACzBpB,KAAK,EAAC,aAAa;gBACnB5G,KAAK,EAAE;kBACLsN,UAAU,EAAE,iBAAiB;kBAC7BC,MAAM,EAAE,MAAM;kBACdC,YAAY,EAAE,KAAK;kBACnBC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdP,OAAO,EAAE,MAAM;kBACfQ,UAAU,EAAE,QAAQ;kBACpBP,cAAc,EAAE,QAAQ;kBACxBQ,MAAM,EAAE;gBACV,CAAE;gBAAA1N,QAAA,eAEFP,OAAA;kBAAMK,KAAK,EAAE;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNX,OAAA;cAAK6F,SAAS,EAAC,uBAAuB;cAAAtF,QAAA,gBAGpCP,OAAA;gBAAK6F,SAAS,EAAC,sBAAsB;gBAAAtF,QAAA,EACpCkG,KAAK,CAACuB,QAAQ,gBACbhI,OAAA;kBAAKK,KAAK,EAAE;oBAAE6N,QAAQ,EAAE,UAAU;oBAAEJ,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE;kBAAO,CAAE;kBAAAxN,QAAA,gBAChEP,OAAA;oBACEmO,GAAG,EAAGA,GAAG,IAAKrK,WAAW,CAACqK,GAAG,CAAE;oBAC/BC,QAAQ;oBACRC,WAAW;oBACXC,OAAO,EAAC,MAAM;oBACdR,KAAK,EAAC,MAAM;oBACZC,MAAM,EAAC,MAAM;oBACbQ,MAAM,EAAEvF,eAAe,CAACvC,KAAK,CAAE;oBAC/BpG,KAAK,EAAE;sBACLyN,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdS,eAAe,EAAE,MAAM;sBACvBC,SAAS,EAAE;oBACb,CAAE;oBACFtM,OAAO,EAAC,MAAM;oBACduK,OAAO,EAAGR,CAAC,IAAK;sBACdtI,aAAa,CAAE,yBAAwB6C,KAAK,CAACQ,KAAM,mCAAkC,CAAC;oBACxF,CAAE;oBACFyH,SAAS,EAAEA,CAAA,KAAM;sBACf9K,aAAa,CAAC,IAAI,CAAC;oBACrB,CAAE;oBACF+K,WAAW,EAAEA,CAAA,KAAM;sBACjBrI,OAAO,CAACqB,GAAG,CAAC,0BAA0B,CAAC;oBACzC,CAAE;oBACFiH,WAAW,EAAC,WAAW;oBAAArO,QAAA,gBAGvBP,OAAA;sBAAQwM,GAAG,EAAE/F,KAAK,CAAC0B,cAAc,IAAI1B,KAAK,CAACuB,QAAS;sBAACqE,IAAI,EAAC;oBAAW;sBAAA7L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAGvE8F,KAAK,CAAC0G,SAAS,IAAI1G,KAAK,CAAC0G,SAAS,CAACvF,MAAM,GAAG,CAAC,IAAInB,KAAK,CAAC0G,SAAS,CAACrC,GAAG,CAAC,CAAC+D,QAAQ,EAAE/G,KAAK,kBACpF9H,OAAA;sBAEE8O,IAAI,EAAC,WAAW;sBAChBtC,GAAG,EAAEqC,QAAQ,CAAC9B,GAAI;sBAClBgC,OAAO,EAAEF,QAAQ,CAACG,QAAS;sBAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;sBAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAItH,KAAK,KAAK;oBAAE,GALrC,GAAE+G,QAAQ,CAACG,QAAS,IAAGlH,KAAM,EAAC;sBAAAtH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMrC,CACF,CAAC,EAAC,8CAGL;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAGP8F,KAAK,CAAC0G,SAAS,IAAI1G,KAAK,CAAC0G,SAAS,CAACvF,MAAM,GAAG,CAAC,iBAC5C5H,OAAA;oBAAK6F,SAAS,EAAC,oBAAoB;oBAAAtF,QAAA,gBACjCP,OAAA,CAACiB,YAAY;sBAAC4E,SAAS,EAAC;oBAAe;sBAAArF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1CX,OAAA;sBAAAO,QAAA,GAAM,yBAAuB,EAACkG,KAAK,CAAC0G,SAAS,CAACvF,MAAM,EAAC,cAAY;oBAAA;sBAAApH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CACN,EAGAgD,UAAU,iBACT3D,OAAA;oBAAK6F,SAAS,EAAC,qBAAqB;oBAAAtF,QAAA,eAClCP,OAAA;sBAAK6F,SAAS,EAAC,eAAe;sBAAAtF,QAAA,gBAC5BP,OAAA,CAACkB,eAAe;wBAAC2E,SAAS,EAAC;sBAAY;wBAAArF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC1CX,OAAA;wBAAAO,QAAA,EAAIoD;sBAAU;wBAAAnD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnBX,OAAA;wBAAQuM,OAAO,EAAEA,CAAA,KAAM3I,aAAa,CAAC,IAAI,CAAE;wBAACiC,SAAS,EAAC,mBAAmB;wBAAAtF,QAAA,EAAC;sBAE1E;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,GACJ8F,KAAK,CAACyC,OAAO;gBAAA;gBACf;gBACAlJ,OAAA;kBACEwM,GAAG,EAAG,iCAAgC/F,KAAK,CAACyC,OAAQ,mBAAmB;kBACvEjC,KAAK,EAAER,KAAK,CAACQ,KAAM;kBACnBoI,WAAW,EAAC,GAAG;kBACfC,eAAe;kBACfjP,KAAK,EAAE;oBAAEyN,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE,MAAM;oBAAEH,MAAM,EAAE;kBAAO,CAAE;kBACzD2B,MAAM,EAAEA,CAAA,KAAMjJ,OAAO,CAACqB,GAAG,CAAC,yBAAyB;gBAAE;kBAAAnH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,gBAEVX,OAAA;kBAAK6F,SAAS,EAAC,aAAa;kBAAAtF,QAAA,gBAC1BP,OAAA;oBAAK6F,SAAS,EAAC,YAAY;oBAAAtF,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpCX,OAAA;oBAAAO,QAAA,EAAI;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1BX,OAAA;oBAAAO,QAAA,EAAIoD,UAAU,IAAI;kBAA4C;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnEX,OAAA;oBAAK6F,SAAS,EAAC,eAAe;oBAAAtF,QAAA,eAC5BP,OAAA;sBACEwP,IAAI,EAAE/I,KAAK,CAAC0B,cAAc,IAAI1B,KAAK,CAACuB,QAAS;sBAC7CmE,MAAM,EAAC,QAAQ;sBACfsD,GAAG,EAAC,qBAAqB;sBACzB5J,SAAS,EAAC,mBAAmB;sBAAAtF,QAAA,EAC9B;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAGNX,OAAA;gBAAK6F,SAAS,EAAC,oBAAoB;gBAAAtF,QAAA,gBACjCP,OAAA;kBAAI6F,SAAS,EAAC,qBAAqB;kBAAAtF,QAAA,EAAEkG,KAAK,CAACQ;gBAAK;kBAAAzG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtDX,OAAA;kBAAK6F,SAAS,EAAC,oBAAoB;kBAAAtF,QAAA,gBACjCP,OAAA;oBAAAO,QAAA,EAAOkG,KAAK,CAACX;kBAAO;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BX,OAAA;oBAAAO,QAAA,EAAM;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACdX,OAAA;oBAAAO,QAAA,GAAM,QAAM,EAACkG,KAAK,CAACZ,SAAS;kBAAA;oBAAArF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACnC8F,KAAK,CAAChE,KAAK,iBACVzC,OAAA,CAAAE,SAAA;oBAAAK,QAAA,gBACEP,OAAA;sBAAAO,QAAA,EAAM;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACdX,OAAA;sBAAAO,QAAA,EAAOkG,KAAK,CAAChE;oBAAK;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,eAC1B,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNX,OAAA;kBAAK6F,SAAS,EAAC,uBAAuB;kBAAAtF,QAAA,gBACpCP,OAAA;oBACE6F,SAAS,EAAG,sBAAqBpB,gBAAgB,GAAG,QAAQ,GAAG,EAAG,EAAE;oBACpE8H,OAAO,EAAEA,CAAA,KAAM7H,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;oBAAAlE,QAAA,gBAEtDP,OAAA;sBAAAO,QAAA,EAAM;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACfX,OAAA;sBAAAO,QAAA,EAAM;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACTX,OAAA;oBAAQ6F,SAAS,EAAC,oBAAoB;oBAAAtF,QAAA,gBACpCP,OAAA;sBAAAO,QAAA,EAAM;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACfX,OAAA;sBAAAO,QAAA,EAAM;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACTX,OAAA;oBAAQ6F,SAAS,EAAC,oBAAoB;oBAAAtF,QAAA,gBACpCP,OAAA;sBAAAO,QAAA,EAAM;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACfX,OAAA;sBAAAO,QAAA,EAAM;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL8D,gBAAgB,iBACfzE,OAAA;gBAAK6F,SAAS,EAAC,0BAA0B;gBAAAtF,QAAA,gBACvCP,OAAA;kBAAK6F,SAAS,EAAC,yBAAyB;kBAAAtF,QAAA,eACtCP,OAAA;oBAAAO,QAAA,GAAOwE,uBAAuB,CAAC,CAAC,CAAC6C,MAAM,EAAC,WAAS;kBAAA;oBAAApH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eAGNX,OAAA;kBAAK6F,SAAS,EAAC,uBAAuB;kBAAAtF,QAAA,gBACpCP,OAAA;oBAAK6F,SAAS,EAAC,wBAAwB;oBAAAtF,QAAA,EACpC,CAAAmB,IAAI,aAAJA,IAAI,wBAAA4L,UAAA,GAAJ5L,IAAI,CAAEgO,IAAI,cAAApC,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYxB,MAAM,CAAC,CAAC,CAAC,cAAAyB,iBAAA,uBAArBA,iBAAA,CAAuBxB,WAAW,CAAC,CAAC,KAAI;kBAAG;oBAAAvL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACNX,OAAA;oBAAKK,KAAK,EAAE;sBAAEsP,IAAI,EAAE;oBAAE,CAAE;oBAAApP,QAAA,gBACtBP,OAAA;sBACE6F,SAAS,EAAC,6BAA6B;sBACvCwD,KAAK,EAAEpF,UAAW;sBAClBgI,QAAQ,EAAGC,CAAC,IAAKhI,aAAa,CAACgI,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;sBAC/CiD,WAAW,EAAC,kBAAkB;sBAC9BsD,IAAI,EAAC,GAAG;sBACRvP,KAAK,EAAE;wBACLwP,SAAS,EAAE,MAAM;wBACjBC,MAAM,EAAE,MAAM;wBACdC,QAAQ,EAAE;sBACZ,CAAE;sBACFC,OAAO,EAAG9D,CAAC,IAAK;wBACdA,CAAC,CAACC,MAAM,CAAC9L,KAAK,CAAC0N,MAAM,GAAG,MAAM;wBAC9B7B,CAAC,CAACC,MAAM,CAAC9L,KAAK,CAAC0N,MAAM,GAAG7B,CAAC,CAACC,MAAM,CAAC8D,YAAY,GAAG,IAAI;sBACtD;oBAAE;sBAAAzP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EACDsD,UAAU,CAAC0C,IAAI,CAAC,CAAC,iBAChB3G,OAAA;sBAAK6F,SAAS,EAAC,yBAAyB;sBAAAtF,QAAA,gBACtCP,OAAA;wBACE6F,SAAS,EAAC,4BAA4B;wBACtC0G,OAAO,EAAEA,CAAA,KAAMrI,aAAa,CAAC,EAAE,CAAE;wBAAA3D,QAAA,EAClC;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTX,OAAA;wBACE6F,SAAS,EAAC,4BAA4B;wBACtC0G,OAAO,EAAExC,gBAAiB;wBAC1BmG,QAAQ,EAAE,CAACjM,UAAU,CAAC0C,IAAI,CAAC,CAAE;wBAAApG,QAAA,EAC9B;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAINX,OAAA;kBAAK6F,SAAS,EAAC,uBAAuB;kBAAAtF,QAAA,EACnCwE,uBAAuB,CAAC,CAAC,CAAC6C,MAAM,KAAK,CAAC,gBACrC5H,OAAA;oBAAKK,KAAK,EAAE;sBAAE8P,SAAS,EAAE,QAAQ;sBAAEC,OAAO,EAAE,QAAQ;sBAAEC,KAAK,EAAE;oBAAU,CAAE;oBAAA9P,QAAA,gBACvEP,OAAA;sBAAKK,KAAK,EAAE;wBAAEC,QAAQ,EAAE,MAAM;wBAAEoN,YAAY,EAAE;sBAAO,CAAE;sBAAAnN,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChEX,OAAA;sBAAAO,QAAA,EAAG;oBAAqD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,GAENoE,uBAAuB,CAAC,CAAC,CAAC+F,GAAG,CAAEV,OAAO;oBAAA,IAAAkG,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,iBAAA;oBAAA,oBACtCzQ,OAAA;sBAAqC6F,SAAS,EAAC,iBAAiB;sBAAAtF,QAAA,gBAC9DP,OAAA;wBAAK6F,SAAS,EAAC,wBAAwB;wBAAAtF,QAAA,EACpC6J,OAAO,CAACE,MAAM,MAAAgG,eAAA,GAAIlG,OAAO,CAACC,MAAM,cAAAiG,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBxE,MAAM,CAAC,CAAC,CAAC,cAAAyE,qBAAA,uBAAzBA,qBAAA,CAA2BxE,WAAW,CAAC,CAAC,KAAI;sBAAG;wBAAAvL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/D,CAAC,eACNX,OAAA;wBAAK6F,SAAS,EAAC,yBAAyB;wBAAAtF,QAAA,gBACtCP,OAAA;0BAAK6F,SAAS,EAAC,wBAAwB;0BAAAtF,QAAA,gBACrCP,OAAA;4BAAM6F,SAAS,EAAC,wBAAwB;4BAAAtF,QAAA,EAAE6J,OAAO,CAACC;0BAAM;4BAAA7J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EAC/D,CAACyJ,OAAO,CAACsG,QAAQ,KAAK,OAAO,IAAItG,OAAO,CAACuG,OAAO,kBAC/C3Q,OAAA,CAACF,UAAU;4BAACO,KAAK,EAAE;8BAAEgQ,KAAK,EAAE,SAAS;8BAAE/P,QAAQ,EAAE,MAAM;8BAAEsQ,UAAU,EAAE;4BAAM,CAAE;4BAAC3J,KAAK,EAAC;0BAAgB;4BAAAzG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CACvG,eACDX,OAAA;4BAAM6F,SAAS,EAAC,sBAAsB;4BAAAtF,QAAA,EACnC+K,aAAa,CAAClB,OAAO,CAAC3C,SAAS,IAAI2C,OAAO,CAACmB,SAAS;0BAAC;4BAAA/K,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACNX,OAAA;0BAAK6F,SAAS,EAAC,sBAAsB;0BAAAtF,QAAA,EAClC6J,OAAO,CAACD;wBAAI;0BAAA3J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC,eAENX,OAAA;0BAAK6F,SAAS,EAAC,yBAAyB;0BAAAtF,QAAA,gBACtCP,OAAA;4BACEuM,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAACX,OAAO,CAAChF,GAAG,IAAIgF,OAAO,CAACjF,EAAE,CAAE;4BAC5DU,SAAS,EAAG,0BAAyB,CAAA2K,gBAAA,GAAApG,OAAO,CAACK,OAAO,cAAA+F,gBAAA,eAAfA,gBAAA,CAAiBtJ,QAAQ,CAACxF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,GAAG,CAAC,GAAG,OAAO,GAAG,EAAG,EAAE;4BAAA7E,QAAA,gBAE3FP,OAAA;8BAAAO,QAAA,EAAO,CAAAkQ,iBAAA,GAAArG,OAAO,CAACK,OAAO,cAAAgG,iBAAA,eAAfA,iBAAA,CAAiBvJ,QAAQ,CAACxF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,GAAG,CAAC,GAAG,IAAI,GAAG;4BAAI;8BAAA5E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,EAChEyJ,OAAO,CAACI,KAAK,GAAG,CAAC,iBAAIxK,OAAA;8BAAAO,QAAA,EAAO6J,OAAO,CAACI;4BAAK;8BAAAhK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5C,CAAC,eACTX,OAAA;4BACEuM,OAAO,EAAEA,CAAA,KAAMnI,aAAa,CAACD,UAAU,MAAMiG,OAAO,CAAChF,GAAG,IAAIgF,OAAO,CAACjF,EAAE,CAAC,GAAG,IAAI,GAAIiF,OAAO,CAAChF,GAAG,IAAIgF,OAAO,CAACjF,EAAG,CAAE;4BAC9GU,SAAS,EAAC,wBAAwB;4BAAAtF,QAAA,eAElCP,OAAA;8BAAAO,QAAA,EAAM;4BAAE;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACT,CAAC,eACTX,OAAA;4BACEuM,OAAO,EAAEA,CAAA,KAAMnI,aAAa,CAACD,UAAU,MAAMiG,OAAO,CAAChF,GAAG,IAAIgF,OAAO,CAACjF,EAAE,CAAC,GAAG,IAAI,GAAIiF,OAAO,CAAChF,GAAG,IAAIgF,OAAO,CAACjF,EAAG,CAAE;4BAC9GU,SAAS,EAAC,wBAAwB;4BAAAtF,QAAA,EACnC;0BAED;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,EAERyJ,OAAO,CAAC1I,IAAI,MAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,GAAG,kBACzBpF,OAAA,CAAAE,SAAA;4BAAAK,QAAA,gBACEP,OAAA;8BAAQ6F,SAAS,EAAC,wBAAwB;8BAAAtF,QAAA,EAAC;4BAE3C;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,eACTX,OAAA;8BACE6F,SAAS,EAAC,wBAAwB;8BAClC0G,OAAO,EAAEA,CAAA,KAAM;gCACb,IAAIsE,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;kCACnE5F,mBAAmB,CAACd,OAAO,CAAChF,GAAG,IAAIgF,OAAO,CAACjF,EAAE,CAAC;gCAChD;8BACF,CAAE;8BAAA5E,QAAA,EACH;4BAED;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA,eACT,CACH;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA,GAzDEyJ,OAAO,CAAChF,GAAG,IAAIgF,OAAO,CAACjF,EAAE;sBAAA3E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0D9B,CAAC;kBAAA,CACL;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEV,CAAC,EAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACc,EAAA,CAvjCQD,YAAY;EAAA,QACFnC,WAAW,EAC6BQ,WAAW,EACnDT,WAAW;AAAA;AAAA2R,EAAA,GAHrBvP,YAAY;AAyjCrB,eAAeA,YAAY;AAAC,IAAAuP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}