{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoLessons = () => {\n  _s();\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.user) {\n        console.log('✅ Found user in state.user');\n        return state.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState({});\n  const [newComment, setNewComment] = useState('');\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [replyingTo, setReplyingTo] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = video => {\n    // Based on your database schema, the field is 'thumbnail'\n    if (video.thumbnail && video.thumbnail.trim() !== '') {\n      console.log('✅ Using database thumbnail:', video.thumbnail);\n      return video.thumbnail;\n    }\n\n    // Fallback to thumbnailUrl if available\n    if (video.thumbnailUrl && video.thumbnailUrl.trim() !== '') {\n      console.log('✅ Using thumbnailUrl:', video.thumbnailUrl);\n      return video.thumbnailUrl;\n    }\n\n    // For YouTube videos, extract video ID and use YouTube thumbnail\n    if (video.videoID && video.videoID.trim() !== '') {\n      // Check if it's a YouTube URL or just an ID\n      let videoId = video.videoID;\n\n      // If it's a full YouTube URL, extract the ID\n      if (video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be')) {\n        const match = video.videoID.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : video.videoID;\n      }\n      console.log('📺 Using YouTube thumbnail for video ID:', videoId);\n      return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n    }\n\n    // Final fallback - clean placeholder\n    console.log('🎬 Using placeholder for video:', video.title);\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n  };\n  const getSubjectName = subject => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Comment helper functions\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    console.log('🔍 Getting comments for video ID:', videoId);\n    console.log('🔍 Available comments:', comments);\n    return comments[videoId] || [];\n  };\n  const formatTimeAgo = timestamp => {\n    if (!timestamp) return 'Just now';\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  };\n\n  // Comment handlers\n  const handleAddComment = async () => {\n    if (!newComment.trim() || currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    console.log('💬 Adding comment with user data:', user);\n\n    // Get user name properly with extensive debugging\n    const userName = (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.username) || (user === null || user === void 0 ? void 0 : user.displayName) || 'Student';\n    const fullName = user !== null && user !== void 0 && user.firstName && user !== null && user !== void 0 && user.lastName ? `${user.firstName} ${user.lastName}` : userName;\n    console.log('👤 User name resolution:');\n    console.log('  - user?.name:', user === null || user === void 0 ? void 0 : user.name);\n    console.log('  - user?.firstName:', user === null || user === void 0 ? void 0 : user.firstName);\n    console.log('  - user?.lastName:', user === null || user === void 0 ? void 0 : user.lastName);\n    console.log('  - user?.username:', user === null || user === void 0 ? void 0 : user.username);\n    console.log('  - user?.displayName:', user === null || user === void 0 ? void 0 : user.displayName);\n    console.log('  - Final fullName:', fullName);\n    const comment = {\n      id: Date.now().toString(),\n      text: newComment.trim(),\n      author: fullName,\n      user: (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id),\n      userId: (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id),\n      userRole: user === null || user === void 0 ? void 0 : user.role,\n      isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'admin',\n      timestamp: new Date().toISOString(),\n      createdAt: new Date().toISOString(),\n      likes: 0,\n      likedBy: [],\n      // Add user profile data\n      userProfile: {\n        name: fullName,\n        firstName: user === null || user === void 0 ? void 0 : user.firstName,\n        lastName: user === null || user === void 0 ? void 0 : user.lastName,\n        username: user === null || user === void 0 ? void 0 : user.username,\n        email: user === null || user === void 0 ? void 0 : user.email,\n        role: user === null || user === void 0 ? void 0 : user.role,\n        avatar: (user === null || user === void 0 ? void 0 : user.avatar) || (user === null || user === void 0 ? void 0 : user.profilePicture)\n      }\n    };\n    console.log('💬 Created comment object:', comment);\n\n    // Try to save comment to backend using the correct API\n    try {\n      const videoId = video._id || video.id;\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          videoId: videoId,\n          text: newComment.trim(),\n          author: fullName,\n          avatar: (user === null || user === void 0 ? void 0 : user.avatar) || (user === null || user === void 0 ? void 0 : user.profilePicture) || fullName.charAt(0).toUpperCase(),\n          userLevel: (user === null || user === void 0 ? void 0 : user.level) || 'primary',\n          userClass: (user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className)\n        })\n      });\n      if (response.ok) {\n        const savedComment = await response.json();\n        console.log('✅ Comment saved to backend:', savedComment);\n\n        // Use the saved comment from backend\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), savedComment.data || savedComment]\n        }));\n      } else {\n        console.log('⚠️ Backend save failed, using local storage');\n        // Fallback to local storage\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), comment]\n        }));\n      }\n    } catch (error) {\n      console.log('⚠️ Backend error, using local storage:', error);\n      // Fallback to local storage\n      const videoId = video._id || video.id;\n      setComments(prev => ({\n        ...prev,\n        [videoId]: [...(prev[videoId] || []), comment]\n      }));\n    }\n    setNewComment('');\n  };\n  const handleLikeComment = commentId => {\n    if (!(user !== null && user !== void 0 && user._id) && !(user !== null && user !== void 0 && user.id) || currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    const userId = (user === null || user === void 0 ? void 0 : user._id) || (user === null || user === void 0 ? void 0 : user.id);\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).map(comment => {\n        if (comment.id === commentId || comment._id === commentId) {\n          var _comment$likedBy;\n          const isLiked = (_comment$likedBy = comment.likedBy) === null || _comment$likedBy === void 0 ? void 0 : _comment$likedBy.includes(userId);\n          return {\n            ...comment,\n            likes: isLiked ? comment.likes - 1 : comment.likes + 1,\n            likedBy: isLiked ? comment.likedBy.filter(id => id !== userId) : [...(comment.likedBy || []), userId]\n          };\n        }\n        return comment;\n      })\n    }));\n  };\n  const handleDeleteComment = commentId => {\n    if (currentVideoIndex === null) return;\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = (video === null || video === void 0 ? void 0 : video._id) || (video === null || video === void 0 ? void 0 : video.id);\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).filter(comment => comment.id !== commentId && comment._id !== commentId)\n    }));\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('🎥 Attempting to fetch videos from database...');\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        var _response, _response2;\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n        if ((_response = response) !== null && _response !== void 0 && _response.success && (_response2 = response) !== null && _response2 !== void 0 && _response2.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n\n          // Debug: Log first video structure to see available thumbnail fields\n          if (videos.length > 0) {\n            console.log('🔍 FIRST VIDEO STRUCTURE:', videos[0]);\n            console.log('🔍 AVAILABLE FIELDS:', Object.keys(videos[0]));\n            console.log('🔍 THUMBNAIL FIELDS CHECK:');\n            console.log('  - thumbnail:', videos[0].thumbnail);\n            console.log('  - thumbnailUrl:', videos[0].thumbnailUrl);\n            console.log('  - image:', videos[0].image);\n            console.log('  - imageUrl:', videos[0].imageUrl);\n            console.log('  - poster:', videos[0].poster);\n            console.log('  - posterUrl:', videos[0].posterUrl);\n            console.log('  - cover:', videos[0].cover);\n            console.log('  - coverImage:', videos[0].coverImage);\n            console.log('  - previewImage:', videos[0].previewImage);\n            console.log('  - videoThumbnail:', videos[0].videoThumbnail);\n            console.log('  - thumb:', videos[0].thumb);\n            console.log('  - preview:', videos[0].preview);\n\n            // Check a few more videos to see if any have thumbnails\n            console.log('🔍 CHECKING MORE VIDEOS FOR THUMBNAILS:');\n            videos.slice(0, 5).forEach((video, index) => {\n              console.log(`Video ${index + 1} (${video.title}):`, {\n                thumbnail: video.thumbnail,\n                thumbnailUrl: video.thumbnailUrl,\n                image: video.image,\n                imageUrl: video.imageUrl,\n                poster: video.poster,\n                thumb: video.thumb,\n                preview: video.preview\n              });\n            });\n          }\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          var _response3, _response3$data, _response4, _response4$data;\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n          if ((_response3 = response) !== null && _response3 !== void 0 && (_response3$data = _response3.data) !== null && _response3$data !== void 0 && _response3$data.success && (_response4 = response) !== null && _response4 !== void 0 && (_response4$data = _response4.data) !== null && _response4$data !== void 0 && _response4$data.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          var _response5, _response5$data, _response6, _response6$data;\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n          if ((_response5 = response) !== null && _response5 !== void 0 && (_response5$data = _response5.data) !== null && _response5$data !== void 0 && _response5$data.success && (_response6 = response) !== null && _response6 !== void 0 && (_response6$data = _response6.data) !== null && _response6$data !== void 0 && _response6$data.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item => {\n              var _item$title;\n              return item.type === 'video' || item.videoUrl || item.videoID || ((_item$title = item.title) === null || _item$title === void 0 ? void 0 : _item$title.toLowerCase().includes('video'));\n            });\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel || !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass || !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject || !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      var _video$title, _video$subject, _video$topic;\n      const matchesSearch = !searchTerm || ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isKiswahili ? 'Masomo ya Video' : 'Video Lessons'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: isKiswahili ? 'Tafuta video...' : 'Search videos...',\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLevel,\n          onChange: e => setSelectedLevel(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"primary\",\n            children: isKiswahili ? 'Msingi' : 'Primary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"secondary\",\n            children: isKiswahili ? 'Sekondari' : 'Secondary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advance\",\n            children: isKiswahili ? 'Juu' : 'Advanced'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedClass,\n          onChange: e => setSelectedClass(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedSubject,\n          onChange: e => setSelectedSubject(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => {\n          var _ref, _ref$charAt;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-item\",\n            children: currentVideoIndex === index ?\n            /*#__PURE__*/\n            /* Video Player - Replaces the thumbnail when playing */\n            _jsxDEV(\"div\", {\n              className: \"inline-video-player\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-style-layout\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-player\",\n                  children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: ref => setVideoRef(ref),\n                    controls: true,\n                    autoPlay: true,\n                    playsInline: true,\n                    preload: \"metadata\",\n                    width: \"100%\",\n                    height: \"100%\",\n                    poster: getThumbnailUrl(video),\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      backgroundColor: '#000',\n                      objectFit: 'contain'\n                    },\n                    onError: e => setVideoError(`Failed to load video: ${video.title}`),\n                    onCanPlay: () => setVideoError(null),\n                    crossOrigin: \"anonymous\",\n                    children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                      src: video.signedVideoUrl || video.videoUrl,\n                      type: \"video/mp4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 29\n                    }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                      kind: \"subtitles\",\n                      src: subtitle.url,\n                      srcLang: subtitle.language,\n                      label: subtitle.languageName,\n                      default: subtitle.isDefault || index === 0\n                    }, `${subtitle.language}-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 31\n                    }, this)), \"Your browser does not support the video tag.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 27\n                  }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                    src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                    title: video.title,\n                    frameBorder: \"0\",\n                    allowFullScreen: true,\n                    style: {\n                      width: '100%',\n                      height: '100%',\n                      border: 'none'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"video-error\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"error-icon\",\n                      children: \"\\u26A0\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      children: \"Video Unavailable\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 596,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: videoError || \"This video cannot be played at the moment.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"youtube-video-title\",\n                    children: video.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-video-meta\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: getSubjectName(video.subject)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 605,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 606,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Class \", video.className || video.class]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 607,\n                      columnNumber: 27\n                    }, this), video.level && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2022\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 610,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: video.level\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 611,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-video-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `youtube-action-btn ${commentsExpanded ? 'active' : ''}`,\n                      onClick: () => setCommentsExpanded(!commentsExpanded),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 620,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Comments\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 621,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"youtube-action-btn\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\uD83D\\uDC4D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 624,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Like\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 625,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 623,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"youtube-action-btn\",\n                      onClick: () => setCurrentVideoIndex(null),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u2715\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 631,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Close\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 632,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 627,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 23\n                }, this), commentsExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-comments-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comments-header\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [getCurrentVideoComments().length, \" Comments\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comment-input\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"youtube-comment-avatar\",\n                      children: (_ref = (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.username) || 'Student') === null || _ref === void 0 ? void 0 : (_ref$charAt = _ref.charAt(0)) === null || _ref$charAt === void 0 ? void 0 : _ref$charAt.toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 646,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                        className: \"youtube-comment-input-field\",\n                        value: newComment,\n                        onChange: e => setNewComment(e.target.value),\n                        placeholder: \"Add a comment...\",\n                        rows: \"1\",\n                        style: {\n                          minHeight: '20px',\n                          resize: 'none',\n                          overflow: 'hidden'\n                        },\n                        onInput: e => {\n                          e.target.style.height = 'auto';\n                          e.target.style.height = e.target.scrollHeight + 'px';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 650,\n                        columnNumber: 31\n                      }, this), newComment.trim() && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment-actions\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"youtube-comment-btn cancel\",\n                          onClick: () => setNewComment(''),\n                          children: \"Cancel\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 668,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"youtube-comment-btn submit\",\n                          onClick: handleAddComment,\n                          disabled: !newComment.trim(),\n                          children: \"Comment\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 674,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"youtube-comments-list\",\n                    children: getCurrentVideoComments().length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        textAlign: 'center',\n                        padding: '40px 0',\n                        color: '#606060'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '48px',\n                          marginBottom: '16px'\n                        },\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 690,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No comments yet. Be the first to share your thoughts!\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 691,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 689,\n                      columnNumber: 31\n                    }, this) : getCurrentVideoComments().map(comment => {\n                      var _comment$author, _comment$author$charA, _comment$likedBy2, _comment$likedBy3;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"youtube-comment\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-avatar\",\n                          children: comment.avatar || ((_comment$author = comment.author) === null || _comment$author === void 0 ? void 0 : (_comment$author$charA = _comment$author.charAt(0)) === null || _comment$author$charA === void 0 ? void 0 : _comment$author$charA.toUpperCase()) || \"A\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 696,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"youtube-comment-content\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-header\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"youtube-comment-author\",\n                              children: comment.author\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 701,\n                              columnNumber: 39\n                            }, this), (comment.userRole === 'admin' || comment.isAdmin) && /*#__PURE__*/_jsxDEV(MdVerified, {\n                              style: {\n                                color: '#1d9bf0',\n                                fontSize: '12px',\n                                marginLeft: '4px'\n                              },\n                              title: \"Verified Admin\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 703,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"youtube-comment-time\",\n                              children: formatTimeAgo(comment.createdAt || comment.timestamp)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 705,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 700,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-text\",\n                            children: comment.text\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 709,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"youtube-comment-actions\",\n                            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => handleLikeComment(comment._id || comment.id),\n                              className: `youtube-comment-action ${(_comment$likedBy2 = comment.likedBy) !== null && _comment$likedBy2 !== void 0 && _comment$likedBy2.includes(user === null || user === void 0 ? void 0 : user._id) ? 'liked' : ''}`,\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                children: (_comment$likedBy3 = comment.likedBy) !== null && _comment$likedBy3 !== void 0 && _comment$likedBy3.includes(user === null || user === void 0 ? void 0 : user._id) ? '👍' : '👍'\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 717,\n                                columnNumber: 41\n                              }, this), comment.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: comment.likes\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 718,\n                                columnNumber: 63\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 713,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"youtube-comment-action\",\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: \"\\uD83D\\uDC4E\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 721,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 720,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                              className: \"youtube-comment-action\",\n                              children: \"Reply\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 723,\n                              columnNumber: 39\n                            }, this), comment.user === (user === null || user === void 0 ? void 0 : user._id) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-action\",\n                                children: \"Edit\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 728,\n                                columnNumber: 43\n                              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                className: \"youtube-comment-action\",\n                                onClick: () => {\n                                  if (window.confirm('Are you sure you want to delete this comment?')) {\n                                    handleDeleteComment(comment._id || comment.id);\n                                  }\n                                },\n                                children: \"Delete\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 731,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 712,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 699,\n                          columnNumber: 35\n                        }, this)]\n                      }, comment._id || comment.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 695,\n                        columnNumber: 33\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 687,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 19\n            }, this) :\n            /*#__PURE__*/\n            /* Video Card - Shows thumbnail when not playing */\n            _jsxDEV(\"div\", {\n              className: \"video-card\",\n              onClick: () => handleShowVideo(index),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-thumbnail\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getThumbnailUrl(video),\n                  alt: video.title,\n                  className: \"thumbnail-image\",\n                  loading: \"lazy\",\n                  onError: e => {\n                    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                      let videoId = video.videoID;\n                      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                      videoId = match ? match[1] : videoId;\n                      const fallbacks = [`https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`, `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4='];\n                      const currentSrc = e.target.src;\n                      const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop().split('.')[0]));\n                      if (currentIndex < fallbacks.length - 1) {\n                        e.target.src = fallbacks[currentIndex + 1];\n                      }\n                    } else {\n                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"play-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                    className: \"play-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 785,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 784,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-duration\",\n                  children: video.duration || \"Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 23\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 792,\n                    columnNumber: 27\n                  }, this), \"CC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-card-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 799,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 802,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-tags\",\n                  children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"topic-tag\",\n                    children: video.topic\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 809,\n                    columnNumber: 41\n                  }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"shared-tag\",\n                    children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 798,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 829,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 830,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 826,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 478,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoLessons, \"sUl5UJEZU46h2aSoRLty1l9iUvM=\", false, function () {\n  return [useSelector];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useSelector", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "TbAlertTriangle", "MdVerified", "getStudyMaterial", "getAllVideos", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoLessons", "_s", "user", "state", "console", "log", "Object", "keys", "users", "auth", "storedUser", "localStorage", "getItem", "JSON", "parse", "e", "error", "videos", "setVideos", "loading", "setLoading", "setError", "searchTerm", "setSearchTerm", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "currentVideoIndex", "setCurrentVideoIndex", "videoRef", "setVideoRef", "videoError", "setVideoError", "comments", "setComments", "newComment", "setNewComment", "commentsExpanded", "setCommentsExpanded", "replyingTo", "setReplyingTo", "isKiswahili", "getThumbnailUrl", "video", "thumbnail", "trim", "thumbnailUrl", "videoID", "videoId", "includes", "match", "title", "getSubjectName", "subject", "subjectMap", "getCurrentVideoComments", "filteredAndSortedVideos", "_id", "id", "formatTimeAgo", "timestamp", "now", "Date", "time", "diffInSeconds", "Math", "floor", "handleAddComment", "userName", "name", "firstName", "username", "displayName", "fullName", "lastName", "comment", "toString", "text", "author", "userId", "userRole", "role", "isAdmin", "toISOString", "createdAt", "likes", "<PERSON><PERSON><PERSON>", "userProfile", "email", "avatar", "profilePicture", "response", "fetch", "process", "env", "REACT_APP_API_URL", "method", "headers", "body", "stringify", "char<PERSON>t", "toUpperCase", "userLevel", "level", "userClass", "class", "className", "ok", "savedComment", "json", "prev", "data", "handleLikeComment", "commentId", "map", "_comment$likedBy", "isLiked", "filter", "handleDeleteComment", "handleShowVideo", "index", "videoUrl", "signedVideoUrl", "warn", "fetchVideos", "_response", "_response2", "success", "length", "image", "imageUrl", "poster", "posterUrl", "cover", "coverImage", "previewImage", "videoThumbnail", "thumb", "preview", "slice", "for<PERSON>ach", "message", "_response3", "_response3$data", "_response4", "_response4$data", "filters", "type", "_response5", "_response5$data", "_response6", "_response6$data", "allData", "item", "_item$title", "toLowerCase", "filtered", "matchesLevel", "matchesClass", "matchesSubject", "err", "_video$title", "_video$subject", "_video$topic", "matchesSearch", "topic", "sort", "a", "b", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "target", "onClick", "_ref", "_ref$charAt", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "style", "backgroundColor", "objectFit", "onError", "onCanPlay", "crossOrigin", "src", "subtitles", "subtitle", "kind", "url", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "flex", "rows", "minHeight", "resize", "overflow", "onInput", "scrollHeight", "disabled", "textAlign", "padding", "color", "fontSize", "marginBottom", "_comment$author", "_comment$author$charA", "_comment$likedBy2", "_comment$likedBy3", "marginLeft", "window", "confirm", "alt", "fallbacks", "currentSrc", "currentIndex", "findIndex", "split", "pop", "duration", "sharedFromClass", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\n\nconst VideoLessons = () => {\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.user) {\n        console.log('✅ Found user in state.user');\n        return state.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState({});\n  const [newComment, setNewComment] = useState('');\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n  const [replyingTo, setReplyingTo] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = (video) => {\n    // Based on your database schema, the field is 'thumbnail'\n    if (video.thumbnail && video.thumbnail.trim() !== '') {\n      console.log('✅ Using database thumbnail:', video.thumbnail);\n      return video.thumbnail;\n    }\n\n    // Fallback to thumbnailUrl if available\n    if (video.thumbnailUrl && video.thumbnailUrl.trim() !== '') {\n      console.log('✅ Using thumbnailUrl:', video.thumbnailUrl);\n      return video.thumbnailUrl;\n    }\n\n    // For YouTube videos, extract video ID and use YouTube thumbnail\n    if (video.videoID && video.videoID.trim() !== '') {\n      // Check if it's a YouTube URL or just an ID\n      let videoId = video.videoID;\n\n      // If it's a full YouTube URL, extract the ID\n      if (video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be')) {\n        const match = video.videoID.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : video.videoID;\n      }\n\n      console.log('📺 Using YouTube thumbnail for video ID:', videoId);\n      return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n    }\n\n    // Final fallback - clean placeholder\n    console.log('🎬 Using placeholder for video:', video.title);\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';\n  };\n\n  const getSubjectName = (subject) => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Comment helper functions\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n    console.log('🔍 Getting comments for video ID:', videoId);\n    console.log('🔍 Available comments:', comments);\n    return comments[videoId] || [];\n  };\n\n  const formatTimeAgo = (timestamp) => {\n    if (!timestamp) return 'Just now';\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n  };\n\n  // Comment handlers\n  const handleAddComment = async () => {\n    if (!newComment.trim() || currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n\n    console.log('💬 Adding comment with user data:', user);\n\n    // Get user name properly with extensive debugging\n    const userName = user?.name || user?.firstName || user?.username || user?.displayName || 'Student';\n    const fullName = user?.firstName && user?.lastName\n      ? `${user.firstName} ${user.lastName}`\n      : userName;\n\n    console.log('👤 User name resolution:');\n    console.log('  - user?.name:', user?.name);\n    console.log('  - user?.firstName:', user?.firstName);\n    console.log('  - user?.lastName:', user?.lastName);\n    console.log('  - user?.username:', user?.username);\n    console.log('  - user?.displayName:', user?.displayName);\n    console.log('  - Final fullName:', fullName);\n\n    const comment = {\n      id: Date.now().toString(),\n      text: newComment.trim(),\n      author: fullName,\n      user: user?._id || user?.id,\n      userId: user?._id || user?.id,\n      userRole: user?.role,\n      isAdmin: user?.role === 'admin',\n      timestamp: new Date().toISOString(),\n      createdAt: new Date().toISOString(),\n      likes: 0,\n      likedBy: [],\n      // Add user profile data\n      userProfile: {\n        name: fullName,\n        firstName: user?.firstName,\n        lastName: user?.lastName,\n        username: user?.username,\n        email: user?.email,\n        role: user?.role,\n        avatar: user?.avatar || user?.profilePicture\n      }\n    };\n\n    console.log('💬 Created comment object:', comment);\n\n    // Try to save comment to backend using the correct API\n    try {\n      const videoId = video._id || video.id;\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/video-comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n        },\n        body: JSON.stringify({\n          videoId: videoId,\n          text: newComment.trim(),\n          author: fullName,\n          avatar: user?.avatar || user?.profilePicture || fullName.charAt(0).toUpperCase(),\n          userLevel: user?.level || 'primary',\n          userClass: user?.class || user?.className\n        })\n      });\n\n      if (response.ok) {\n        const savedComment = await response.json();\n        console.log('✅ Comment saved to backend:', savedComment);\n\n        // Use the saved comment from backend\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), savedComment.data || savedComment]\n        }));\n      } else {\n        console.log('⚠️ Backend save failed, using local storage');\n        // Fallback to local storage\n        setComments(prev => ({\n          ...prev,\n          [videoId]: [...(prev[videoId] || []), comment]\n        }));\n      }\n    } catch (error) {\n      console.log('⚠️ Backend error, using local storage:', error);\n      // Fallback to local storage\n      const videoId = video._id || video.id;\n      setComments(prev => ({\n        ...prev,\n        [videoId]: [...(prev[videoId] || []), comment]\n      }));\n    }\n\n    setNewComment('');\n  };\n\n  const handleLikeComment = (commentId) => {\n    if (!user?._id && !user?.id || currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n    const userId = user?._id || user?.id;\n\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).map(comment => {\n        if (comment.id === commentId || comment._id === commentId) {\n          const isLiked = comment.likedBy?.includes(userId);\n          return {\n            ...comment,\n            likes: isLiked ? comment.likes - 1 : comment.likes + 1,\n            likedBy: isLiked\n              ? comment.likedBy.filter(id => id !== userId)\n              : [...(comment.likedBy || []), userId]\n          };\n        }\n        return comment;\n      })\n    }));\n  };\n\n  const handleDeleteComment = (commentId) => {\n    if (currentVideoIndex === null) return;\n\n    const video = filteredAndSortedVideos[currentVideoIndex];\n    const videoId = video?._id || video?.id;\n\n    setComments(prev => ({\n      ...prev,\n      [videoId]: (prev[videoId] || []).filter(comment =>\n        comment.id !== commentId && comment._id !== commentId\n      )\n    }));\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🎥 Attempting to fetch videos from database...');\n\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n\n        if (response?.success && response?.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n\n          // Debug: Log first video structure to see available thumbnail fields\n          if (videos.length > 0) {\n            console.log('🔍 FIRST VIDEO STRUCTURE:', videos[0]);\n            console.log('🔍 AVAILABLE FIELDS:', Object.keys(videos[0]));\n            console.log('🔍 THUMBNAIL FIELDS CHECK:');\n            console.log('  - thumbnail:', videos[0].thumbnail);\n            console.log('  - thumbnailUrl:', videos[0].thumbnailUrl);\n            console.log('  - image:', videos[0].image);\n            console.log('  - imageUrl:', videos[0].imageUrl);\n            console.log('  - poster:', videos[0].poster);\n            console.log('  - posterUrl:', videos[0].posterUrl);\n            console.log('  - cover:', videos[0].cover);\n            console.log('  - coverImage:', videos[0].coverImage);\n            console.log('  - previewImage:', videos[0].previewImage);\n            console.log('  - videoThumbnail:', videos[0].videoThumbnail);\n            console.log('  - thumb:', videos[0].thumb);\n            console.log('  - preview:', videos[0].preview);\n\n            // Check a few more videos to see if any have thumbnails\n            console.log('🔍 CHECKING MORE VIDEOS FOR THUMBNAILS:');\n            videos.slice(0, 5).forEach((video, index) => {\n              console.log(`Video ${index + 1} (${video.title}):`, {\n                thumbnail: video.thumbnail,\n                thumbnailUrl: video.thumbnailUrl,\n                image: video.image,\n                imageUrl: video.imageUrl,\n                poster: video.poster,\n                thumb: video.thumb,\n                preview: video.preview\n              });\n            });\n          }\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item =>\n              item.type === 'video' ||\n              item.videoUrl ||\n              item.videoID ||\n              item.title?.toLowerCase().includes('video')\n            );\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' ||\n                              video.level === selectedLevel ||\n                              !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' ||\n                              video.className === selectedClass ||\n                              video.class === selectedClass ||\n                              !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' ||\n                                video.subject === selectedSubject ||\n                                !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      const matchesSearch = !searchTerm ||\n        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());\n\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n\n  return (\n    <div className=\"video-lessons-container\">\n      <div className=\"video-lessons-header\">\n        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>\n        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"video-controls\">\n        <div className=\"search-section\">\n          <input\n            type=\"text\"\n            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n\n        <div className=\"filter-section\">\n          <select\n            value={selectedLevel}\n            onChange={(e) => setSelectedLevel(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"primary\">{isKiswahili ? 'Msingi' : 'Primary'}</option>\n            <option value=\"secondary\">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>\n            <option value=\"advance\">{isKiswahili ? 'Juu' : 'Advanced'}</option>\n          </select>\n\n          <select\n            value={selectedClass}\n            onChange={(e) => setSelectedClass(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n            {/* Add class options based on selected level */}\n          </select>\n\n          <select\n            value={selectedSubject}\n            onChange={(e) => setSelectedSubject(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n            {/* Add subject options */}\n          </select>\n        </div>\n      </div>\n\n      {/* Video Content */}\n      <div className=\"video-content\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-item\">\n                {currentVideoIndex === index ? (\n                  /* Video Player - Replaces the thumbnail when playing */\n                  <div className=\"inline-video-player\">\n                    <div className=\"youtube-style-layout\">\n                      <div className=\"youtube-video-player\">\n                        {video.videoUrl ? (\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"100%\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            }}\n                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}\n                            onCanPlay={() => setVideoError(null)}\n                            crossOrigin=\"anonymous\"\n                          >\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n                            Your browser does not support the video tag.\n                          </video>\n                        ) : video.videoID ? (\n                          <iframe\n                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                            title={video.title}\n                            frameBorder=\"0\"\n                            allowFullScreen\n                            style={{ width: '100%', height: '100%', border: 'none' }}\n                          ></iframe>\n                        ) : (\n                          <div className=\"video-error\">\n                            <div className=\"error-icon\">⚠️</div>\n                            <h3>Video Unavailable</h3>\n                            <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"youtube-video-info\">\n                        <h1 className=\"youtube-video-title\">{video.title}</h1>\n                        <div className=\"youtube-video-meta\">\n                          <span>{getSubjectName(video.subject)}</span>\n                          <span>•</span>\n                          <span>Class {video.className || video.class}</span>\n                          {video.level && (\n                            <>\n                              <span>•</span>\n                              <span>{video.level}</span>\n                            </>\n                          )}\n                        </div>\n                        <div className=\"youtube-video-actions\">\n                          <button\n                            className={`youtube-action-btn ${commentsExpanded ? 'active' : ''}`}\n                            onClick={() => setCommentsExpanded(!commentsExpanded)}\n                          >\n                            <span>💬</span>\n                            <span>Comments</span>\n                          </button>\n                          <button className=\"youtube-action-btn\">\n                            <span>👍</span>\n                            <span>Like</span>\n                          </button>\n                          <button\n                            className=\"youtube-action-btn\"\n                            onClick={() => setCurrentVideoIndex(null)}\n                          >\n                            <span>✕</span>\n                            <span>Close</span>\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {commentsExpanded && (\n                        <div className=\"youtube-comments-section\">\n                          <div className=\"youtube-comments-header\">\n                            <span>{getCurrentVideoComments().length} Comments</span>\n                          </div>\n\n                          {/* Add Comment */}\n                          <div className=\"youtube-comment-input\">\n                            <div className=\"youtube-comment-avatar\">\n                              {(user?.name || user?.firstName || user?.username || 'Student')?.charAt(0)?.toUpperCase()}\n                            </div>\n                            <div style={{ flex: 1 }}>\n                              <textarea\n                                className=\"youtube-comment-input-field\"\n                                value={newComment}\n                                onChange={(e) => setNewComment(e.target.value)}\n                                placeholder=\"Add a comment...\"\n                                rows=\"1\"\n                                style={{\n                                  minHeight: '20px',\n                                  resize: 'none',\n                                  overflow: 'hidden'\n                                }}\n                                onInput={(e) => {\n                                  e.target.style.height = 'auto';\n                                  e.target.style.height = e.target.scrollHeight + 'px';\n                                }}\n                              />\n                              {newComment.trim() && (\n                                <div className=\"youtube-comment-actions\">\n                                  <button\n                                    className=\"youtube-comment-btn cancel\"\n                                    onClick={() => setNewComment('')}\n                                  >\n                                    Cancel\n                                  </button>\n                                  <button\n                                    className=\"youtube-comment-btn submit\"\n                                    onClick={handleAddComment}\n                                    disabled={!newComment.trim()}\n                                  >\n                                    Comment\n                                  </button>\n                                </div>\n                              )}\n                            </div>\n                          </div>\n\n                          {/* Comments List */}\n                          <div className=\"youtube-comments-list\">\n                            {getCurrentVideoComments().length === 0 ? (\n                              <div style={{ textAlign: 'center', padding: '40px 0', color: '#606060' }}>\n                                <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>\n                                <p>No comments yet. Be the first to share your thoughts!</p>\n                              </div>\n                            ) : (\n                              getCurrentVideoComments().map((comment) => (\n                                <div key={comment._id || comment.id} className=\"youtube-comment\">\n                                  <div className=\"youtube-comment-avatar\">\n                                    {comment.avatar || comment.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                  </div>\n                                  <div className=\"youtube-comment-content\">\n                                    <div className=\"youtube-comment-header\">\n                                      <span className=\"youtube-comment-author\">{comment.author}</span>\n                                      {(comment.userRole === 'admin' || comment.isAdmin) && (\n                                        <MdVerified style={{ color: '#1d9bf0', fontSize: '12px', marginLeft: '4px' }} title=\"Verified Admin\" />\n                                      )}\n                                      <span className=\"youtube-comment-time\">\n                                        {formatTimeAgo(comment.createdAt || comment.timestamp)}\n                                      </span>\n                                    </div>\n                                    <div className=\"youtube-comment-text\">\n                                      {comment.text}\n                                    </div>\n                                    <div className=\"youtube-comment-actions\">\n                                      <button\n                                        onClick={() => handleLikeComment(comment._id || comment.id)}\n                                        className={`youtube-comment-action ${comment.likedBy?.includes(user?._id) ? 'liked' : ''}`}\n                                      >\n                                        <span>{comment.likedBy?.includes(user?._id) ? '👍' : '👍'}</span>\n                                        {comment.likes > 0 && <span>{comment.likes}</span>}\n                                      </button>\n                                      <button className=\"youtube-comment-action\">\n                                        <span>👎</span>\n                                      </button>\n                                      <button className=\"youtube-comment-action\">\n                                        Reply\n                                      </button>\n                                      {comment.user === user?._id && (\n                                        <>\n                                          <button className=\"youtube-comment-action\">\n                                            Edit\n                                          </button>\n                                          <button\n                                            className=\"youtube-comment-action\"\n                                            onClick={() => {\n                                              if (window.confirm('Are you sure you want to delete this comment?')) {\n                                                handleDeleteComment(comment._id || comment.id);\n                                              }\n                                            }}\n                                          >\n                                            Delete\n                                          </button>\n                                        </>\n                                      )}\n                                    </div>\n                                  </div>\n                                </div>\n                              ))\n                            )}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                ) : (\n                  /* Video Card - Shows thumbnail when not playing */\n                  <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                    <div className=\"video-card-thumbnail\">\n                      <img\n                        src={getThumbnailUrl(video)}\n                        alt={video.title}\n                        className=\"thumbnail-image\"\n                        loading=\"lazy\"\n                        onError={(e) => {\n                          if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                            let videoId = video.videoID;\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                            const fallbacks = [\n                              `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n                              `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                              `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                              `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                              'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4='\n                            ];\n                            const currentSrc = e.target.src;\n                            const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop().split('.')[0]));\n                            if (currentIndex < fallbacks.length - 1) {\n                              e.target.src = fallbacks[currentIndex + 1];\n                            }\n                          } else {\n                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n                          }\n                        }}\n                      />\n                      <div className=\"play-overlay\">\n                        <FaPlayCircle className=\"play-icon\" />\n                      </div>\n                      <div className=\"video-duration\">\n                        {video.duration || \"Video\"}\n                      </div>\n                      {video.subtitles && video.subtitles.length > 0 && (\n                        <div className=\"subtitle-badge\">\n                          <TbInfoCircle />\n                          CC\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"video-card-content\">\n                      <h3 className=\"video-title\">{video.title}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                        <span className=\"video-class\">\n                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                            ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)\n                            : `Form ${video.className || video.class}`}\n                        </span>\n                      </div>\n                      <div className=\"video-tags\">\n                        {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                        {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                          <span className=\"shared-tag\">\n                            {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}\n                            {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                              ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)\n                              : `Form ${video.sharedFromClass}`}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAMC,IAAI,GAAGd,WAAW,CAACe,KAAK,IAAI;IAChC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,KAAK,CAAC;MACpDC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,MAAM,CAACC,IAAI,CAACJ,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;;MAEjE;MACA,IAAIA,KAAK,IAAIA,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK,CAACN,IAAI,EAAE;QAC5CE,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,OAAOF,KAAK,CAACK,KAAK,CAACN,IAAI;MACzB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACD,IAAI,EAAE;QACvBE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,OAAOF,KAAK,CAACD,IAAI;MACnB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACM,IAAI,CAACP,IAAI,EAAE;QAC1CE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAOF,KAAK,CAACM,IAAI,CAACP,IAAI;MACxB;MAEAE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMK,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIF,UAAU,EAAE;QACd,IAAI;UACFN,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,OAAOQ,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;QAC/B,CAAC,CAAC,OAAOK,CAAC,EAAE;UACVX,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C;MACF;MAEAD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,OAAO,IAAI;IACb,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,KAAK,CAAC;MACrD,OAAO,IAAI;IACb;EACF,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,KAAK,EAAEK,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4C,eAAe,EAAEC,kBAAkB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM4D,WAAW,GAAGpB,aAAa,KAAK,mBAAmB;;EAEzD;EACA,MAAMqB,eAAe,GAAIC,KAAK,IAAK;IACjC;IACA,IAAIA,KAAK,CAACC,SAAS,IAAID,KAAK,CAACC,SAAS,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACpD5C,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEyC,KAAK,CAACC,SAAS,CAAC;MAC3D,OAAOD,KAAK,CAACC,SAAS;IACxB;;IAEA;IACA,IAAID,KAAK,CAACG,YAAY,IAAIH,KAAK,CAACG,YAAY,CAACD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC1D5C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEyC,KAAK,CAACG,YAAY,CAAC;MACxD,OAAOH,KAAK,CAACG,YAAY;IAC3B;;IAEA;IACA,IAAIH,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAACF,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAChD;MACA,IAAIG,OAAO,GAAGL,KAAK,CAACI,OAAO;;MAE3B;MACA,IAAIJ,KAAK,CAACI,OAAO,CAACE,QAAQ,CAAC,aAAa,CAAC,IAAIN,KAAK,CAACI,OAAO,CAACE,QAAQ,CAAC,UAAU,CAAC,EAAE;QAC/E,MAAMC,KAAK,GAAGP,KAAK,CAACI,OAAO,CAACG,KAAK,CAAC,oDAAoD,CAAC;QACvFF,OAAO,GAAGE,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGP,KAAK,CAACI,OAAO;MAC5C;MAEA9C,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE8C,OAAO,CAAC;MAChE,OAAQ,8BAA6BA,OAAQ,gBAAe;IAC9D;;IAEA;IACA/C,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEyC,KAAK,CAACQ,KAAK,CAAC;IAC3D,OAAO,4cAA4c;EACrd,CAAC;EAED,MAAMC,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,UAAU,GAAG;MACjB,aAAa,EAAEb,WAAW,GAAG,UAAU,GAAG,aAAa;MACvD,SAAS,EAAEA,WAAW,GAAG,YAAY,GAAG,SAAS;MACjD,WAAW,EAAE,WAAW;MACxB,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG,SAAS;MAC9C,gBAAgB,EAAEA,WAAW,GAAG,kBAAkB,GAAG,gBAAgB;MACrE,QAAQ,EAAEA,WAAW,GAAG,OAAO,GAAG,QAAQ;MAC1C,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,WAAW,GAAG,WAAW;MACpD,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,OAAO,GAAG,WAAW;MAChD,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG;IACvC,CAAC;IACD,OAAOa,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO;EACvC,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI5B,iBAAiB,KAAK,IAAI,EAAE,OAAO,EAAE;IACzC,MAAMgB,KAAK,GAAGa,uBAAuB,CAAC7B,iBAAiB,CAAC;IACxD,MAAMqB,OAAO,GAAG,CAAAL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEc,GAAG,MAAId,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEe,EAAE;IACvCzD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE8C,OAAO,CAAC;IACzD/C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE+B,QAAQ,CAAC;IAC/C,OAAOA,QAAQ,CAACe,OAAO,CAAC,IAAI,EAAE;EAChC,CAAC;EAED,MAAMW,aAAa,GAAIC,SAAS,IAAK;IACnC,IAAI,CAACA,SAAS,EAAE,OAAO,UAAU;IACjC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,SAAS,CAAC;IAChC,MAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,GAAGE,IAAI,IAAI,IAAI,CAAC;IAErD,IAAIC,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAE,cAAa;IAChF,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAE,YAAW;IACjF,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAE,WAAU;EACxD,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAChC,UAAU,CAACU,IAAI,CAAC,CAAC,IAAIlB,iBAAiB,KAAK,IAAI,EAAE;IAEtD,MAAMgB,KAAK,GAAGa,uBAAuB,CAAC7B,iBAAiB,CAAC;IAExD1B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEH,IAAI,CAAC;;IAEtD;IACA,MAAMqE,QAAQ,GAAG,CAAArE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,IAAI,MAAItE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,MAAIvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ,MAAIxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,WAAW,KAAI,SAAS;IAClG,MAAMC,QAAQ,GAAG1E,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuE,SAAS,IAAIvE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE2E,QAAQ,GAC7C,GAAE3E,IAAI,CAACuE,SAAU,IAAGvE,IAAI,CAAC2E,QAAS,EAAC,GACpCN,QAAQ;IAEZnE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvCD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,IAAI,CAAC;IAC1CpE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,CAAC;IACpDrE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,QAAQ,CAAC;IAClDzE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ,CAAC;IAClDtE,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,WAAW,CAAC;IACxDvE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuE,QAAQ,CAAC;IAE5C,MAAME,OAAO,GAAG;MACdjB,EAAE,EAAEI,IAAI,CAACD,GAAG,CAAC,CAAC,CAACe,QAAQ,CAAC,CAAC;MACzBC,IAAI,EAAE1C,UAAU,CAACU,IAAI,CAAC,CAAC;MACvBiC,MAAM,EAAEL,QAAQ;MAChB1E,IAAI,EAAE,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,GAAG,MAAI1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,EAAE;MAC3BqB,MAAM,EAAE,CAAAhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,GAAG,MAAI1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,EAAE;MAC7BsB,QAAQ,EAAEjF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkF,IAAI;MACpBC,OAAO,EAAE,CAAAnF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkF,IAAI,MAAK,OAAO;MAC/BrB,SAAS,EAAE,IAAIE,IAAI,CAAC,CAAC,CAACqB,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAItB,IAAI,CAAC,CAAC,CAACqB,WAAW,CAAC,CAAC;MACnCE,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE,EAAE;MACX;MACAC,WAAW,EAAE;QACXlB,IAAI,EAAEI,QAAQ;QACdH,SAAS,EAAEvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS;QAC1BI,QAAQ,EAAE3E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,QAAQ;QACxBH,QAAQ,EAAExE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ;QACxBiB,KAAK,EAAEzF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyF,KAAK;QAClBP,IAAI,EAAElF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkF,IAAI;QAChBQ,MAAM,EAAE,CAAA1F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,MAAM,MAAI1F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2F,cAAc;MAC9C;IACF,CAAC;IAEDzF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyE,OAAO,CAAC;;IAElD;IACA,IAAI;MACF,MAAM3B,OAAO,GAAGL,KAAK,CAACc,GAAG,IAAId,KAAK,CAACe,EAAE;MACrC,MAAMiC,QAAQ,GAAG,MAAMC,KAAK,CAAE,GAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAwB,qBAAoB,EAAE;QAC7GC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAASzF,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D,CAAC;QACDyF,IAAI,EAAExF,IAAI,CAACyF,SAAS,CAAC;UACnBnD,OAAO,EAAEA,OAAO;UAChB6B,IAAI,EAAE1C,UAAU,CAACU,IAAI,CAAC,CAAC;UACvBiC,MAAM,EAAEL,QAAQ;UAChBgB,MAAM,EAAE,CAAA1F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,MAAM,MAAI1F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2F,cAAc,KAAIjB,QAAQ,CAAC2B,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAChFC,SAAS,EAAE,CAAAvG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwG,KAAK,KAAI,SAAS;UACnCC,SAAS,EAAE,CAAAzG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,KAAK,MAAI1G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2G,SAAS;QAC3C,CAAC;MACH,CAAC,CAAC;MAEF,IAAIf,QAAQ,CAACgB,EAAE,EAAE;QACf,MAAMC,YAAY,GAAG,MAAMjB,QAAQ,CAACkB,IAAI,CAAC,CAAC;QAC1C5G,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE0G,YAAY,CAAC;;QAExD;QACA1E,WAAW,CAAC4E,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP,CAAC9D,OAAO,GAAG,CAAC,IAAI8D,IAAI,CAAC9D,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE4D,YAAY,CAACG,IAAI,IAAIH,YAAY;QACzE,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL3G,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1D;QACAgC,WAAW,CAAC4E,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP,CAAC9D,OAAO,GAAG,CAAC,IAAI8D,IAAI,CAAC9D,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE2B,OAAO;QAC/C,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEW,KAAK,CAAC;MAC5D;MACA,MAAMmC,OAAO,GAAGL,KAAK,CAACc,GAAG,IAAId,KAAK,CAACe,EAAE;MACrCxB,WAAW,CAAC4E,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAAC9D,OAAO,GAAG,CAAC,IAAI8D,IAAI,CAAC9D,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE2B,OAAO;MAC/C,CAAC,CAAC,CAAC;IACL;IAEAvC,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAM4E,iBAAiB,GAAIC,SAAS,IAAK;IACvC,IAAI,EAAClH,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0D,GAAG,KAAI,EAAC1D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE2D,EAAE,KAAI/B,iBAAiB,KAAK,IAAI,EAAE;IAE3D,MAAMgB,KAAK,GAAGa,uBAAuB,CAAC7B,iBAAiB,CAAC;IACxD,MAAMqB,OAAO,GAAG,CAAAL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEc,GAAG,MAAId,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEe,EAAE;IACvC,MAAMqB,MAAM,GAAG,CAAAhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,GAAG,MAAI1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,EAAE;IAEpCxB,WAAW,CAAC4E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC9D,OAAO,GAAG,CAAC8D,IAAI,CAAC9D,OAAO,CAAC,IAAI,EAAE,EAAEkE,GAAG,CAACvC,OAAO,IAAI;QAC9C,IAAIA,OAAO,CAACjB,EAAE,KAAKuD,SAAS,IAAItC,OAAO,CAAClB,GAAG,KAAKwD,SAAS,EAAE;UAAA,IAAAE,gBAAA;UACzD,MAAMC,OAAO,IAAAD,gBAAA,GAAGxC,OAAO,CAACW,OAAO,cAAA6B,gBAAA,uBAAfA,gBAAA,CAAiBlE,QAAQ,CAAC8B,MAAM,CAAC;UACjD,OAAO;YACL,GAAGJ,OAAO;YACVU,KAAK,EAAE+B,OAAO,GAAGzC,OAAO,CAACU,KAAK,GAAG,CAAC,GAAGV,OAAO,CAACU,KAAK,GAAG,CAAC;YACtDC,OAAO,EAAE8B,OAAO,GACZzC,OAAO,CAACW,OAAO,CAAC+B,MAAM,CAAC3D,EAAE,IAAIA,EAAE,KAAKqB,MAAM,CAAC,GAC3C,CAAC,IAAIJ,OAAO,CAACW,OAAO,IAAI,EAAE,CAAC,EAAEP,MAAM;UACzC,CAAC;QACH;QACA,OAAOJ,OAAO;MAChB,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM2C,mBAAmB,GAAIL,SAAS,IAAK;IACzC,IAAItF,iBAAiB,KAAK,IAAI,EAAE;IAEhC,MAAMgB,KAAK,GAAGa,uBAAuB,CAAC7B,iBAAiB,CAAC;IACxD,MAAMqB,OAAO,GAAG,CAAAL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEc,GAAG,MAAId,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEe,EAAE;IAEvCxB,WAAW,CAAC4E,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC9D,OAAO,GAAG,CAAC8D,IAAI,CAAC9D,OAAO,CAAC,IAAI,EAAE,EAAEqE,MAAM,CAAC1C,OAAO,IAC7CA,OAAO,CAACjB,EAAE,KAAKuD,SAAS,IAAItC,OAAO,CAAClB,GAAG,KAAKwD,SAC9C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMM,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAM7E,KAAK,GAAGa,uBAAuB,CAACgE,KAAK,CAAC;IAC5C5F,oBAAoB,CAAC4F,KAAK,CAAC;IAC3BxF,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIW,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE8E,QAAQ,KAAK9E,KAAK,CAAC8E,QAAQ,CAACxE,QAAQ,CAAC,eAAe,CAAC,IAAIN,KAAK,CAAC8E,QAAQ,CAACxE,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF;QACA;QACA;QACAN,KAAK,CAAC+E,cAAc,GAAG/E,KAAK,CAAC8E,QAAQ;MACvC,CAAC,CAAC,OAAO5G,KAAK,EAAE;QACdZ,OAAO,CAAC0H,IAAI,CAAC,8CAA8C,CAAC;QAC5DhF,KAAK,CAAC+E,cAAc,GAAG/E,KAAK,CAAC8E,QAAQ;MACvC;IACF;EACF,CAAC;;EAED;EACA,MAAMG,WAAW,GAAG5I,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFiC,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC;MAEdjB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAIyF,QAAQ,GAAG,IAAI;MACnB,IAAI7E,MAAM,GAAG,EAAE;;MAEf;MACA,IAAI;QAAA,IAAA+G,SAAA,EAAAC,UAAA;QACF7H,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjDyF,QAAQ,GAAG,MAAMnG,YAAY,CAAC,CAAC;QAC/BS,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEyF,QAAQ,CAAC;QAE/C,IAAI,CAAAkC,SAAA,GAAAlC,QAAQ,cAAAkC,SAAA,eAARA,SAAA,CAAUE,OAAO,KAAAD,UAAA,GAAInC,QAAQ,cAAAmC,UAAA,eAARA,UAAA,CAAUf,IAAI,EAAE;UACvCjG,MAAM,GAAG6E,QAAQ,CAACoB,IAAI;UACtB9G,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEY,MAAM,CAACkH,MAAM,CAAC;;UAE7E;UACA,IAAIlH,MAAM,CAACkH,MAAM,GAAG,CAAC,EAAE;YACrB/H,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC;YACnDb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,MAAM,CAACC,IAAI,CAACU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3Db,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;YACzCD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC8B,SAAS,CAAC;YAClD3C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACgC,YAAY,CAAC;YACxD7C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACmH,KAAK,CAAC;YAC1ChI,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACoH,QAAQ,CAAC;YAChDjI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACqH,MAAM,CAAC;YAC5ClI,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACsH,SAAS,CAAC;YAClDnI,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACuH,KAAK,CAAC;YAC1CpI,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACwH,UAAU,CAAC;YACpDrI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAACyH,YAAY,CAAC;YACxDtI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC0H,cAAc,CAAC;YAC5DvI,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC2H,KAAK,CAAC;YAC1CxI,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEY,MAAM,CAAC,CAAC,CAAC,CAAC4H,OAAO,CAAC;;YAE9C;YACAzI,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;YACtDY,MAAM,CAAC6H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAACjG,KAAK,EAAE6E,KAAK,KAAK;cAC3CvH,OAAO,CAACC,GAAG,CAAE,SAAQsH,KAAK,GAAG,CAAE,KAAI7E,KAAK,CAACQ,KAAM,IAAG,EAAE;gBAClDP,SAAS,EAAED,KAAK,CAACC,SAAS;gBAC1BE,YAAY,EAAEH,KAAK,CAACG,YAAY;gBAChCmF,KAAK,EAAEtF,KAAK,CAACsF,KAAK;gBAClBC,QAAQ,EAAEvF,KAAK,CAACuF,QAAQ;gBACxBC,MAAM,EAAExF,KAAK,CAACwF,MAAM;gBACpBM,KAAK,EAAE9F,KAAK,CAAC8F,KAAK;gBAClBC,OAAO,EAAE/F,KAAK,CAAC+F;cACjB,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC,OAAO7H,KAAK,EAAE;QACdZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEW,KAAK,CAACgI,OAAO,CAAC;MACtD;;MAEA;MACA,IAAI/H,MAAM,CAACkH,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAc,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACFhJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,MAAMgJ,OAAO,GAAG;YACd3C,KAAK,EAAElF,aAAa;YACpB8H,IAAI,EAAE;UACR,CAAC;UAEDxD,QAAQ,GAAG,MAAMpG,gBAAgB,CAAC2J,OAAO,CAAC;UAC1CjJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyF,QAAQ,CAAC;UAEnD,IAAI,CAAAmD,UAAA,GAAAnD,QAAQ,cAAAmD,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAU/B,IAAI,cAAAgC,eAAA,eAAdA,eAAA,CAAgBhB,OAAO,KAAAiB,UAAA,GAAIrD,QAAQ,cAAAqD,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUjC,IAAI,cAAAkC,eAAA,eAAdA,eAAA,CAAgBlC,IAAI,EAAE;YACnDjG,MAAM,GAAG6E,QAAQ,CAACoB,IAAI,CAACA,IAAI;YAC3B9G,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEY,MAAM,CAACkH,MAAM,CAAC;UACnF;QACF,CAAC,CAAC,OAAOnH,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,KAAK,CAACgI,OAAO,CAAC;QAC1D;MACF;;MAEA;MACA,IAAI/H,MAAM,CAACkH,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAoB,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACFtJ,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5DyF,QAAQ,GAAG,MAAMpG,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACrCU,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEyF,QAAQ,CAAC;UAEhE,IAAI,CAAAyD,UAAA,GAAAzD,QAAQ,cAAAyD,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUrC,IAAI,cAAAsC,eAAA,eAAdA,eAAA,CAAgBtB,OAAO,KAAAuB,UAAA,GAAI3D,QAAQ,cAAA2D,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUvC,IAAI,cAAAwC,eAAA,eAAdA,eAAA,CAAgBxC,IAAI,EAAE;YACnD;YACA,MAAMyC,OAAO,GAAG7D,QAAQ,CAACoB,IAAI,CAACA,IAAI;YAClCjG,MAAM,GAAG0I,OAAO,CAACnC,MAAM,CAACoC,IAAI;cAAA,IAAAC,WAAA;cAAA,OAC1BD,IAAI,CAACN,IAAI,KAAK,OAAO,IACrBM,IAAI,CAAChC,QAAQ,IACbgC,IAAI,CAAC1G,OAAO,MAAA2G,WAAA,GACZD,IAAI,CAACtG,KAAK,cAAAuG,WAAA,uBAAVA,WAAA,CAAYC,WAAW,CAAC,CAAC,CAAC1G,QAAQ,CAAC,OAAO,CAAC;YAAA,CAC7C,CAAC;YACDhD,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,MAAM,CAACkH,MAAM,CAAC;UAC1E;QACF,CAAC,CAAC,OAAOnH,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEW,KAAK,CAACgI,OAAO,CAAC;QACvE;MACF;;MAEA;MACA,IAAI/H,MAAM,CAACkH,MAAM,GAAG,CAAC,EAAE;QACrB,MAAM4B,QAAQ,GAAG9I,MAAM,CAACuG,MAAM,CAAC1E,KAAK,IAAI;UACtC,MAAMkH,YAAY,GAAGxI,aAAa,KAAK,KAAK,IACxBsB,KAAK,CAAC4D,KAAK,KAAKlF,aAAa,IAC7B,CAACsB,KAAK,CAAC4D,KAAK,CAAC,CAAC;;UAElC,MAAMuD,YAAY,GAAGvI,aAAa,KAAK,KAAK,IACxBoB,KAAK,CAAC+D,SAAS,KAAKnF,aAAa,IACjCoB,KAAK,CAAC8D,KAAK,KAAKlF,aAAa,IAC7B,CAACoB,KAAK,CAAC+D,SAAS,CAAC,CAAC;;UAEtC,MAAMqD,cAAc,GAAGtI,eAAe,KAAK,KAAK,IAC1BkB,KAAK,CAACU,OAAO,KAAK5B,eAAe,IACjC,CAACkB,KAAK,CAACU,OAAO,CAAC,CAAC;;UAEtC,OAAOwG,YAAY,IAAIC,YAAY,IAAIC,cAAc;QACvD,CAAC,CAAC;QAEFhJ,SAAS,CAAC6I,QAAQ,CAAC;QACnB3J,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE0J,QAAQ,CAAC5B,MAAM,EAAE,QAAQ,CAAC;QAErE,IAAI4B,QAAQ,CAAC5B,MAAM,KAAK,CAAC,EAAE;UACzB9G,QAAQ,CAAC,wEAAwE,CAAC;QACpF;MACF,CAAC,MAAM;QACL;QACAjB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpED,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrCD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAElCgB,QAAQ,CAAC,yFAAyF,CAAC;QACnGH,SAAS,CAAC,EAAE,CAAC;MACf;IAEF,CAAC,CAAC,OAAOiJ,GAAG,EAAE;MACZ/J,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEmJ,GAAG,CAAC;MACvD9I,QAAQ,CAAC,0DAA0D,CAAC;MACpEH,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACI,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEnD;EACA,MAAM+B,uBAAuB,GAAGzE,OAAO,CAAC,MAAM;IAC5C,IAAI6K,QAAQ,GAAG9I,MAAM,CAACuG,MAAM,CAAC1E,KAAK,IAAI;MAAA,IAAAsH,YAAA,EAAAC,cAAA,EAAAC,YAAA;MACpC,MAAMC,aAAa,GAAG,CAACjJ,UAAU,MAAA8I,YAAA,GAC/BtH,KAAK,CAACQ,KAAK,cAAA8G,YAAA,uBAAXA,YAAA,CAAaN,WAAW,CAAC,CAAC,CAAC1G,QAAQ,CAAC9B,UAAU,CAACwI,WAAW,CAAC,CAAC,CAAC,OAAAO,cAAA,GAC7DvH,KAAK,CAACU,OAAO,cAAA6G,cAAA,uBAAbA,cAAA,CAAeP,WAAW,CAAC,CAAC,CAAC1G,QAAQ,CAAC9B,UAAU,CAACwI,WAAW,CAAC,CAAC,CAAC,OAAAQ,YAAA,GAC/DxH,KAAK,CAAC0H,KAAK,cAAAF,YAAA,uBAAXA,YAAA,CAAaR,WAAW,CAAC,CAAC,CAAC1G,QAAQ,CAAC9B,UAAU,CAACwI,WAAW,CAAC,CAAC,CAAC;MAE/D,MAAME,YAAY,GAAGxI,aAAa,KAAK,KAAK,IAAIsB,KAAK,CAAC4D,KAAK,KAAKlF,aAAa;MAC7E,MAAMyI,YAAY,GAAGvI,aAAa,KAAK,KAAK,IAAIoB,KAAK,CAAC+D,SAAS,KAAKnF,aAAa,IAAIoB,KAAK,CAAC8D,KAAK,KAAKlF,aAAa;MAClH,MAAMwI,cAAc,GAAGtI,eAAe,KAAK,KAAK,IAAIkB,KAAK,CAACU,OAAO,KAAK5B,eAAe;MAErF,OAAO2I,aAAa,IAAIP,YAAY,IAAIC,YAAY,IAAIC,cAAc;IACxE,CAAC,CAAC;IAEF,OAAOH,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI1G,IAAI,CAAC0G,CAAC,CAACpF,SAAS,CAAC,GAAG,IAAItB,IAAI,CAACyG,CAAC,CAACnF,SAAS,CAAC,CAAC;EAC/E,CAAC,EAAE,CAACtE,MAAM,EAAEK,UAAU,EAAEE,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEvE;EACA3C,SAAS,CAAC,MAAM;IACd8I,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA9I,SAAS,CAAC,MAAM;IACd8I,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACvG,aAAa,EAAEE,aAAa,EAAEE,eAAe,EAAEmG,WAAW,CAAC,CAAC;EAEhE,oBACElI,OAAA;IAAKgH,SAAS,EAAC,yBAAyB;IAAA+D,QAAA,gBACtC/K,OAAA;MAAKgH,SAAS,EAAC,sBAAsB;MAAA+D,QAAA,gBACnC/K,OAAA;QAAA+K,QAAA,EAAKhI,WAAW,GAAG,iBAAiB,GAAG;MAAe;QAAAiI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5DnL,OAAA;QAAA+K,QAAA,EAAIhI,WAAW,GAAG,iCAAiC,GAAG;MAAoC;QAAAiI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAGNnL,OAAA;MAAKgH,SAAS,EAAC,gBAAgB;MAAA+D,QAAA,gBAC7B/K,OAAA;QAAKgH,SAAS,EAAC,gBAAgB;QAAA+D,QAAA,eAC7B/K,OAAA;UACEyJ,IAAI,EAAC,MAAM;UACX2B,WAAW,EAAErI,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;UAClEsI,KAAK,EAAE5J,UAAW;UAClB6J,QAAQ,EAAGpK,CAAC,IAAKQ,aAAa,CAACR,CAAC,CAACqK,MAAM,CAACF,KAAK,CAAE;UAC/CrE,SAAS,EAAC;QAAc;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnL,OAAA;QAAKgH,SAAS,EAAC,gBAAgB;QAAA+D,QAAA,gBAC7B/K,OAAA;UACEqL,KAAK,EAAE1J,aAAc;UACrB2J,QAAQ,EAAGpK,CAAC,IAAKU,gBAAgB,CAACV,CAAC,CAACqK,MAAM,CAACF,KAAK,CAAE;UAClDrE,SAAS,EAAC,eAAe;UAAA+D,QAAA,gBAEzB/K,OAAA;YAAQqL,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAEhI,WAAW,GAAG,QAAQ,GAAG;UAAS;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrEnL,OAAA;YAAQqL,KAAK,EAAC,WAAW;YAAAN,QAAA,EAAEhI,WAAW,GAAG,WAAW,GAAG;UAAW;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5EnL,OAAA;YAAQqL,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAEhI,WAAW,GAAG,KAAK,GAAG;UAAU;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAETnL,OAAA;UACEqL,KAAK,EAAExJ,aAAc;UACrByJ,QAAQ,EAAGpK,CAAC,IAAKY,gBAAgB,CAACZ,CAAC,CAACqK,MAAM,CAACF,KAAK,CAAE;UAClDrE,SAAS,EAAC,eAAe;UAAA+D,QAAA,eAEzB/K,OAAA;YAAQqL,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAEhI,WAAW,GAAG,eAAe,GAAG;UAAa;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtE,CAAC,eAETnL,OAAA;UACEqL,KAAK,EAAEtJ,eAAgB;UACvBuJ,QAAQ,EAAGpK,CAAC,IAAKc,kBAAkB,CAACd,CAAC,CAACqK,MAAM,CAACF,KAAK,CAAE;UACpDrE,SAAS,EAAC,eAAe;UAAA+D,QAAA,eAEzB/K,OAAA;YAAQqL,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAEhI,WAAW,GAAG,aAAa,GAAG;UAAc;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnL,OAAA;MAAKgH,SAAS,EAAC,eAAe;MAAA+D,QAAA,EAC3BzJ,OAAO,gBACNtB,OAAA;QAAKgH,SAAS,EAAC,eAAe;QAAA+D,QAAA,gBAC5B/K,OAAA;UAAKgH,SAAS,EAAC;QAAiB;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCnL,OAAA;UAAA+K,QAAA,EAAIhI,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJhK,KAAK,gBACPnB,OAAA;QAAKgH,SAAS,EAAC,aAAa;QAAA+D,QAAA,gBAC1B/K,OAAA,CAACL,eAAe;UAACqH,SAAS,EAAC;QAAY;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CnL,OAAA;UAAA+K,QAAA,EAAKhI,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7EnL,OAAA;UAAA+K,QAAA,EAAI5J;QAAK;UAAA6J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdnL,OAAA;UAAQwL,OAAO,EAAEtD,WAAY;UAAClB,SAAS,EAAC,WAAW;UAAA+D,QAAA,EAChDhI,WAAW,GAAG,aAAa,GAAG;QAAW;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJrH,uBAAuB,CAACwE,MAAM,GAAG,CAAC,gBACpCtI,OAAA;QAAKgH,SAAS,EAAC,aAAa;QAAA+D,QAAA,EACzBjH,uBAAuB,CAAC0D,GAAG,CAAC,CAACvE,KAAK,EAAE6E,KAAK;UAAA,IAAA2D,IAAA,EAAAC,WAAA;UAAA,oBACxC1L,OAAA;YAAiBgH,SAAS,EAAC,YAAY;YAAA+D,QAAA,EACpC9I,iBAAiB,KAAK6F,KAAK;YAAA;YAC1B;YACA9H,OAAA;cAAKgH,SAAS,EAAC,qBAAqB;cAAA+D,QAAA,eAClC/K,OAAA;gBAAKgH,SAAS,EAAC,sBAAsB;gBAAA+D,QAAA,gBACnC/K,OAAA;kBAAKgH,SAAS,EAAC,sBAAsB;kBAAA+D,QAAA,EAClC9H,KAAK,CAAC8E,QAAQ,gBACb/H,OAAA;oBACE2L,GAAG,EAAGA,GAAG,IAAKvJ,WAAW,CAACuJ,GAAG,CAAE;oBAC/BC,QAAQ;oBACRC,QAAQ;oBACRC,WAAW;oBACXC,OAAO,EAAC,UAAU;oBAClBC,KAAK,EAAC,MAAM;oBACZC,MAAM,EAAC,MAAM;oBACbxD,MAAM,EAAEzF,eAAe,CAACC,KAAK,CAAE;oBAC/BiJ,KAAK,EAAE;sBACLF,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdE,eAAe,EAAE,MAAM;sBACvBC,SAAS,EAAE;oBACb,CAAE;oBACFC,OAAO,EAAGnL,CAAC,IAAKoB,aAAa,CAAE,yBAAwBW,KAAK,CAACQ,KAAM,EAAC,CAAE;oBACtE6I,SAAS,EAAEA,CAAA,KAAMhK,aAAa,CAAC,IAAI,CAAE;oBACrCiK,WAAW,EAAC,WAAW;oBAAAxB,QAAA,gBAEvB/K,OAAA;sBAAQwM,GAAG,EAAEvJ,KAAK,CAAC+E,cAAc,IAAI/E,KAAK,CAAC8E,QAAS;sBAAC0B,IAAI,EAAC;oBAAW;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACvElI,KAAK,CAACwJ,SAAS,IAAIxJ,KAAK,CAACwJ,SAAS,CAACnE,MAAM,GAAG,CAAC,IAAIrF,KAAK,CAACwJ,SAAS,CAACjF,GAAG,CAAC,CAACkF,QAAQ,EAAE5E,KAAK,kBACpF9H,OAAA;sBAEE2M,IAAI,EAAC,WAAW;sBAChBH,GAAG,EAAEE,QAAQ,CAACE,GAAI;sBAClBC,OAAO,EAAEH,QAAQ,CAACI,QAAS;sBAC3BC,KAAK,EAAEL,QAAQ,CAACM,YAAa;sBAC7BC,OAAO,EAAEP,QAAQ,CAACQ,SAAS,IAAIpF,KAAK,KAAK;oBAAE,GALrC,GAAE4E,QAAQ,CAACI,QAAS,IAAGhF,KAAM,EAAC;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,GACNlI,KAAK,CAACI,OAAO,gBACfrD,OAAA;oBACEwM,GAAG,EAAG,iCAAgCvJ,KAAK,CAACI,OAAQ,mBAAmB;oBACvEI,KAAK,EAAER,KAAK,CAACQ,KAAM;oBACnB0J,WAAW,EAAC,GAAG;oBACfC,eAAe;oBACflB,KAAK,EAAE;sBAAEF,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE,MAAM;sBAAEoB,MAAM,EAAE;oBAAO;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,gBAEVnL,OAAA;oBAAKgH,SAAS,EAAC,aAAa;oBAAA+D,QAAA,gBAC1B/K,OAAA;sBAAKgH,SAAS,EAAC,YAAY;sBAAA+D,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpCnL,OAAA;sBAAA+K,QAAA,EAAI;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1BnL,OAAA;sBAAA+K,QAAA,EAAI1I,UAAU,IAAI;oBAA4C;sBAAA2I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENnL,OAAA;kBAAKgH,SAAS,EAAC,oBAAoB;kBAAA+D,QAAA,gBACjC/K,OAAA;oBAAIgH,SAAS,EAAC,qBAAqB;oBAAA+D,QAAA,EAAE9H,KAAK,CAACQ;kBAAK;oBAAAuH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtDnL,OAAA;oBAAKgH,SAAS,EAAC,oBAAoB;oBAAA+D,QAAA,gBACjC/K,OAAA;sBAAA+K,QAAA,EAAOrH,cAAc,CAACT,KAAK,CAACU,OAAO;oBAAC;sBAAAqH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC5CnL,OAAA;sBAAA+K,QAAA,EAAM;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACdnL,OAAA;sBAAA+K,QAAA,GAAM,QAAM,EAAC9H,KAAK,CAAC+D,SAAS,IAAI/D,KAAK,CAAC8D,KAAK;oBAAA;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAClDlI,KAAK,CAAC4D,KAAK,iBACV7G,OAAA,CAAAE,SAAA;sBAAA6K,QAAA,gBACE/K,OAAA;wBAAA+K,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACdnL,OAAA;wBAAA+K,QAAA,EAAO9H,KAAK,CAAC4D;sBAAK;wBAAAmE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,eAC1B,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNnL,OAAA;oBAAKgH,SAAS,EAAC,uBAAuB;oBAAA+D,QAAA,gBACpC/K,OAAA;sBACEgH,SAAS,EAAG,sBAAqBrE,gBAAgB,GAAG,QAAQ,GAAG,EAAG,EAAE;sBACpE6I,OAAO,EAAEA,CAAA,KAAM5I,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;sBAAAoI,QAAA,gBAEtD/K,OAAA;wBAAA+K,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACfnL,OAAA;wBAAA+K,QAAA,EAAM;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACTnL,OAAA;sBAAQgH,SAAS,EAAC,oBAAoB;sBAAA+D,QAAA,gBACpC/K,OAAA;wBAAA+K,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACfnL,OAAA;wBAAA+K,QAAA,EAAM;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACTnL,OAAA;sBACEgH,SAAS,EAAC,oBAAoB;sBAC9BwE,OAAO,EAAEA,CAAA,KAAMtJ,oBAAoB,CAAC,IAAI,CAAE;sBAAA6I,QAAA,gBAE1C/K,OAAA;wBAAA+K,QAAA,EAAM;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACdnL,OAAA;wBAAA+K,QAAA,EAAM;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLxI,gBAAgB,iBACf3C,OAAA;kBAAKgH,SAAS,EAAC,0BAA0B;kBAAA+D,QAAA,gBACvC/K,OAAA;oBAAKgH,SAAS,EAAC,yBAAyB;oBAAA+D,QAAA,eACtC/K,OAAA;sBAAA+K,QAAA,GAAOlH,uBAAuB,CAAC,CAAC,CAACyE,MAAM,EAAC,WAAS;oBAAA;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC,eAGNnL,OAAA;oBAAKgH,SAAS,EAAC,uBAAuB;oBAAA+D,QAAA,gBACpC/K,OAAA;sBAAKgH,SAAS,EAAC,wBAAwB;sBAAA+D,QAAA,GAAAU,IAAA,GACnC,CAAApL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,IAAI,MAAItE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,MAAIvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,QAAQ,KAAI,SAAS,cAAA4G,IAAA,wBAAAC,WAAA,GAA7DD,IAAA,CAAgE/E,MAAM,CAAC,CAAC,CAAC,cAAAgF,WAAA,uBAAzEA,WAAA,CAA2E/E,WAAW,CAAC;oBAAC;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF,CAAC,eACNnL,OAAA;sBAAKkM,KAAK,EAAE;wBAAEoB,IAAI,EAAE;sBAAE,CAAE;sBAAAvC,QAAA,gBACtB/K,OAAA;wBACEgH,SAAS,EAAC,6BAA6B;wBACvCqE,KAAK,EAAE5I,UAAW;wBAClB6I,QAAQ,EAAGpK,CAAC,IAAKwB,aAAa,CAACxB,CAAC,CAACqK,MAAM,CAACF,KAAK,CAAE;wBAC/CD,WAAW,EAAC,kBAAkB;wBAC9BmC,IAAI,EAAC,GAAG;wBACRrB,KAAK,EAAE;0BACLsB,SAAS,EAAE,MAAM;0BACjBC,MAAM,EAAE,MAAM;0BACdC,QAAQ,EAAE;wBACZ,CAAE;wBACFC,OAAO,EAAGzM,CAAC,IAAK;0BACdA,CAAC,CAACqK,MAAM,CAACW,KAAK,CAACD,MAAM,GAAG,MAAM;0BAC9B/K,CAAC,CAACqK,MAAM,CAACW,KAAK,CAACD,MAAM,GAAG/K,CAAC,CAACqK,MAAM,CAACqC,YAAY,GAAG,IAAI;wBACtD;sBAAE;wBAAA5C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EACD1I,UAAU,CAACU,IAAI,CAAC,CAAC,iBAChBnD,OAAA;wBAAKgH,SAAS,EAAC,yBAAyB;wBAAA+D,QAAA,gBACtC/K,OAAA;0BACEgH,SAAS,EAAC,4BAA4B;0BACtCwE,OAAO,EAAEA,CAAA,KAAM9I,aAAa,CAAC,EAAE,CAAE;0BAAAqI,QAAA,EAClC;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACTnL,OAAA;0BACEgH,SAAS,EAAC,4BAA4B;0BACtCwE,OAAO,EAAE/G,gBAAiB;0BAC1BoJ,QAAQ,EAAE,CAACpL,UAAU,CAACU,IAAI,CAAC,CAAE;0BAAA4H,QAAA,EAC9B;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNnL,OAAA;oBAAKgH,SAAS,EAAC,uBAAuB;oBAAA+D,QAAA,EACnClH,uBAAuB,CAAC,CAAC,CAACyE,MAAM,KAAK,CAAC,gBACrCtI,OAAA;sBAAKkM,KAAK,EAAE;wBAAE4B,SAAS,EAAE,QAAQ;wBAAEC,OAAO,EAAE,QAAQ;wBAAEC,KAAK,EAAE;sBAAU,CAAE;sBAAAjD,QAAA,gBACvE/K,OAAA;wBAAKkM,KAAK,EAAE;0BAAE+B,QAAQ,EAAE,MAAM;0BAAEC,YAAY,EAAE;wBAAO,CAAE;wBAAAnD,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChEnL,OAAA;wBAAA+K,QAAA,EAAG;sBAAqD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,GAENtH,uBAAuB,CAAC,CAAC,CAAC2D,GAAG,CAAEvC,OAAO;sBAAA,IAAAkJ,eAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,iBAAA;sBAAA,oBACpCtO,OAAA;wBAAqCgH,SAAS,EAAC,iBAAiB;wBAAA+D,QAAA,gBAC9D/K,OAAA;0BAAKgH,SAAS,EAAC,wBAAwB;0BAAA+D,QAAA,EACpC9F,OAAO,CAACc,MAAM,MAAAoI,eAAA,GAAIlJ,OAAO,CAACG,MAAM,cAAA+I,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBzH,MAAM,CAAC,CAAC,CAAC,cAAA0H,qBAAA,uBAAzBA,qBAAA,CAA2BzH,WAAW,CAAC,CAAC,KAAI;wBAAG;0BAAAqE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D,CAAC,eACNnL,OAAA;0BAAKgH,SAAS,EAAC,yBAAyB;0BAAA+D,QAAA,gBACtC/K,OAAA;4BAAKgH,SAAS,EAAC,wBAAwB;4BAAA+D,QAAA,gBACrC/K,OAAA;8BAAMgH,SAAS,EAAC,wBAAwB;8BAAA+D,QAAA,EAAE9F,OAAO,CAACG;4BAAM;8BAAA4F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,EAC/D,CAAClG,OAAO,CAACK,QAAQ,KAAK,OAAO,IAAIL,OAAO,CAACO,OAAO,kBAC/CxF,OAAA,CAACJ,UAAU;8BAACsM,KAAK,EAAE;gCAAE8B,KAAK,EAAE,SAAS;gCAAEC,QAAQ,EAAE,MAAM;gCAAEM,UAAU,EAAE;8BAAM,CAAE;8BAAC9K,KAAK,EAAC;4BAAgB;8BAAAuH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CACvG,eACDnL,OAAA;8BAAMgH,SAAS,EAAC,sBAAsB;8BAAA+D,QAAA,EACnC9G,aAAa,CAACgB,OAAO,CAACS,SAAS,IAAIT,OAAO,CAACf,SAAS;4BAAC;8BAAA8G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAClD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eACNnL,OAAA;4BAAKgH,SAAS,EAAC,sBAAsB;4BAAA+D,QAAA,EAClC9F,OAAO,CAACE;0BAAI;4BAAA6F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACNnL,OAAA;4BAAKgH,SAAS,EAAC,yBAAyB;4BAAA+D,QAAA,gBACtC/K,OAAA;8BACEwL,OAAO,EAAEA,CAAA,KAAMlE,iBAAiB,CAACrC,OAAO,CAAClB,GAAG,IAAIkB,OAAO,CAACjB,EAAE,CAAE;8BAC5DgD,SAAS,EAAG,0BAAyB,CAAAqH,iBAAA,GAAApJ,OAAO,CAACW,OAAO,cAAAyI,iBAAA,eAAfA,iBAAA,CAAiB9K,QAAQ,CAAClD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,GAAG,CAAC,GAAG,OAAO,GAAG,EAAG,EAAE;8BAAAgH,QAAA,gBAE3F/K,OAAA;gCAAA+K,QAAA,EAAO,CAAAuD,iBAAA,GAAArJ,OAAO,CAACW,OAAO,cAAA0I,iBAAA,eAAfA,iBAAA,CAAiB/K,QAAQ,CAAClD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,GAAG,CAAC,GAAG,IAAI,GAAG;8BAAI;gCAAAiH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,EAChElG,OAAO,CAACU,KAAK,GAAG,CAAC,iBAAI3F,OAAA;gCAAA+K,QAAA,EAAO9F,OAAO,CAACU;8BAAK;gCAAAqF,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5C,CAAC,eACTnL,OAAA;8BAAQgH,SAAS,EAAC,wBAAwB;8BAAA+D,QAAA,eACxC/K,OAAA;gCAAA+K,QAAA,EAAM;8BAAE;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC,eACTnL,OAAA;8BAAQgH,SAAS,EAAC,wBAAwB;8BAAA+D,QAAA,EAAC;4BAE3C;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,EACRlG,OAAO,CAAC5E,IAAI,MAAKA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,GAAG,kBACzB/D,OAAA,CAAAE,SAAA;8BAAA6K,QAAA,gBACE/K,OAAA;gCAAQgH,SAAS,EAAC,wBAAwB;gCAAA+D,QAAA,EAAC;8BAE3C;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eACTnL,OAAA;gCACEgH,SAAS,EAAC,wBAAwB;gCAClCwE,OAAO,EAAEA,CAAA,KAAM;kCACb,IAAIgD,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;oCACnE7G,mBAAmB,CAAC3C,OAAO,CAAClB,GAAG,IAAIkB,OAAO,CAACjB,EAAE,CAAC;kCAChD;gCACF,CAAE;gCAAA+G,QAAA,EACH;8BAED;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC;4BAAA,eACT,CACH;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA,GAjDElG,OAAO,CAAClB,GAAG,IAAIkB,OAAO,CAACjB,EAAE;wBAAAgH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAkD9B,CAAC;oBAAA,CACP;kBACF;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACAnL,OAAA;cAAKgH,SAAS,EAAC,YAAY;cAACwE,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAACC,KAAK,CAAE;cAAAiD,QAAA,gBAChE/K,OAAA;gBAAKgH,SAAS,EAAC,sBAAsB;gBAAA+D,QAAA,gBACnC/K,OAAA;kBACEwM,GAAG,EAAExJ,eAAe,CAACC,KAAK,CAAE;kBAC5ByL,GAAG,EAAEzL,KAAK,CAACQ,KAAM;kBACjBuD,SAAS,EAAC,iBAAiB;kBAC3B1F,OAAO,EAAC,MAAM;kBACd+K,OAAO,EAAGnL,CAAC,IAAK;oBACd,IAAI+B,KAAK,CAACI,OAAO,IAAI,CAACJ,KAAK,CAACI,OAAO,CAACE,QAAQ,CAAC,eAAe,CAAC,EAAE;sBAC7D,IAAID,OAAO,GAAGL,KAAK,CAACI,OAAO;sBAC3B,MAAMG,KAAK,GAAGF,OAAO,CAACE,KAAK,CAAC,oDAAoD,CAAC;sBACjFF,OAAO,GAAGE,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGF,OAAO;sBACpC,MAAMqL,SAAS,GAAG,CACf,8BAA6BrL,OAAQ,oBAAmB,EACxD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,4cAA4c,CAC7c;sBACD,MAAMsL,UAAU,GAAG1N,CAAC,CAACqK,MAAM,CAACiB,GAAG;sBAC/B,MAAMqC,YAAY,GAAGF,SAAS,CAACG,SAAS,CAAClC,GAAG,IAAIgC,UAAU,CAACrL,QAAQ,CAACqJ,GAAG,CAACmC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACxG,IAAIF,YAAY,GAAGF,SAAS,CAACrG,MAAM,GAAG,CAAC,EAAE;wBACvCpH,CAAC,CAACqK,MAAM,CAACiB,GAAG,GAAGmC,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;sBAC5C;oBACF,CAAC,MAAM;sBACL3N,CAAC,CAACqK,MAAM,CAACiB,GAAG,GAAG,4cAA4c;oBAC7d;kBACF;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFnL,OAAA;kBAAKgH,SAAS,EAAC,cAAc;kBAAA+D,QAAA,eAC3B/K,OAAA,CAACR,YAAY;oBAACwH,SAAS,EAAC;kBAAW;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNnL,OAAA;kBAAKgH,SAAS,EAAC,gBAAgB;kBAAA+D,QAAA,EAC5B9H,KAAK,CAACgM,QAAQ,IAAI;gBAAO;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EACLlI,KAAK,CAACwJ,SAAS,IAAIxJ,KAAK,CAACwJ,SAAS,CAACnE,MAAM,GAAG,CAAC,iBAC5CtI,OAAA;kBAAKgH,SAAS,EAAC,gBAAgB;kBAAA+D,QAAA,gBAC7B/K,OAAA,CAACN,YAAY;oBAAAsL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,MAElB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENnL,OAAA;gBAAKgH,SAAS,EAAC,oBAAoB;gBAAA+D,QAAA,gBACjC/K,OAAA;kBAAIgH,SAAS,EAAC,aAAa;kBAAA+D,QAAA,EAAE9H,KAAK,CAACQ;gBAAK;kBAAAuH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9CnL,OAAA;kBAAKgH,SAAS,EAAC,YAAY;kBAAA+D,QAAA,gBACzB/K,OAAA;oBAAMgH,SAAS,EAAC,eAAe;oBAAA+D,QAAA,EAAErH,cAAc,CAACT,KAAK,CAACU,OAAO;kBAAC;oBAAAqH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtEnL,OAAA;oBAAMgH,SAAS,EAAC,aAAa;oBAAA+D,QAAA,EAC1BpJ,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEoB,WAAW,GAAI,aAAYE,KAAK,CAAC+D,SAAS,IAAI/D,KAAK,CAAC8D,KAAM,EAAC,GAAI,SAAQ9D,KAAK,CAAC+D,SAAS,IAAI/D,KAAK,CAAC8D,KAAM,EAAC,GACvG,QAAO9D,KAAK,CAAC+D,SAAS,IAAI/D,KAAK,CAAC8D,KAAM;kBAAC;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNnL,OAAA;kBAAKgH,SAAS,EAAC,YAAY;kBAAA+D,QAAA,GACxB9H,KAAK,CAAC0H,KAAK,iBAAI3K,OAAA;oBAAMgH,SAAS,EAAC,WAAW;oBAAA+D,QAAA,EAAE9H,KAAK,CAAC0H;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC/DlI,KAAK,CAACiM,eAAe,IAAIjM,KAAK,CAACiM,eAAe,MAAMjM,KAAK,CAAC+D,SAAS,IAAI/D,KAAK,CAAC8D,KAAK,CAAC,iBAClF/G,OAAA;oBAAMgH,SAAS,EAAC,YAAY;oBAAA+D,QAAA,GACzBhI,WAAW,GAAG,qBAAqB,GAAG,cAAc,EACpDpB,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEoB,WAAW,GAAI,aAAYE,KAAK,CAACiM,eAAgB,EAAC,GAAI,SAAQjM,KAAK,CAACiM,eAAgB,EAAC,GACrF,QAAOjM,KAAK,CAACiM,eAAgB,EAAC;kBAAA;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN,GAnROrD,KAAK;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoRV,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENnL,OAAA;QAAKgH,SAAS,EAAC,aAAa;QAAA+D,QAAA,gBAC1B/K,OAAA,CAACP,eAAe;UAACuH,SAAS,EAAC;QAAY;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CnL,OAAA;UAAA+K,QAAA,EAAKhI,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1EnL,OAAA;UAAA+K,QAAA,EAAIhI,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJnL,OAAA;UAAGgH,SAAS,EAAC,YAAY;UAAA+D,QAAA,EAAEhI,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/K,EAAA,CA3zBID,YAAY;EAAA,QAEHZ,WAAW;AAAA;AAAA4P,EAAA,GAFpBhP,YAAY;AA6zBlB,eAAeA,YAAY;AAAC,IAAAgP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}