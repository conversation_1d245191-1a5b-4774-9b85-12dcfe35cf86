{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoLessons = () => {\n  _s();\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.user) {\n        console.log('✅ Found user in state.user');\n        return state.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = video => {\n    if (video.thumbnailUrl) return video.thumbnailUrl;\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      // Extract video ID from various YouTube URL formats\n      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n      videoId = match ? match[1] : videoId;\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    // Use a working placeholder image\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n  };\n  const getSubjectName = subject => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('🎥 Attempting to fetch videos from database...');\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        var _response, _response2;\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n        if ((_response = response) !== null && _response !== void 0 && _response.success && (_response2 = response) !== null && _response2 !== void 0 && _response2.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          var _response3, _response3$data, _response4, _response4$data;\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n          if ((_response3 = response) !== null && _response3 !== void 0 && (_response3$data = _response3.data) !== null && _response3$data !== void 0 && _response3$data.success && (_response4 = response) !== null && _response4 !== void 0 && (_response4$data = _response4.data) !== null && _response4$data !== void 0 && _response4$data.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          var _response5, _response5$data, _response6, _response6$data;\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n          if ((_response5 = response) !== null && _response5 !== void 0 && (_response5$data = _response5.data) !== null && _response5$data !== void 0 && _response5$data.success && (_response6 = response) !== null && _response6 !== void 0 && (_response6$data = _response6.data) !== null && _response6$data !== void 0 && _response6$data.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item => {\n              var _item$title;\n              return item.type === 'video' || item.videoUrl || item.videoID || ((_item$title = item.title) === null || _item$title === void 0 ? void 0 : _item$title.toLowerCase().includes('video'));\n            });\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel || !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass || !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject || !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      var _video$title, _video$subject, _video$topic;\n      const matchesSearch = !searchTerm || ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isKiswahili ? 'Masomo ya Video' : 'Video Lessons'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: isKiswahili ? 'Tafuta video...' : 'Search videos...',\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLevel,\n          onChange: e => setSelectedLevel(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"primary\",\n            children: isKiswahili ? 'Msingi' : 'Primary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"secondary\",\n            children: isKiswahili ? 'Sekondari' : 'Secondary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advance\",\n            children: isKiswahili ? 'Juu' : 'Advanced'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedClass,\n          onChange: e => setSelectedClass(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedSubject,\n          onChange: e => setSelectedSubject(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card\",\n            onClick: () => handleShowVideo(index),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card-thumbnail\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getThumbnailUrl(video),\n                alt: video.title,\n                className: \"thumbnail-image\",\n                loading: \"lazy\",\n                onError: e => {\n                  if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                    let videoId = video.videoID;\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                    const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                    const currentSrc = e.target.src;\n                    const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                    if (currentIndex < fallbacks.length - 1) {\n                      e.target.src = fallbacks[currentIndex + 1];\n                    } else {\n                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n                    }\n                  } else {\n                    e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"play-overlay\",\n                children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                  className: \"play-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-duration\",\n                children: video.duration || \"Video\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 21\n              }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"subtitle-badge\",\n                children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 25\n                }, this), \"CC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"video-title\",\n                children: video.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-subject\",\n                  children: getSubjectName(video.subject)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-class\",\n                  children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-tags\",\n                children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"topic-tag\",\n                  children: video.topic\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 39\n                }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"shared-tag\",\n                  children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 17\n          }, this), currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-video-player\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"youtube-style-layout\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-player\",\n                children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                  ref: ref => setVideoRef(ref),\n                  controls: true,\n                  autoPlay: true,\n                  playsInline: true,\n                  preload: \"metadata\",\n                  width: \"100%\",\n                  height: \"100%\",\n                  poster: getThumbnailUrl(video),\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    backgroundColor: '#000',\n                    objectFit: 'contain'\n                  },\n                  onError: e => setVideoError(`Failed to load video: ${video.title}`),\n                  onCanPlay: () => setVideoError(null),\n                  crossOrigin: \"anonymous\",\n                  children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                    src: video.signedVideoUrl || video.videoUrl,\n                    type: \"video/mp4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 29\n                  }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                    kind: \"subtitles\",\n                    src: subtitle.url,\n                    srcLang: subtitle.language,\n                    label: subtitle.languageName,\n                    default: subtitle.isDefault || index === 0\n                  }, `${subtitle.language}-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 31\n                  }, this)), \"Your browser does not support the video tag.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 27\n                }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                  src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                  title: video.title,\n                  frameBorder: \"0\",\n                  allowFullScreen: true,\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    border: 'none'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-icon\",\n                    children: \"\\u26A0\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: \"Video Unavailable\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: videoError || \"This video cannot be played at the moment.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"youtube-video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u2022\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Class \", video.className || video.class]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-actions\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"youtube-action-btn\",\n                    onClick: () => setCurrentVideoIndex(null),\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2715\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Close\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 19\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 261,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoLessons, \"DHZMV1Su3mhqAshc/3kyKCKohuA=\", false, function () {\n  return [useSelector];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useSelector", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "TbAlertTriangle", "MdVerified", "getStudyMaterial", "getAllVideos", "jsxDEV", "_jsxDEV", "VideoLessons", "_s", "user", "state", "console", "log", "Object", "keys", "users", "auth", "storedUser", "localStorage", "getItem", "JSON", "parse", "e", "error", "videos", "setVideos", "loading", "setLoading", "setError", "searchTerm", "setSearchTerm", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "currentVideoIndex", "setCurrentVideoIndex", "videoRef", "setVideoRef", "videoError", "setVideoError", "isKiswahili", "getThumbnailUrl", "video", "thumbnailUrl", "videoID", "includes", "videoId", "match", "getSubjectName", "subject", "subjectMap", "handleShowVideo", "index", "filteredAndSortedVideos", "videoUrl", "signedVideoUrl", "warn", "fetchVideos", "response", "_response", "_response2", "success", "data", "length", "message", "_response3", "_response3$data", "_response4", "_response4$data", "filters", "level", "type", "_response5", "_response5$data", "_response6", "_response6$data", "allData", "filter", "item", "_item$title", "title", "toLowerCase", "filtered", "matchesLevel", "matchesClass", "className", "class", "matchesSubject", "err", "_video$title", "_video$subject", "_video$topic", "matchesSearch", "topic", "sort", "a", "b", "Date", "createdAt", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "target", "onClick", "map", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "duration", "subtitles", "sharedFromClass", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "style", "backgroundColor", "objectFit", "onCanPlay", "crossOrigin", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\n\nconst VideoLessons = () => {\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.user) {\n        console.log('✅ Found user in state.user');\n        return state.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnailUrl) return video.thumbnailUrl;\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      // Extract video ID from various YouTube URL formats\n      const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n      videoId = match ? match[1] : videoId;\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    // Use a working placeholder image\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n  };\n\n  const getSubjectName = (subject) => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🎥 Attempting to fetch videos from database...');\n\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n\n        if (response?.success && response?.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item =>\n              item.type === 'video' ||\n              item.videoUrl ||\n              item.videoID ||\n              item.title?.toLowerCase().includes('video')\n            );\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' ||\n                              video.level === selectedLevel ||\n                              !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' ||\n                              video.className === selectedClass ||\n                              video.class === selectedClass ||\n                              !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' ||\n                                video.subject === selectedSubject ||\n                                !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      const matchesSearch = !searchTerm ||\n        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());\n\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n\n  return (\n    <div className=\"video-lessons-container\">\n      <div className=\"video-lessons-header\">\n        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>\n        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"video-controls\">\n        <div className=\"search-section\">\n          <input\n            type=\"text\"\n            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n\n        <div className=\"filter-section\">\n          <select\n            value={selectedLevel}\n            onChange={(e) => setSelectedLevel(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"primary\">{isKiswahili ? 'Msingi' : 'Primary'}</option>\n            <option value=\"secondary\">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>\n            <option value=\"advance\">{isKiswahili ? 'Juu' : 'Advanced'}</option>\n          </select>\n\n          <select\n            value={selectedClass}\n            onChange={(e) => setSelectedClass(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n            {/* Add class options based on selected level */}\n          </select>\n\n          <select\n            value={selectedSubject}\n            onChange={(e) => setSelectedSubject(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n            {/* Add subject options */}\n          </select>\n        </div>\n      </div>\n\n      {/* Video Content */}\n      <div className=\"video-content\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-item\">\n                {/* Video Card */}\n                <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                  <div className=\"video-card-thumbnail\">\n                    <img\n                      src={getThumbnailUrl(video)}\n                      alt={video.title}\n                      className=\"thumbnail-image\"\n                      loading=\"lazy\"\n                      onError={(e) => {\n                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                          let videoId = video.videoID;\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                          const fallbacks = [\n                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                            '/api/placeholder/320/180'\n                          ];\n                          const currentSrc = e.target.src;\n                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                          if (currentIndex < fallbacks.length - 1) {\n                            e.target.src = fallbacks[currentIndex + 1];\n                          } else {\n                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n                          }\n                        } else {\n                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                    </div>\n                    <div className=\"video-duration\">\n                      {video.duration || \"Video\"}\n                    </div>\n                    {video.subtitles && video.subtitles.length > 0 && (\n                      <div className=\"subtitle-badge\">\n                        <TbInfoCircle />\n                        CC\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"video-card-content\">\n                    <h3 className=\"video-title\">{video.title}</h3>\n                    <div className=\"video-meta\">\n                      <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                      <span className=\"video-class\">\n                        {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                          ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)\n                          : `Form ${video.className || video.class}`}\n                      </span>\n                    </div>\n                    <div className=\"video-tags\">\n                      {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                      {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                        <span className=\"shared-tag\">\n                          {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}\n                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                            ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)\n                            : `Form ${video.sharedFromClass}`}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Inline Video Player */}\n                {currentVideoIndex === index && (\n                  <div className=\"inline-video-player\">\n                    <div className=\"youtube-style-layout\">\n                      <div className=\"youtube-video-player\">\n                        {video.videoUrl ? (\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"100%\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            }}\n                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}\n                            onCanPlay={() => setVideoError(null)}\n                            crossOrigin=\"anonymous\"\n                          >\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n                            Your browser does not support the video tag.\n                          </video>\n                        ) : video.videoID ? (\n                          <iframe\n                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                            title={video.title}\n                            frameBorder=\"0\"\n                            allowFullScreen\n                            style={{ width: '100%', height: '100%', border: 'none' }}\n                          ></iframe>\n                        ) : (\n                          <div className=\"video-error\">\n                            <div className=\"error-icon\">⚠️</div>\n                            <h3>Video Unavailable</h3>\n                            <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"youtube-video-info\">\n                        <h1 className=\"youtube-video-title\">{video.title}</h1>\n                        <div className=\"youtube-video-meta\">\n                          <span>{getSubjectName(video.subject)}</span>\n                          <span>•</span>\n                          <span>Class {video.className || video.class}</span>\n                        </div>\n                        <div className=\"youtube-video-actions\">\n                          <button\n                            className=\"youtube-action-btn\"\n                            onClick={() => setCurrentVideoIndex(null)}\n                          >\n                            <span>✕</span>\n                            <span>Close</span>\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAMC,IAAI,GAAGZ,WAAW,CAACa,KAAK,IAAI;IAChC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,KAAK,CAAC;MACpDC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,MAAM,CAACC,IAAI,CAACJ,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;;MAEjE;MACA,IAAIA,KAAK,IAAIA,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK,CAACN,IAAI,EAAE;QAC5CE,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,OAAOF,KAAK,CAACK,KAAK,CAACN,IAAI;MACzB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACD,IAAI,EAAE;QACvBE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,OAAOF,KAAK,CAACD,IAAI;MACnB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACM,IAAI,CAACP,IAAI,EAAE;QAC1CE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAOF,KAAK,CAACM,IAAI,CAACP,IAAI;MACxB;MAEAE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMK,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIF,UAAU,EAAE;QACd,IAAI;UACFN,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,OAAOQ,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;QAC/B,CAAC,CAAC,OAAOK,CAAC,EAAE;UACVX,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C;MACF;MAEAD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,OAAO,IAAI;IACb,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,KAAK,CAAC;MACrD,OAAO,IAAI;IACb;EACF,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,KAAK,EAAEK,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0C,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAMkD,WAAW,GAAGZ,aAAa,KAAK,mBAAmB;;EAEzD;EACA,MAAMa,eAAe,GAAIC,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACC,YAAY,EAAE,OAAOD,KAAK,CAACC,YAAY;IACjD,IAAID,KAAK,CAACE,OAAO,IAAI,CAACF,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC7D,IAAIC,OAAO,GAAGJ,KAAK,CAACE,OAAO;MAC3B;MACA,MAAMG,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;MACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACpC,OAAQ,8BAA6BA,OAAQ,oBAAmB;IAClE;IACA;IACA,OAAO,4cAA4c;EACrd,CAAC;EAED,MAAME,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,UAAU,GAAG;MACjB,aAAa,EAAEV,WAAW,GAAG,UAAU,GAAG,aAAa;MACvD,SAAS,EAAEA,WAAW,GAAG,YAAY,GAAG,SAAS;MACjD,WAAW,EAAE,WAAW;MACxB,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG,SAAS;MAC9C,gBAAgB,EAAEA,WAAW,GAAG,kBAAkB,GAAG,gBAAgB;MACrE,QAAQ,EAAEA,WAAW,GAAG,OAAO,GAAG,QAAQ;MAC1C,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,WAAW,GAAG,WAAW;MACpD,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,OAAO,GAAG,WAAW;MAChD,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG;IACvC,CAAC;IACD,OAAOU,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO;EACvC,CAAC;;EAED;EACA,MAAME,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMV,KAAK,GAAGW,uBAAuB,CAACD,KAAK,CAAC;IAC5CjB,oBAAoB,CAACiB,KAAK,CAAC;IAC3Bb,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIG,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEY,QAAQ,KAAKZ,KAAK,CAACY,QAAQ,CAACT,QAAQ,CAAC,eAAe,CAAC,IAAIH,KAAK,CAACY,QAAQ,CAACT,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF;QACA;QACA;QACAH,KAAK,CAACa,cAAc,GAAGb,KAAK,CAACY,QAAQ;MACvC,CAAC,CAAC,OAAOlC,KAAK,EAAE;QACdZ,OAAO,CAACgD,IAAI,CAAC,8CAA8C,CAAC;QAC5Dd,KAAK,CAACa,cAAc,GAAGb,KAAK,CAACY,QAAQ;MACvC;IACF;EACF,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGhE,WAAW,CAAC,YAAY;IAC1C,IAAI;MACF+B,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC;MAEdjB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAIiD,QAAQ,GAAG,IAAI;MACnB,IAAIrC,MAAM,GAAG,EAAE;;MAEf;MACA,IAAI;QAAA,IAAAsC,SAAA,EAAAC,UAAA;QACFpD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjDiD,QAAQ,GAAG,MAAMzD,YAAY,CAAC,CAAC;QAC/BO,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEiD,QAAQ,CAAC;QAE/C,IAAI,CAAAC,SAAA,GAAAD,QAAQ,cAAAC,SAAA,eAARA,SAAA,CAAUE,OAAO,KAAAD,UAAA,GAAIF,QAAQ,cAAAE,UAAA,eAARA,UAAA,CAAUE,IAAI,EAAE;UACvCzC,MAAM,GAAGqC,QAAQ,CAACI,IAAI;UACtBtD,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEY,MAAM,CAAC0C,MAAM,CAAC;QAC/E;MACF,CAAC,CAAC,OAAO3C,KAAK,EAAE;QACdZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEW,KAAK,CAAC4C,OAAO,CAAC;MACtD;;MAEA;MACA,IAAI3C,MAAM,CAAC0C,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAE,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACF5D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,MAAM4D,OAAO,GAAG;YACdC,KAAK,EAAE1C,aAAa;YACpB2C,IAAI,EAAE;UACR,CAAC;UAEDb,QAAQ,GAAG,MAAM1D,gBAAgB,CAACqE,OAAO,CAAC;UAC1C7D,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEiD,QAAQ,CAAC;UAEnD,IAAI,CAAAO,UAAA,GAAAP,QAAQ,cAAAO,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUH,IAAI,cAAAI,eAAA,eAAdA,eAAA,CAAgBL,OAAO,KAAAM,UAAA,GAAIT,QAAQ,cAAAS,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUL,IAAI,cAAAM,eAAA,eAAdA,eAAA,CAAgBN,IAAI,EAAE;YACnDzC,MAAM,GAAGqC,QAAQ,CAACI,IAAI,CAACA,IAAI;YAC3BtD,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEY,MAAM,CAAC0C,MAAM,CAAC;UACnF;QACF,CAAC,CAAC,OAAO3C,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,KAAK,CAAC4C,OAAO,CAAC;QAC1D;MACF;;MAEA;MACA,IAAI3C,MAAM,CAAC0C,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAS,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACFnE,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5DiD,QAAQ,GAAG,MAAM1D,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACrCQ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEiD,QAAQ,CAAC;UAEhE,IAAI,CAAAc,UAAA,GAAAd,QAAQ,cAAAc,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUV,IAAI,cAAAW,eAAA,eAAdA,eAAA,CAAgBZ,OAAO,KAAAa,UAAA,GAAIhB,QAAQ,cAAAgB,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUZ,IAAI,cAAAa,eAAA,eAAdA,eAAA,CAAgBb,IAAI,EAAE;YACnD;YACA,MAAMc,OAAO,GAAGlB,QAAQ,CAACI,IAAI,CAACA,IAAI;YAClCzC,MAAM,GAAGuD,OAAO,CAACC,MAAM,CAACC,IAAI;cAAA,IAAAC,WAAA;cAAA,OAC1BD,IAAI,CAACP,IAAI,KAAK,OAAO,IACrBO,IAAI,CAACxB,QAAQ,IACbwB,IAAI,CAAClC,OAAO,MAAAmC,WAAA,GACZD,IAAI,CAACE,KAAK,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,WAAW,CAAC,CAAC,CAACpC,QAAQ,CAAC,OAAO,CAAC;YAAA,CAC7C,CAAC;YACDrC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,MAAM,CAAC0C,MAAM,CAAC;UAC1E;QACF,CAAC,CAAC,OAAO3C,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEW,KAAK,CAAC4C,OAAO,CAAC;QACvE;MACF;;MAEA;MACA,IAAI3C,MAAM,CAAC0C,MAAM,GAAG,CAAC,EAAE;QACrB,MAAMmB,QAAQ,GAAG7D,MAAM,CAACwD,MAAM,CAACnC,KAAK,IAAI;UACtC,MAAMyC,YAAY,GAAGvD,aAAa,KAAK,KAAK,IACxBc,KAAK,CAAC4B,KAAK,KAAK1C,aAAa,IAC7B,CAACc,KAAK,CAAC4B,KAAK,CAAC,CAAC;;UAElC,MAAMc,YAAY,GAAGtD,aAAa,KAAK,KAAK,IACxBY,KAAK,CAAC2C,SAAS,KAAKvD,aAAa,IACjCY,KAAK,CAAC4C,KAAK,KAAKxD,aAAa,IAC7B,CAACY,KAAK,CAAC2C,SAAS,CAAC,CAAC;;UAEtC,MAAME,cAAc,GAAGvD,eAAe,KAAK,KAAK,IAC1BU,KAAK,CAACO,OAAO,KAAKjB,eAAe,IACjC,CAACU,KAAK,CAACO,OAAO,CAAC,CAAC;;UAEtC,OAAOkC,YAAY,IAAIC,YAAY,IAAIG,cAAc;QACvD,CAAC,CAAC;QAEFjE,SAAS,CAAC4D,QAAQ,CAAC;QACnB1E,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEyE,QAAQ,CAACnB,MAAM,EAAE,QAAQ,CAAC;QAErE,IAAImB,QAAQ,CAACnB,MAAM,KAAK,CAAC,EAAE;UACzBtC,QAAQ,CAAC,wEAAwE,CAAC;QACpF;MACF,CAAC,MAAM;QACL;QACAjB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpED,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrCD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAElCgB,QAAQ,CAAC,yFAAyF,CAAC;QACnGH,SAAS,CAAC,EAAE,CAAC;MACf;IAEF,CAAC,CAAC,OAAOkE,GAAG,EAAE;MACZhF,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEoE,GAAG,CAAC;MACvD/D,QAAQ,CAAC,0DAA0D,CAAC;MACpEH,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACI,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEnD;EACA,MAAMqB,uBAAuB,GAAG7D,OAAO,CAAC,MAAM;IAC5C,IAAI0F,QAAQ,GAAG7D,MAAM,CAACwD,MAAM,CAACnC,KAAK,IAAI;MAAA,IAAA+C,YAAA,EAAAC,cAAA,EAAAC,YAAA;MACpC,MAAMC,aAAa,GAAG,CAAClE,UAAU,MAAA+D,YAAA,GAC/B/C,KAAK,CAACsC,KAAK,cAAAS,YAAA,uBAAXA,YAAA,CAAaR,WAAW,CAAC,CAAC,CAACpC,QAAQ,CAACnB,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC,OAAAS,cAAA,GAC7DhD,KAAK,CAACO,OAAO,cAAAyC,cAAA,uBAAbA,cAAA,CAAeT,WAAW,CAAC,CAAC,CAACpC,QAAQ,CAACnB,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC,OAAAU,YAAA,GAC/DjD,KAAK,CAACmD,KAAK,cAAAF,YAAA,uBAAXA,YAAA,CAAaV,WAAW,CAAC,CAAC,CAACpC,QAAQ,CAACnB,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC;MAE/D,MAAME,YAAY,GAAGvD,aAAa,KAAK,KAAK,IAAIc,KAAK,CAAC4B,KAAK,KAAK1C,aAAa;MAC7E,MAAMwD,YAAY,GAAGtD,aAAa,KAAK,KAAK,IAAIY,KAAK,CAAC2C,SAAS,KAAKvD,aAAa,IAAIY,KAAK,CAAC4C,KAAK,KAAKxD,aAAa;MAClH,MAAMyD,cAAc,GAAGvD,eAAe,KAAK,KAAK,IAAIU,KAAK,CAACO,OAAO,KAAKjB,eAAe;MAErF,OAAO4D,aAAa,IAAIT,YAAY,IAAIC,YAAY,IAAIG,cAAc;IACxE,CAAC,CAAC;IAEF,OAAOL,QAAQ,CAACY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAAC;EAC/E,CAAC,EAAE,CAAC7E,MAAM,EAAEK,UAAU,EAAEE,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEvE;EACAzC,SAAS,CAAC,MAAM;IACdkE,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACAlE,SAAS,CAAC,MAAM;IACdkE,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAC7B,aAAa,EAAEE,aAAa,EAAEE,eAAe,EAAEyB,WAAW,CAAC,CAAC;EAEhE,oBACEtD,OAAA;IAAKkF,SAAS,EAAC,yBAAyB;IAAAc,QAAA,gBACtChG,OAAA;MAAKkF,SAAS,EAAC,sBAAsB;MAAAc,QAAA,gBACnChG,OAAA;QAAAgG,QAAA,EAAK3D,WAAW,GAAG,iBAAiB,GAAG;MAAe;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5DpG,OAAA;QAAAgG,QAAA,EAAI3D,WAAW,GAAG,iCAAiC,GAAG;MAAoC;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAGNpG,OAAA;MAAKkF,SAAS,EAAC,gBAAgB;MAAAc,QAAA,gBAC7BhG,OAAA;QAAKkF,SAAS,EAAC,gBAAgB;QAAAc,QAAA,eAC7BhG,OAAA;UACEoE,IAAI,EAAC,MAAM;UACXiC,WAAW,EAAEhE,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;UAClEiE,KAAK,EAAE/E,UAAW;UAClBgF,QAAQ,EAAGvF,CAAC,IAAKQ,aAAa,CAACR,CAAC,CAACwF,MAAM,CAACF,KAAK,CAAE;UAC/CpB,SAAS,EAAC;QAAc;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENpG,OAAA;QAAKkF,SAAS,EAAC,gBAAgB;QAAAc,QAAA,gBAC7BhG,OAAA;UACEsG,KAAK,EAAE7E,aAAc;UACrB8E,QAAQ,EAAGvF,CAAC,IAAKU,gBAAgB,CAACV,CAAC,CAACwF,MAAM,CAACF,KAAK,CAAE;UAClDpB,SAAS,EAAC,eAAe;UAAAc,QAAA,gBAEzBhG,OAAA;YAAQsG,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAE3D,WAAW,GAAG,QAAQ,GAAG;UAAS;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrEpG,OAAA;YAAQsG,KAAK,EAAC,WAAW;YAAAN,QAAA,EAAE3D,WAAW,GAAG,WAAW,GAAG;UAAW;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5EpG,OAAA;YAAQsG,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAE3D,WAAW,GAAG,KAAK,GAAG;UAAU;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAETpG,OAAA;UACEsG,KAAK,EAAE3E,aAAc;UACrB4E,QAAQ,EAAGvF,CAAC,IAAKY,gBAAgB,CAACZ,CAAC,CAACwF,MAAM,CAACF,KAAK,CAAE;UAClDpB,SAAS,EAAC,eAAe;UAAAc,QAAA,eAEzBhG,OAAA;YAAQsG,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAE3D,WAAW,GAAG,eAAe,GAAG;UAAa;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtE,CAAC,eAETpG,OAAA;UACEsG,KAAK,EAAEzE,eAAgB;UACvB0E,QAAQ,EAAGvF,CAAC,IAAKc,kBAAkB,CAACd,CAAC,CAACwF,MAAM,CAACF,KAAK,CAAE;UACpDpB,SAAS,EAAC,eAAe;UAAAc,QAAA,eAEzBhG,OAAA;YAAQsG,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAE3D,WAAW,GAAG,aAAa,GAAG;UAAc;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpG,OAAA;MAAKkF,SAAS,EAAC,eAAe;MAAAc,QAAA,EAC3B5E,OAAO,gBACNpB,OAAA;QAAKkF,SAAS,EAAC,eAAe;QAAAc,QAAA,gBAC5BhG,OAAA;UAAKkF,SAAS,EAAC;QAAiB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCpG,OAAA;UAAAgG,QAAA,EAAI3D,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJnF,KAAK,gBACPjB,OAAA;QAAKkF,SAAS,EAAC,aAAa;QAAAc,QAAA,gBAC1BhG,OAAA,CAACL,eAAe;UAACuF,SAAS,EAAC;QAAY;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CpG,OAAA;UAAAgG,QAAA,EAAK3D,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7EpG,OAAA;UAAAgG,QAAA,EAAI/E;QAAK;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdpG,OAAA;UAAQyG,OAAO,EAAEnD,WAAY;UAAC4B,SAAS,EAAC,WAAW;UAAAc,QAAA,EAChD3D,WAAW,GAAG,aAAa,GAAG;QAAW;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJlD,uBAAuB,CAACU,MAAM,GAAG,CAAC,gBACpC5D,OAAA;QAAKkF,SAAS,EAAC,aAAa;QAAAc,QAAA,EACzB9C,uBAAuB,CAACwD,GAAG,CAAC,CAACnE,KAAK,EAAEU,KAAK,kBACxCjD,OAAA;UAAiBkF,SAAS,EAAC,YAAY;UAAAc,QAAA,gBAErChG,OAAA;YAAKkF,SAAS,EAAC,YAAY;YAACuB,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAACC,KAAK,CAAE;YAAA+C,QAAA,gBAChEhG,OAAA;cAAKkF,SAAS,EAAC,sBAAsB;cAAAc,QAAA,gBACnChG,OAAA;gBACE2G,GAAG,EAAErE,eAAe,CAACC,KAAK,CAAE;gBAC5BqE,GAAG,EAAErE,KAAK,CAACsC,KAAM;gBACjBK,SAAS,EAAC,iBAAiB;gBAC3B9D,OAAO,EAAC,MAAM;gBACdyF,OAAO,EAAG7F,CAAC,IAAK;kBACd,IAAIuB,KAAK,CAACE,OAAO,IAAI,CAACF,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;oBAC7D,IAAIC,OAAO,GAAGJ,KAAK,CAACE,OAAO;oBAC3B,MAAMG,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;oBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;oBACpC,MAAMmE,SAAS,GAAG,CACf,8BAA6BnE,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;oBACD,MAAMoE,UAAU,GAAG/F,CAAC,CAACwF,MAAM,CAACG,GAAG;oBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACC,GAAG,IAAIH,UAAU,CAACrE,QAAQ,CAACwE,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC1F,IAAIJ,YAAY,GAAGF,SAAS,CAAClD,MAAM,GAAG,CAAC,EAAE;sBACvC5C,CAAC,CAACwF,MAAM,CAACG,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;oBAC5C,CAAC,MAAM;sBACLhG,CAAC,CAACwF,MAAM,CAACG,GAAG,GAAG,4cAA4c;oBAC7d;kBACF,CAAC,MAAM;oBACL3F,CAAC,CAACwF,MAAM,CAACG,GAAG,GAAG,4cAA4c;kBAC7d;gBACF;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFpG,OAAA;gBAAKkF,SAAS,EAAC,cAAc;gBAAAc,QAAA,eAC3BhG,OAAA,CAACR,YAAY;kBAAC0F,SAAS,EAAC;gBAAW;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACNpG,OAAA;gBAAKkF,SAAS,EAAC,gBAAgB;gBAAAc,QAAA,EAC5BzD,KAAK,CAAC8E,QAAQ,IAAI;cAAO;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,EACL7D,KAAK,CAAC+E,SAAS,IAAI/E,KAAK,CAAC+E,SAAS,CAAC1D,MAAM,GAAG,CAAC,iBAC5C5D,OAAA;gBAAKkF,SAAS,EAAC,gBAAgB;gBAAAc,QAAA,gBAC7BhG,OAAA,CAACN,YAAY;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,MAElB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENpG,OAAA;cAAKkF,SAAS,EAAC,oBAAoB;cAAAc,QAAA,gBACjChG,OAAA;gBAAIkF,SAAS,EAAC,aAAa;gBAAAc,QAAA,EAAEzD,KAAK,CAACsC;cAAK;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9CpG,OAAA;gBAAKkF,SAAS,EAAC,YAAY;gBAAAc,QAAA,gBACzBhG,OAAA;kBAAMkF,SAAS,EAAC,eAAe;kBAAAc,QAAA,EAAEnD,cAAc,CAACN,KAAK,CAACO,OAAO;gBAAC;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtEpG,OAAA;kBAAMkF,SAAS,EAAC,aAAa;kBAAAc,QAAA,EAC1BvE,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEY,WAAW,GAAI,aAAYE,KAAK,CAAC2C,SAAS,IAAI3C,KAAK,CAAC4C,KAAM,EAAC,GAAI,SAAQ5C,KAAK,CAAC2C,SAAS,IAAI3C,KAAK,CAAC4C,KAAM,EAAC,GACvG,QAAO5C,KAAK,CAAC2C,SAAS,IAAI3C,KAAK,CAAC4C,KAAM;gBAAC;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNpG,OAAA;gBAAKkF,SAAS,EAAC,YAAY;gBAAAc,QAAA,GACxBzD,KAAK,CAACmD,KAAK,iBAAI1F,OAAA;kBAAMkF,SAAS,EAAC,WAAW;kBAAAc,QAAA,EAAEzD,KAAK,CAACmD;gBAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/D7D,KAAK,CAACgF,eAAe,IAAIhF,KAAK,CAACgF,eAAe,MAAMhF,KAAK,CAAC2C,SAAS,IAAI3C,KAAK,CAAC4C,KAAK,CAAC,iBAClFnF,OAAA;kBAAMkF,SAAS,EAAC,YAAY;kBAAAc,QAAA,GACzB3D,WAAW,GAAG,qBAAqB,GAAG,cAAc,EACpDZ,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEY,WAAW,GAAI,aAAYE,KAAK,CAACgF,eAAgB,EAAC,GAAI,SAAQhF,KAAK,CAACgF,eAAgB,EAAC,GACrF,QAAOhF,KAAK,CAACgF,eAAgB,EAAC;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLrE,iBAAiB,KAAKkB,KAAK,iBAC1BjD,OAAA;YAAKkF,SAAS,EAAC,qBAAqB;YAAAc,QAAA,eAClChG,OAAA;cAAKkF,SAAS,EAAC,sBAAsB;cAAAc,QAAA,gBACnChG,OAAA;gBAAKkF,SAAS,EAAC,sBAAsB;gBAAAc,QAAA,EAClCzD,KAAK,CAACY,QAAQ,gBACbnD,OAAA;kBACEwH,GAAG,EAAGA,GAAG,IAAKtF,WAAW,CAACsF,GAAG,CAAE;kBAC/BC,QAAQ;kBACRC,QAAQ;kBACRC,WAAW;kBACXC,OAAO,EAAC,UAAU;kBAClBC,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAC,MAAM;kBACbC,MAAM,EAAEzF,eAAe,CAACC,KAAK,CAAE;kBAC/ByF,KAAK,EAAE;oBACLH,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdG,eAAe,EAAE,MAAM;oBACvBC,SAAS,EAAE;kBACb,CAAE;kBACFrB,OAAO,EAAG7F,CAAC,IAAKoB,aAAa,CAAE,yBAAwBG,KAAK,CAACsC,KAAM,EAAC,CAAE;kBACtEsD,SAAS,EAAEA,CAAA,KAAM/F,aAAa,CAAC,IAAI,CAAE;kBACrCgG,WAAW,EAAC,WAAW;kBAAApC,QAAA,gBAEvBhG,OAAA;oBAAQ2G,GAAG,EAAEpE,KAAK,CAACa,cAAc,IAAIb,KAAK,CAACY,QAAS;oBAACiB,IAAI,EAAC;kBAAW;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACvE7D,KAAK,CAAC+E,SAAS,IAAI/E,KAAK,CAAC+E,SAAS,CAAC1D,MAAM,GAAG,CAAC,IAAIrB,KAAK,CAAC+E,SAAS,CAACZ,GAAG,CAAC,CAAC2B,QAAQ,EAAEpF,KAAK,kBACpFjD,OAAA;oBAEEsI,IAAI,EAAC,WAAW;oBAChB3B,GAAG,EAAE0B,QAAQ,CAACnB,GAAI;oBAClBqB,OAAO,EAAEF,QAAQ,CAACG,QAAS;oBAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;oBAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAI3F,KAAK,KAAK;kBAAE,GALrC,GAAEoF,QAAQ,CAACG,QAAS,IAAGvF,KAAM,EAAC;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,GACN7D,KAAK,CAACE,OAAO,gBACfzC,OAAA;kBACE2G,GAAG,EAAG,iCAAgCpE,KAAK,CAACE,OAAQ,mBAAmB;kBACvEoC,KAAK,EAAEtC,KAAK,CAACsC,KAAM;kBACnBgE,WAAW,EAAC,GAAG;kBACfC,eAAe;kBACfd,KAAK,EAAE;oBAAEH,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE,MAAM;oBAAEiB,MAAM,EAAE;kBAAO;gBAAE;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,gBAEVpG,OAAA;kBAAKkF,SAAS,EAAC,aAAa;kBAAAc,QAAA,gBAC1BhG,OAAA;oBAAKkF,SAAS,EAAC,YAAY;oBAAAc,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpCpG,OAAA;oBAAAgG,QAAA,EAAI;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1BpG,OAAA;oBAAAgG,QAAA,EAAI7D,UAAU,IAAI;kBAA4C;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENpG,OAAA;gBAAKkF,SAAS,EAAC,oBAAoB;gBAAAc,QAAA,gBACjChG,OAAA;kBAAIkF,SAAS,EAAC,qBAAqB;kBAAAc,QAAA,EAAEzD,KAAK,CAACsC;gBAAK;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtDpG,OAAA;kBAAKkF,SAAS,EAAC,oBAAoB;kBAAAc,QAAA,gBACjChG,OAAA;oBAAAgG,QAAA,EAAOnD,cAAc,CAACN,KAAK,CAACO,OAAO;kBAAC;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5CpG,OAAA;oBAAAgG,QAAA,EAAM;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACdpG,OAAA;oBAAAgG,QAAA,GAAM,QAAM,EAACzD,KAAK,CAAC2C,SAAS,IAAI3C,KAAK,CAAC4C,KAAK;kBAAA;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACNpG,OAAA;kBAAKkF,SAAS,EAAC,uBAAuB;kBAAAc,QAAA,eACpChG,OAAA;oBACEkF,SAAS,EAAC,oBAAoB;oBAC9BuB,OAAO,EAAEA,CAAA,KAAMzE,oBAAoB,CAAC,IAAI,CAAE;oBAAAgE,QAAA,gBAE1ChG,OAAA;sBAAAgG,QAAA,EAAM;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACdpG,OAAA;sBAAAgG,QAAA,EAAM;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA,GAhJOnD,KAAK;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiJV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENpG,OAAA;QAAKkF,SAAS,EAAC,aAAa;QAAAc,QAAA,gBAC1BhG,OAAA,CAACP,eAAe;UAACyF,SAAS,EAAC;QAAY;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CpG,OAAA;UAAAgG,QAAA,EAAK3D,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1EpG,OAAA;UAAAgG,QAAA,EAAI3D,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJpG,OAAA;UAAGkF,SAAS,EAAC,YAAY;UAAAc,QAAA,EAAE3D,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClG,EAAA,CA/dID,YAAY;EAAA,QAEHV,WAAW;AAAA;AAAAyJ,EAAA,GAFpB/I,YAAY;AAielB,eAAeA,YAAY;AAAC,IAAA+I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}