// routes/chatgpt.js
const express = require("express");
const axios = require("axios");
const router = express.Router();
const AWS = require("aws-sdk");
const multer = require("multer");

const storage = multer.memoryStorage();
const upload = multer({ storage });

const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
});

router.post("/image/upload", upload.single("image"), async (req, res) => {
  try {
    console.log('Image upload request received');
    const file = req.file;
    if (!file) {
      console.log('No file in request');
      return res.status(400).json({ success: false, message: "No file uploaded." });
    }

    console.log('File details:', { name: file.originalname, size: file.size, type: file.mimetype });

    const params = {
      Bucket: process.env.AWS_S3_BUCKET_NAME, // Your S3 bucket name
      Key: `${Date.now()}-${file.originalname}`, // Unique file name
      Body: file.buffer,
      ContentType: file.mimetype,
      ACL: "public-read", // File should be publicly readable
    };

    const result = await s3.upload(params).promise();
    console.log("File uploaded successfully:", result);

    res.status(200).json({
      success: true,
      data: { url: result.Location },
      url: result.Location // Fallback for compatibility
    });
  } catch (error) {
    console.error("Error uploading file:", error);
    res.status(500).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
});

router.post("/chat", async (req, res) => {
  console.log('Data: ', req.body);
  try {
    const start = performance.now();
    const { messages, language, systemPrompt } = req.body;

    // Prepare messages with language-specific system prompt for Kiswahili users
    let finalMessages = [...messages];

    if (language === 'kiswahili' && systemPrompt) {
      // Add or update system message for Kiswahili users
      const systemMessage = {
        role: "system",
        content: systemPrompt
      };

      // Check if there's already a system message
      const hasSystemMessage = finalMessages.some(msg => msg.role === 'system');
      if (hasSystemMessage) {
        // Replace existing system message
        finalMessages = finalMessages.map(msg =>
          msg.role === 'system' ? systemMessage : msg
        );
      } else {
        // Add system message at the beginning
        finalMessages = [systemMessage, ...finalMessages];
      }
    }

    const response = await axios.post(
      "https://api.openai.com/v1/chat/completions",
      {
        model: "gpt-4o",
        messages: finalMessages,
        // max_tokens: 100,
        temperature: 0.5,
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
        },
      }
    );

    const end = performance.now();
    console.log('Time taken by openai api (s): ', (end - start) / 1000);
    res.status(200).json({ success: true, data: response.data.choices[0].message.content });
  } catch (error) {
    console.error("Error fetching GPT-4 response:", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// ✔️ NEW – AI ­answer-checker
router.post("/check-answer", async (req, res) => {
  try {
    const questions = req.body;

    if (!Array.isArray(questions) || questions.length === 0) {
      return res.status(400).json({
        success: false,
        error: "Request body must be a non-empty array of questions",
      });
    }

    const results = [];

    for (const item of questions) {
      const { question, expectedAnswer, userAnswer } = item;

      if (!question || !expectedAnswer || !userAnswer) {
        results.push({
          question,
          success: false,
          error: "question, expectedAnswer and userAnswer are required",
        });
        continue;
      }

      const messages = [
        {
          role: "system",
          content:
            'You are an examiner. Compare the student\'s answer with the expected answer. Ignore the format just validate if answer is correct or not. ' +
            'Reply ONLY with valid JSON: {"isCorrect": true/false}.',
        },
        {
          role: "user",
          content: `QUESTION: ${question}\nEXPECTED ANSWER: ${expectedAnswer}\nSTUDENT ANSWER: ${userAnswer}`,
        },
      ];

      try {
        const { data } = await axios.post(
          "https://api.openai.com/v1/chat/completions",
          {
            model: "gpt-4o",
            messages,
            temperature: 0,
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
            },
          }
        );

        const aiJson = JSON.parse(data.choices[0].message.content.trim());

        results.push({
          question,
          expectedAnswer,
          userAnswer,
          result: aiJson,
          success: true,
        });
      } catch (innerError) {
        console.error("OpenAI error:", innerError.message);
        results.push({
          question,
          expectedAnswer,
          userAnswer,
          success: false,
          error: "AI response failed or returned invalid JSON",
        });
      }
    }

    res.status(200).json({ success: true, data: results });
  } catch (error) {
    console.error("Answer-check error:", error.message);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/explain-answer", async (req, res) => {
  try {
    const { question, expectedAnswer, userAnswer, imageUrl, language } = req.body;

    // console.log("Image Url: ", imageUrl);

    if (!question || !expectedAnswer || !userAnswer) {
      return res.status(400).json({
        success: false,
        error: "question, expectedAnswer, and userAnswer are required",
      });
    };

    // Detect if this is a math question
    const isMathQuestion = /[\d\+\-\×\÷\*\/\=\(\)\^\%]|math|calculate|solve|equation|formula|area|volume|perimeter|algebra|geometry|trigonometry|statistics|probability/i.test(question + ' ' + expectedAnswer);

    // Build the user message content
    let userContent = [
      `QUESTION: ${question}`,
      `EXPECTED ANSWER: ${expectedAnswer}`,
      `STUDENT ANSWER: ${userAnswer}`,
      isMathQuestion ?
        `This is a MATHEMATICS question. Provide a complete mathematical solution using LaTeX notation for mathematical expressions:

        **Solution:**
        Step 1: [Clear mathematical step with equations in LaTeX format]
        Step 2: [Next step with calculations in LaTeX format]
        Step 3: [Continue with formulas and work in LaTeX format]

        **Calculations:**
        Show all mathematical work using LaTeX notation:
        - Use \\( \\) for inline math: \\(2 + 3 = 5\\)
        - Use \\[ \\] for block equations: \\[Area = length \\times width\\]
        - Use proper LaTeX symbols: \\times, \\div, \\leq, \\geq, \\neq, \\frac{a}{b}, x^2, \\sqrt{x}

        **Answer:** \\[Final = numerical \\text{ answer with units}\\]

        IMPORTANT:
        - Wrap ALL mathematical expressions in LaTeX notation
        - Use \\( \\) for inline math and \\[ \\] for display math
        - Show step-by-step calculations with proper mathematical formatting
        - Use LaTeX commands for fractions, exponents, roots, etc.

        Example format:
        **Step 1:** Find the area
        \\[Area = length \\times width = 5 \\times 3 = 15\\]

        **Step 2:** Calculate perimeter
        \\[Perimeter = 2(l + w) = 2(5 + 3) = 16\\]` :
        `Please provide a clear explanation why the student's answer is incorrect and show the correct method to solve this question.`
    ].filter(Boolean).join('\n');

    // Handle image content for vision models
    let messageContent;
    if (imageUrl) {
      messageContent = [
        {
          type: "text",
          text: userContent
        },
        {
          type: "image_url",
          image_url: {
            url: imageUrl,
          },
        }
      ];
    } else {
      messageContent = userContent;
    }

    // Prepare system message based on language
    const systemContent = language === 'kiswahili'
      ? "Wewe ni mwalimu mkarimu na wa msaada. Unapopewa swali, jibu sahihi, jibu la mwanafunzi, na picha ikiwezekana, eleza kwa nini jibu la mwanafunzi si sahihi kwa mistari 1-2 ya upole na rahisi kwa wanafunzi. Pia taja njia sahihi, sababu kwa ufupi na mahesabu kwa swali la hisabati. Tenganisha maelezo na mahesabu na hatua."
      : "You are a kind and helpful teacher. When given a question, the correct answer, the student's answer, and optionally an image, explain why the student's answer is incorrect in just 1–2 polite, beginner-friendly lines. Also mention the correct method, reasoning briefly and calculations for math question. Separate explanation with calculatons and steps.";

    const messages = [
      {
        role: "system",
        content: systemContent,
      },
      {
        role: "user",
        content: messageContent
      },
    ];


    // Validate OpenAI API key
    if (!process.env.OPENAI_API_KEY) {
      throw new Error("OpenAI API key not configured");
    }

    console.log("Sending explanation request:", {
      questionLength: question.length,
      expectedAnswer,
      userAnswer,
      hasImage: !!imageUrl,
      language: language || 'english'
    });

    // GPT-4o request
    const { data } = await axios.post(
      "https://api.openai.com/v1/chat/completions",
      {
        model: "gpt-4o",
        messages,
        temperature: 0.7,
        max_tokens: 1000,
        timeout: 30000
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
        },
        timeout: 30000
      }
    );

    // Validate response
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error("Invalid response from OpenAI API");
    }

    const explanation = data.choices[0].message.content.trim();

    if (!explanation) {
      throw new Error("Empty explanation received from OpenAI API");
    }

    console.log("Explanation generated successfully:", {
      explanationLength: explanation.length,
      questionType: isMathQuestion ? 'math' : 'general'
    });

    res.status(200).json({
      success: true,
      explanation,
    });
  } catch (error) {
    console.error("Explain-answer error:", error.message);
    console.error("Error details:", {
      question: req.body.question?.substring(0, 100),
      expectedAnswer: req.body.expectedAnswer,
      userAnswer: req.body.userAnswer,
      hasImage: !!req.body.imageUrl,
      language: req.body.language
    });

    // Provide a more helpful error message
    const errorMessage = error.response?.data?.error?.message || error.message || "Failed to generate explanation";

    res.status(500).json({
      success: false,
      error: `Sorry, we could not generate an explanation at this time. Please try again later. (${errorMessage})`
    });
  }
});



module.exports = router;
