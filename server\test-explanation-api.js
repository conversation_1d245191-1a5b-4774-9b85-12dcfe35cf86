const axios = require('axios');
require('dotenv').config();

async function testExplanationAPI() {
  try {
    console.log('🧪 Testing Explanation API...');
    
    // Test cases for different question types
    const testCases = [
      {
        name: "Fill in the blank - Simple",
        question: "The cat normally ________ under the table",
        expectedAnswer: "sleeps",
        userAnswer: "runs",
        language: "english"
      },
      {
        name: "Fill in the blank - Math",
        question: "2 + 3 = ________",
        expectedAnswer: "5",
        userAnswer: "6",
        language: "english"
      },
      {
        name: "Fill in the blank - Science",
        question: "The process by which plants make food is called ________",
        expectedAnswer: "photosynthesis",
        userAnswer: "respiration",
        language: "english"
      },
      {
        name: "Fill in the blank - Kiswahili",
        question: "Mti mkubwa ________ kivulini",
        expectedAnswer: "unatoa",
        userAnswer: "unakula",
        language: "kiswahili"
      }
    ];

    const baseURL = 'http://localhost:5000';
    
    for (const testCase of testCases) {
      console.log(`\n📝 Testing: ${testCase.name}`);
      console.log(`Question: ${testCase.question}`);
      console.log(`Expected: ${testCase.expectedAnswer}`);
      console.log(`User Answer: ${testCase.userAnswer}`);
      
      try {
        const response = await axios.post(`${baseURL}/api/chatgpt/explain-answer`, {
          question: testCase.question,
          expectedAnswer: testCase.expectedAnswer,
          userAnswer: testCase.userAnswer,
          language: testCase.language
        }, {
          timeout: 30000
        });

        if (response.data.success) {
          console.log('✅ Success!');
          console.log(`Explanation: ${response.data.explanation.substring(0, 200)}...`);
        } else {
          console.log('❌ Failed!');
          console.log(`Error: ${response.data.error}`);
        }
      } catch (error) {
        console.log('❌ Request Failed!');
        console.log(`Error: ${error.message}`);
        if (error.response) {
          console.log(`Status: ${error.response.status}`);
          console.log(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
        }
      }
    }

    // Test with missing parameters
    console.log('\n🚫 Testing with missing parameters...');
    try {
      const response = await axios.post(`${baseURL}/api/chatgpt/explain-answer`, {
        question: "Test question",
        // Missing expectedAnswer and userAnswer
      });
      console.log('❌ Should have failed but succeeded');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ Correctly rejected missing parameters');
        console.log(`Error: ${error.response.data.error}`);
      } else {
        console.log('❌ Unexpected error');
        console.log(`Error: ${error.message}`);
      }
    }

    // Test server health
    console.log('\n🏥 Testing server health...');
    try {
      const healthResponse = await axios.get(`${baseURL}/api/health`);
      console.log('✅ Server is healthy');
      console.log(`Response: ${JSON.stringify(healthResponse.data, null, 2)}`);
    } catch (error) {
      console.log('❌ Server health check failed');
      console.log(`Error: ${error.message}`);
    }

    console.log('\n🎯 Test Summary:');
    console.log('- Fill in the blank questions should now work consistently');
    console.log('- Better error handling and logging implemented');
    console.log('- API request format fixed for OpenAI compatibility');
    console.log('- Validation added for missing parameters');

  } catch (error) {
    console.error('❌ Test script error:', error.message);
  }
}

// Run the test
testExplanationAPI();
