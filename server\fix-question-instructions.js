const mongoose = require('mongoose');
require('dotenv').config();

const Question = require('./models/questionModel');

async function findAndFixQuestionInstructions() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');

    // Search for questions with the specific instructions (expanded search)
    const instructionsToRemove = [
      'USE CAPITAL LETTER AND PUT FULL STOP',
      'USE CAPITAL LETTER',
      'use capital letter and put full stop',
      'use capital letter',
      'Use capital letter and put full stop',
      'Use capital letter',
      'USE CAPITAL LETTERS AND PUT FULL STOP',
      'USE CAPITAL LETTERS',
      'use capital letters and put full stop',
      'use capital letters',
      'Use capital letters and put full stop',
      'Use capital letters',
      'CAPITAL LETTER',
      'capital letter',
      'Capital letter',
      'CAPITAL LETTERS',
      'capital letters',
      'Capital letters'
    ];

    console.log('🔍 Searching for questions with unwanted instructions...');

    // Build regex pattern to find any of these instructions
    const regexPattern = instructionsToRemove.map(instruction => 
      instruction.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // Escape special regex characters
    ).join('|');

    const searchRegex = new RegExp(regexPattern, 'i'); // Case insensitive

    // Find questions containing these instructions
    const questionsWithInstructions = await Question.find({
      name: { $regex: searchRegex }
    });

    console.log(`📊 Found ${questionsWithInstructions.length} questions with unwanted instructions`);

    // Also do a broader search for any mention of "capital" in questions
    console.log('\n🔍 Doing broader search for "capital" mentions...');
    const capitalMentions = await Question.find({
      name: { $regex: /capital/i }
    }).limit(10);

    console.log(`📊 Found ${capitalMentions.length} questions mentioning "capital"`);
    if (capitalMentions.length > 0) {
      console.log('\n📋 Sample questions mentioning "capital":');
      capitalMentions.forEach((question, index) => {
        console.log(`${index + 1}. ${question.name.substring(0, 150)}...`);
      });
    }

    if (questionsWithInstructions.length === 0) {
      console.log('\n✅ No questions found with the specific unwanted instructions');
      return;
    }

    // Display found questions
    console.log('\n📋 Questions found:');
    questionsWithInstructions.forEach((question, index) => {
      console.log(`${index + 1}. ID: ${question._id}`);
      console.log(`   Question: ${question.name.substring(0, 100)}...`);
      console.log(`   Topic: ${question.topic}`);
      console.log(`   Class: ${question.classLevel}`);
      console.log('');
    });

    // Fix the questions by removing the instructions
    console.log('🔧 Removing unwanted instructions from questions...');
    
    let fixedCount = 0;
    
    for (const question of questionsWithInstructions) {
      let originalText = question.name;
      let cleanedText = originalText;

      // Remove each instruction pattern
      for (const instruction of instructionsToRemove) {
        // Remove the instruction with various punctuation and spacing
        const patterns = [
          new RegExp(`\\b${instruction.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b[.,;:]*\\s*`, 'gi'),
          new RegExp(`\\(${instruction.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\)[.,;:]*\\s*`, 'gi'),
          new RegExp(`\\[${instruction.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\][.,;:]*\\s*`, 'gi'),
          new RegExp(`${instruction.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[.,;:]*\\s*`, 'gi')
        ];

        for (const pattern of patterns) {
          cleanedText = cleanedText.replace(pattern, '');
        }
      }

      // Clean up extra spaces and punctuation
      cleanedText = cleanedText
        .replace(/\s+/g, ' ') // Multiple spaces to single space
        .replace(/\s*[.,;:]+\s*/g, '. ') // Clean up punctuation
        .replace(/\.\s*\./g, '.') // Remove double periods
        .replace(/^\s*[.,;:]+\s*/, '') // Remove leading punctuation
        .trim();

      // Only update if the text actually changed
      if (cleanedText !== originalText && cleanedText.length > 0) {
        await Question.findByIdAndUpdate(question._id, {
          name: cleanedText
        });

        console.log(`✅ Fixed question ${question._id}`);
        console.log(`   Before: ${originalText.substring(0, 80)}...`);
        console.log(`   After:  ${cleanedText.substring(0, 80)}...`);
        console.log('');
        
        fixedCount++;
      }
    }

    console.log(`🎉 Successfully fixed ${fixedCount} questions`);

    // Verify the fix by searching again
    console.log('\n🔍 Verifying fix by searching again...');
    const remainingQuestions = await Question.find({
      name: { $regex: searchRegex }
    });

    console.log(`📊 Remaining questions with instructions: ${remainingQuestions.length}`);

    if (remainingQuestions.length > 0) {
      console.log('⚠️ Some questions still contain instructions:');
      remainingQuestions.forEach((question, index) => {
        console.log(`${index + 1}. ${question.name.substring(0, 100)}...`);
      });
    } else {
      console.log('✅ All unwanted instructions have been successfully removed!');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
}

// Run the script
findAndFixQuestionInstructions();
