{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial } from '../../../apicalls/study';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoLessons = () => {\n  _s();\n  // Redux state\n  const {\n    user\n  } = useSelector(state => state.users);\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = video => {\n    if (video.thumbnailUrl) return video.thumbnailUrl;\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      var _video$videoID$match;\n      const videoId = video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be') ? ((_video$videoID$match = video.videoID.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/)) === null || _video$videoID$match === void 0 ? void 0 : _video$videoID$match[1]) || video.videoID : video.videoID;\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/320/180';\n  };\n  const getSubjectName = subject => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n\n      // Use the proper API call for study materials (videos)\n      const filters = {\n        level: selectedLevel,\n        class: selectedClass !== 'all' ? selectedClass : undefined,\n        subject: selectedSubject !== 'all' ? selectedSubject : undefined,\n        type: 'video' // Filter for videos only\n      };\n\n      const response = await getStudyMaterial(filters);\n      if ((_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        setVideos(response.data.data || []);\n      } else {\n        var _response$data2;\n        throw new Error(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || 'Failed to fetch videos');\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to load videos');\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      var _video$title, _video$subject, _video$topic;\n      const matchesSearch = !searchTerm || ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isKiswahili ? 'Masomo ya Video' : 'Video Lessons'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: isKiswahili ? 'Tafuta video...' : 'Search videos...',\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLevel,\n          onChange: e => setSelectedLevel(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"primary\",\n            children: isKiswahili ? 'Msingi' : 'Primary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"secondary\",\n            children: isKiswahili ? 'Sekondari' : 'Secondary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advance\",\n            children: isKiswahili ? 'Juu' : 'Advanced'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedClass,\n          onChange: e => setSelectedClass(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedSubject,\n          onChange: e => setSelectedSubject(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card\",\n            onClick: () => handleShowVideo(index),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card-thumbnail\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getThumbnailUrl(video),\n                alt: video.title,\n                className: \"thumbnail-image\",\n                loading: \"lazy\",\n                onError: e => {\n                  if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                    let videoId = video.videoID;\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                    const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                    const currentSrc = e.target.src;\n                    const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                    if (currentIndex < fallbacks.length - 1) {\n                      e.target.src = fallbacks[currentIndex + 1];\n                    } else {\n                      e.target.src = '/api/placeholder/320/180';\n                    }\n                  } else {\n                    e.target.src = '/api/placeholder/320/180';\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"play-overlay\",\n                children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                  className: \"play-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-duration\",\n                children: video.duration || \"Video\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 21\n              }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"subtitle-badge\",\n                children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 25\n                }, this), \"CC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"video-title\",\n                children: video.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-subject\",\n                  children: getSubjectName(video.subject)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-class\",\n                  children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-tags\",\n                children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"topic-tag\",\n                  children: video.topic\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 39\n                }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"shared-tag\",\n                  children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 17\n          }, this), currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-video-player\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"youtube-style-layout\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-player\",\n                children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                  ref: ref => setVideoRef(ref),\n                  controls: true,\n                  autoPlay: true,\n                  playsInline: true,\n                  preload: \"metadata\",\n                  width: \"100%\",\n                  height: \"100%\",\n                  poster: getThumbnailUrl(video),\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    backgroundColor: '#000',\n                    objectFit: 'contain'\n                  },\n                  onError: e => setVideoError(`Failed to load video: ${video.title}`),\n                  onCanPlay: () => setVideoError(null),\n                  crossOrigin: \"anonymous\",\n                  children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                    src: video.signedVideoUrl || video.videoUrl,\n                    type: \"video/mp4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 29\n                  }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                    kind: \"subtitles\",\n                    src: subtitle.url,\n                    srcLang: subtitle.language,\n                    label: subtitle.languageName,\n                    default: subtitle.isDefault || index === 0\n                  }, `${subtitle.language}-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 31\n                  }, this)), \"Your browser does not support the video tag.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 27\n                }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                  src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                  title: video.title,\n                  frameBorder: \"0\",\n                  allowFullScreen: true,\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    border: 'none'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-icon\",\n                    children: \"\\u26A0\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: \"Video Unavailable\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: videoError || \"This video cannot be played at the moment.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"youtube-video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u2022\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Class \", video.className || video.class]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-actions\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"youtube-action-btn\",\n                    onClick: () => setCurrentVideoIndex(null),\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2715\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Close\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 19\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoLessons, \"oklSOgHrNX6QsXXj+boStxR0n+k=\", false, function () {\n  return [useSelector];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useSelector", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "TbAlertTriangle", "MdVerified", "getStudyMaterial", "jsxDEV", "_jsxDEV", "VideoLessons", "_s", "user", "state", "users", "videos", "setVideos", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "currentVideoIndex", "setCurrentVideoIndex", "videoRef", "setVideoRef", "videoError", "setVideoError", "isKiswahili", "getThumbnailUrl", "video", "thumbnailUrl", "videoID", "includes", "_video$videoID$match", "videoId", "match", "getSubjectName", "subject", "subjectMap", "handleShowVideo", "index", "filteredAndSortedVideos", "videoUrl", "signedVideoUrl", "console", "warn", "fetchVideos", "_response$data", "filters", "level", "class", "undefined", "type", "response", "data", "success", "_response$data2", "Error", "message", "err", "filtered", "filter", "_video$title", "_video$subject", "_video$topic", "matchesSearch", "title", "toLowerCase", "topic", "matchesLevel", "matchesClass", "className", "matchesSubject", "sort", "a", "b", "Date", "createdAt", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "e", "target", "onClick", "length", "map", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "duration", "subtitles", "sharedFromClass", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "style", "backgroundColor", "objectFit", "onCanPlay", "crossOrigin", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial } from '../../../apicalls/study';\nimport './index.css';\n\nconst VideoLessons = () => {\n  // Redux state\n  const { user } = useSelector(state => state.users);\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnailUrl) return video.thumbnailUrl;\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      const videoId = video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be')\n        ? video.videoID.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/)?.[1] || video.videoID\n        : video.videoID;\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/320/180';\n  };\n\n  const getSubjectName = (subject) => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Use the proper API call for study materials (videos)\n      const filters = {\n        level: selectedLevel,\n        class: selectedClass !== 'all' ? selectedClass : undefined,\n        subject: selectedSubject !== 'all' ? selectedSubject : undefined,\n        type: 'video' // Filter for videos only\n      };\n\n      const response = await getStudyMaterial(filters);\n\n      if (response.data?.success) {\n        setVideos(response.data.data || []);\n      } else {\n        throw new Error(response.data?.message || 'Failed to fetch videos');\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to load videos');\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      const matchesSearch = !searchTerm ||\n        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());\n\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  return (\n    <div className=\"video-lessons-container\">\n      <div className=\"video-lessons-header\">\n        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>\n        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"video-controls\">\n        <div className=\"search-section\">\n          <input\n            type=\"text\"\n            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n\n        <div className=\"filter-section\">\n          <select\n            value={selectedLevel}\n            onChange={(e) => setSelectedLevel(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"primary\">{isKiswahili ? 'Msingi' : 'Primary'}</option>\n            <option value=\"secondary\">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>\n            <option value=\"advance\">{isKiswahili ? 'Juu' : 'Advanced'}</option>\n          </select>\n\n          <select\n            value={selectedClass}\n            onChange={(e) => setSelectedClass(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n            {/* Add class options based on selected level */}\n          </select>\n\n          <select\n            value={selectedSubject}\n            onChange={(e) => setSelectedSubject(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n            {/* Add subject options */}\n          </select>\n        </div>\n      </div>\n\n      {/* Video Content */}\n      <div className=\"video-content\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-item\">\n                {/* Video Card */}\n                <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                  <div className=\"video-card-thumbnail\">\n                    <img\n                      src={getThumbnailUrl(video)}\n                      alt={video.title}\n                      className=\"thumbnail-image\"\n                      loading=\"lazy\"\n                      onError={(e) => {\n                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                          let videoId = video.videoID;\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                          const fallbacks = [\n                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                            '/api/placeholder/320/180'\n                          ];\n                          const currentSrc = e.target.src;\n                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                          if (currentIndex < fallbacks.length - 1) {\n                            e.target.src = fallbacks[currentIndex + 1];\n                          } else {\n                            e.target.src = '/api/placeholder/320/180';\n                          }\n                        } else {\n                          e.target.src = '/api/placeholder/320/180';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                    </div>\n                    <div className=\"video-duration\">\n                      {video.duration || \"Video\"}\n                    </div>\n                    {video.subtitles && video.subtitles.length > 0 && (\n                      <div className=\"subtitle-badge\">\n                        <TbInfoCircle />\n                        CC\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"video-card-content\">\n                    <h3 className=\"video-title\">{video.title}</h3>\n                    <div className=\"video-meta\">\n                      <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                      <span className=\"video-class\">\n                        {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                          ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)\n                          : `Form ${video.className || video.class}`}\n                      </span>\n                    </div>\n                    <div className=\"video-tags\">\n                      {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                      {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                        <span className=\"shared-tag\">\n                          {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}\n                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                            ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)\n                            : `Form ${video.sharedFromClass}`}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Inline Video Player */}\n                {currentVideoIndex === index && (\n                  <div className=\"inline-video-player\">\n                    <div className=\"youtube-style-layout\">\n                      <div className=\"youtube-video-player\">\n                        {video.videoUrl ? (\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"100%\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            }}\n                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}\n                            onCanPlay={() => setVideoError(null)}\n                            crossOrigin=\"anonymous\"\n                          >\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n                            Your browser does not support the video tag.\n                          </video>\n                        ) : video.videoID ? (\n                          <iframe\n                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                            title={video.title}\n                            frameBorder=\"0\"\n                            allowFullScreen\n                            style={{ width: '100%', height: '100%', border: 'none' }}\n                          ></iframe>\n                        ) : (\n                          <div className=\"video-error\">\n                            <div className=\"error-icon\">⚠️</div>\n                            <h3>Video Unavailable</h3>\n                            <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"youtube-video-info\">\n                        <h1 className=\"youtube-video-title\">{video.title}</h1>\n                        <div className=\"youtube-video-meta\">\n                          <span>{getSubjectName(video.subject)}</span>\n                          <span>•</span>\n                          <span>Class {video.className || video.class}</span>\n                        </div>\n                        <div className=\"youtube-video-actions\">\n                          <button\n                            className=\"youtube-action-btn\"\n                            onClick={() => setCurrentVideoIndex(null)}\n                          >\n                            <span>✕</span>\n                            <span>Close</span>\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM;IAAEC;EAAK,CAAC,GAAGX,WAAW,CAACY,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC;;EAElD;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8B,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAMsC,WAAW,GAAGZ,aAAa,KAAK,mBAAmB;;EAEzD;EACA,MAAMa,eAAe,GAAIC,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACC,YAAY,EAAE,OAAOD,KAAK,CAACC,YAAY;IACjD,IAAID,KAAK,CAACE,OAAO,IAAI,CAACF,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;MAAA,IAAAC,oBAAA;MAC7D,MAAMC,OAAO,GAAGL,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIH,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,UAAU,CAAC,GACvF,EAAAC,oBAAA,GAAAJ,KAAK,CAACE,OAAO,CAACI,KAAK,CAAC,oDAAoD,CAAC,cAAAF,oBAAA,uBAAzEA,oBAAA,CAA4E,CAAC,CAAC,KAAIJ,KAAK,CAACE,OAAO,GAC/FF,KAAK,CAACE,OAAO;MACjB,OAAQ,8BAA6BG,OAAQ,oBAAmB;IAClE;IACA,OAAO,0BAA0B;EACnC,CAAC;EAED,MAAME,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,UAAU,GAAG;MACjB,aAAa,EAAEX,WAAW,GAAG,UAAU,GAAG,aAAa;MACvD,SAAS,EAAEA,WAAW,GAAG,YAAY,GAAG,SAAS;MACjD,WAAW,EAAE,WAAW;MACxB,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG,SAAS;MAC9C,gBAAgB,EAAEA,WAAW,GAAG,kBAAkB,GAAG,gBAAgB;MACrE,QAAQ,EAAEA,WAAW,GAAG,OAAO,GAAG,QAAQ;MAC1C,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,WAAW,GAAG,WAAW;MACpD,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,OAAO,GAAG,WAAW;MAChD,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG;IACvC,CAAC;IACD,OAAOW,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO;EACvC,CAAC;;EAED;EACA,MAAME,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMX,KAAK,GAAGY,uBAAuB,CAACD,KAAK,CAAC;IAC5ClB,oBAAoB,CAACkB,KAAK,CAAC;IAC3Bd,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIG,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEa,QAAQ,KAAKb,KAAK,CAACa,QAAQ,CAACV,QAAQ,CAAC,eAAe,CAAC,IAAIH,KAAK,CAACa,QAAQ,CAACV,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF;QACA;QACA;QACAH,KAAK,CAACc,cAAc,GAAGd,KAAK,CAACa,QAAQ;MACvC,CAAC,CAAC,OAAO/B,KAAK,EAAE;QACdiC,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;QAC5DhB,KAAK,CAACc,cAAc,GAAGd,KAAK,CAACa,QAAQ;MACvC;IACF;EACF,CAAC;;EAED;EACA,MAAMI,WAAW,GAAGtD,WAAW,CAAC,YAAY;IAC1C,IAAI;MAAA,IAAAuD,cAAA;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMoC,OAAO,GAAG;QACdC,KAAK,EAAElC,aAAa;QACpBmC,KAAK,EAAEjC,aAAa,KAAK,KAAK,GAAGA,aAAa,GAAGkC,SAAS;QAC1Dd,OAAO,EAAElB,eAAe,KAAK,KAAK,GAAGA,eAAe,GAAGgC,SAAS;QAChEC,IAAI,EAAE,OAAO,CAAC;MAChB,CAAC;;MAED,MAAMC,QAAQ,GAAG,MAAMtD,gBAAgB,CAACiD,OAAO,CAAC;MAEhD,KAAAD,cAAA,GAAIM,QAAQ,CAACC,IAAI,cAAAP,cAAA,eAAbA,cAAA,CAAeQ,OAAO,EAAE;QAC1B/C,SAAS,CAAC6C,QAAQ,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACrC,CAAC,MAAM;QAAA,IAAAE,eAAA;QACL,MAAM,IAAIC,KAAK,CAAC,EAAAD,eAAA,GAAAH,QAAQ,CAACC,IAAI,cAAAE,eAAA,uBAAbA,eAAA,CAAeE,OAAO,KAAI,wBAAwB,CAAC;MACrE;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ/C,QAAQ,CAAC+C,GAAG,CAACD,OAAO,IAAI,uBAAuB,CAAC;IAClD,CAAC,SAAS;MACRhD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACK,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEnD;EACA,MAAMsB,uBAAuB,GAAGlD,OAAO,CAAC,MAAM;IAC5C,IAAIqE,QAAQ,GAAGrD,MAAM,CAACsD,MAAM,CAAChC,KAAK,IAAI;MAAA,IAAAiC,YAAA,EAAAC,cAAA,EAAAC,YAAA;MACpC,MAAMC,aAAa,GAAG,CAACpD,UAAU,MAAAiD,YAAA,GAC/BjC,KAAK,CAACqC,KAAK,cAAAJ,YAAA,uBAAXA,YAAA,CAAaK,WAAW,CAAC,CAAC,CAACnC,QAAQ,CAACnB,UAAU,CAACsD,WAAW,CAAC,CAAC,CAAC,OAAAJ,cAAA,GAC7DlC,KAAK,CAACQ,OAAO,cAAA0B,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,CAACnC,QAAQ,CAACnB,UAAU,CAACsD,WAAW,CAAC,CAAC,CAAC,OAAAH,YAAA,GAC/DnC,KAAK,CAACuC,KAAK,cAAAJ,YAAA,uBAAXA,YAAA,CAAaG,WAAW,CAAC,CAAC,CAACnC,QAAQ,CAACnB,UAAU,CAACsD,WAAW,CAAC,CAAC,CAAC;MAE/D,MAAME,YAAY,GAAGtD,aAAa,KAAK,KAAK,IAAIc,KAAK,CAACoB,KAAK,KAAKlC,aAAa;MAC7E,MAAMuD,YAAY,GAAGrD,aAAa,KAAK,KAAK,IAAIY,KAAK,CAAC0C,SAAS,KAAKtD,aAAa,IAAIY,KAAK,CAACqB,KAAK,KAAKjC,aAAa;MAClH,MAAMuD,cAAc,GAAGrD,eAAe,KAAK,KAAK,IAAIU,KAAK,CAACQ,OAAO,KAAKlB,eAAe;MAErF,OAAO8C,aAAa,IAAII,YAAY,IAAIC,YAAY,IAAIE,cAAc;IACxE,CAAC,CAAC;IAEF,OAAOZ,QAAQ,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAAC;EAC/E,CAAC,EAAE,CAACtE,MAAM,EAAEM,UAAU,EAAEE,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEvE;EACA7B,SAAS,CAAC,MAAM;IACdwD,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjB,oBACE7C,OAAA;IAAKsE,SAAS,EAAC,yBAAyB;IAAAO,QAAA,gBACtC7E,OAAA;MAAKsE,SAAS,EAAC,sBAAsB;MAAAO,QAAA,gBACnC7E,OAAA;QAAA6E,QAAA,EAAKnD,WAAW,GAAG,iBAAiB,GAAG;MAAe;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5DjF,OAAA;QAAA6E,QAAA,EAAInD,WAAW,GAAG,iCAAiC,GAAG;MAAoC;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAGNjF,OAAA;MAAKsE,SAAS,EAAC,gBAAgB;MAAAO,QAAA,gBAC7B7E,OAAA;QAAKsE,SAAS,EAAC,gBAAgB;QAAAO,QAAA,eAC7B7E,OAAA;UACEmD,IAAI,EAAC,MAAM;UACX+B,WAAW,EAAExD,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;UAClEyD,KAAK,EAAEvE,UAAW;UAClBwE,QAAQ,EAAGC,CAAC,IAAKxE,aAAa,CAACwE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/Cb,SAAS,EAAC;QAAc;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENjF,OAAA;QAAKsE,SAAS,EAAC,gBAAgB;QAAAO,QAAA,gBAC7B7E,OAAA;UACEmF,KAAK,EAAErE,aAAc;UACrBsE,QAAQ,EAAGC,CAAC,IAAKtE,gBAAgB,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAClDb,SAAS,EAAC,eAAe;UAAAO,QAAA,gBAEzB7E,OAAA;YAAQmF,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAEnD,WAAW,GAAG,QAAQ,GAAG;UAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrEjF,OAAA;YAAQmF,KAAK,EAAC,WAAW;YAAAN,QAAA,EAAEnD,WAAW,GAAG,WAAW,GAAG;UAAW;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5EjF,OAAA;YAAQmF,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAEnD,WAAW,GAAG,KAAK,GAAG;UAAU;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAETjF,OAAA;UACEmF,KAAK,EAAEnE,aAAc;UACrBoE,QAAQ,EAAGC,CAAC,IAAKpE,gBAAgB,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAClDb,SAAS,EAAC,eAAe;UAAAO,QAAA,eAEzB7E,OAAA;YAAQmF,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAEnD,WAAW,GAAG,eAAe,GAAG;UAAa;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtE,CAAC,eAETjF,OAAA;UACEmF,KAAK,EAAEjE,eAAgB;UACvBkE,QAAQ,EAAGC,CAAC,IAAKlE,kBAAkB,CAACkE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDb,SAAS,EAAC,eAAe;UAAAO,QAAA,eAEzB7E,OAAA;YAAQmF,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAEnD,WAAW,GAAG,aAAa,GAAG;UAAc;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjF,OAAA;MAAKsE,SAAS,EAAC,eAAe;MAAAO,QAAA,EAC3BrE,OAAO,gBACNR,OAAA;QAAKsE,SAAS,EAAC,eAAe;QAAAO,QAAA,gBAC5B7E,OAAA;UAAKsE,SAAS,EAAC;QAAiB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCjF,OAAA;UAAA6E,QAAA,EAAInD,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJvE,KAAK,gBACPV,OAAA;QAAKsE,SAAS,EAAC,aAAa;QAAAO,QAAA,gBAC1B7E,OAAA,CAACJ,eAAe;UAAC0E,SAAS,EAAC;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CjF,OAAA;UAAA6E,QAAA,EAAKnD,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7EjF,OAAA;UAAA6E,QAAA,EAAInE;QAAK;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdjF,OAAA;UAAQuF,OAAO,EAAE1C,WAAY;UAACyB,SAAS,EAAC,WAAW;UAAAO,QAAA,EAChDnD,WAAW,GAAG,aAAa,GAAG;QAAW;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJzC,uBAAuB,CAACgD,MAAM,GAAG,CAAC,gBACpCxF,OAAA;QAAKsE,SAAS,EAAC,aAAa;QAAAO,QAAA,EACzBrC,uBAAuB,CAACiD,GAAG,CAAC,CAAC7D,KAAK,EAAEW,KAAK,kBACxCvC,OAAA;UAAiBsE,SAAS,EAAC,YAAY;UAAAO,QAAA,gBAErC7E,OAAA;YAAKsE,SAAS,EAAC,YAAY;YAACiB,OAAO,EAAEA,CAAA,KAAMjD,eAAe,CAACC,KAAK,CAAE;YAAAsC,QAAA,gBAChE7E,OAAA;cAAKsE,SAAS,EAAC,sBAAsB;cAAAO,QAAA,gBACnC7E,OAAA;gBACE0F,GAAG,EAAE/D,eAAe,CAACC,KAAK,CAAE;gBAC5B+D,GAAG,EAAE/D,KAAK,CAACqC,KAAM;gBACjBK,SAAS,EAAC,iBAAiB;gBAC3B9D,OAAO,EAAC,MAAM;gBACdoF,OAAO,EAAGP,CAAC,IAAK;kBACd,IAAIzD,KAAK,CAACE,OAAO,IAAI,CAACF,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;oBAC7D,IAAIE,OAAO,GAAGL,KAAK,CAACE,OAAO;oBAC3B,MAAMI,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;oBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;oBACpC,MAAM4D,SAAS,GAAG,CACf,8BAA6B5D,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;oBACD,MAAM6D,UAAU,GAAGT,CAAC,CAACC,MAAM,CAACI,GAAG;oBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACC,GAAG,IAAIH,UAAU,CAAC/D,QAAQ,CAACkE,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC1F,IAAIJ,YAAY,GAAGF,SAAS,CAACL,MAAM,GAAG,CAAC,EAAE;sBACvCH,CAAC,CAACC,MAAM,CAACI,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;oBAC5C,CAAC,MAAM;sBACLV,CAAC,CAACC,MAAM,CAACI,GAAG,GAAG,0BAA0B;oBAC3C;kBACF,CAAC,MAAM;oBACLL,CAAC,CAACC,MAAM,CAACI,GAAG,GAAG,0BAA0B;kBAC3C;gBACF;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFjF,OAAA;gBAAKsE,SAAS,EAAC,cAAc;gBAAAO,QAAA,eAC3B7E,OAAA,CAACP,YAAY;kBAAC6E,SAAS,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACNjF,OAAA;gBAAKsE,SAAS,EAAC,gBAAgB;gBAAAO,QAAA,EAC5BjD,KAAK,CAACwE,QAAQ,IAAI;cAAO;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,EACLrD,KAAK,CAACyE,SAAS,IAAIzE,KAAK,CAACyE,SAAS,CAACb,MAAM,GAAG,CAAC,iBAC5CxF,OAAA;gBAAKsE,SAAS,EAAC,gBAAgB;gBAAAO,QAAA,gBAC7B7E,OAAA,CAACL,YAAY;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,MAElB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENjF,OAAA;cAAKsE,SAAS,EAAC,oBAAoB;cAAAO,QAAA,gBACjC7E,OAAA;gBAAIsE,SAAS,EAAC,aAAa;gBAAAO,QAAA,EAAEjD,KAAK,CAACqC;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9CjF,OAAA;gBAAKsE,SAAS,EAAC,YAAY;gBAAAO,QAAA,gBACzB7E,OAAA;kBAAMsE,SAAS,EAAC,eAAe;kBAAAO,QAAA,EAAE1C,cAAc,CAACP,KAAK,CAACQ,OAAO;gBAAC;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtEjF,OAAA;kBAAMsE,SAAS,EAAC,aAAa;kBAAAO,QAAA,EAC1B/D,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEY,WAAW,GAAI,aAAYE,KAAK,CAAC0C,SAAS,IAAI1C,KAAK,CAACqB,KAAM,EAAC,GAAI,SAAQrB,KAAK,CAAC0C,SAAS,IAAI1C,KAAK,CAACqB,KAAM,EAAC,GACvG,QAAOrB,KAAK,CAAC0C,SAAS,IAAI1C,KAAK,CAACqB,KAAM;gBAAC;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNjF,OAAA;gBAAKsE,SAAS,EAAC,YAAY;gBAAAO,QAAA,GACxBjD,KAAK,CAACuC,KAAK,iBAAInE,OAAA;kBAAMsE,SAAS,EAAC,WAAW;kBAAAO,QAAA,EAAEjD,KAAK,CAACuC;gBAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/DrD,KAAK,CAAC0E,eAAe,IAAI1E,KAAK,CAAC0E,eAAe,MAAM1E,KAAK,CAAC0C,SAAS,IAAI1C,KAAK,CAACqB,KAAK,CAAC,iBAClFjD,OAAA;kBAAMsE,SAAS,EAAC,YAAY;kBAAAO,QAAA,GACzBnD,WAAW,GAAG,qBAAqB,GAAG,cAAc,EACpDZ,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEY,WAAW,GAAI,aAAYE,KAAK,CAAC0E,eAAgB,EAAC,GAAI,SAAQ1E,KAAK,CAAC0E,eAAgB,EAAC,GACrF,QAAO1E,KAAK,CAAC0E,eAAgB,EAAC;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL7D,iBAAiB,KAAKmB,KAAK,iBAC1BvC,OAAA;YAAKsE,SAAS,EAAC,qBAAqB;YAAAO,QAAA,eAClC7E,OAAA;cAAKsE,SAAS,EAAC,sBAAsB;cAAAO,QAAA,gBACnC7E,OAAA;gBAAKsE,SAAS,EAAC,sBAAsB;gBAAAO,QAAA,EAClCjD,KAAK,CAACa,QAAQ,gBACbzC,OAAA;kBACEuG,GAAG,EAAGA,GAAG,IAAKhF,WAAW,CAACgF,GAAG,CAAE;kBAC/BC,QAAQ;kBACRC,QAAQ;kBACRC,WAAW;kBACXC,OAAO,EAAC,UAAU;kBAClBC,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAC,MAAM;kBACbC,MAAM,EAAEnF,eAAe,CAACC,KAAK,CAAE;kBAC/BmF,KAAK,EAAE;oBACLH,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdG,eAAe,EAAE,MAAM;oBACvBC,SAAS,EAAE;kBACb,CAAE;kBACFrB,OAAO,EAAGP,CAAC,IAAK5D,aAAa,CAAE,yBAAwBG,KAAK,CAACqC,KAAM,EAAC,CAAE;kBACtEiD,SAAS,EAAEA,CAAA,KAAMzF,aAAa,CAAC,IAAI,CAAE;kBACrC0F,WAAW,EAAC,WAAW;kBAAAtC,QAAA,gBAEvB7E,OAAA;oBAAQ0F,GAAG,EAAE9D,KAAK,CAACc,cAAc,IAAId,KAAK,CAACa,QAAS;oBAACU,IAAI,EAAC;kBAAW;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACvErD,KAAK,CAACyE,SAAS,IAAIzE,KAAK,CAACyE,SAAS,CAACb,MAAM,GAAG,CAAC,IAAI5D,KAAK,CAACyE,SAAS,CAACZ,GAAG,CAAC,CAAC2B,QAAQ,EAAE7E,KAAK,kBACpFvC,OAAA;oBAEEqH,IAAI,EAAC,WAAW;oBAChB3B,GAAG,EAAE0B,QAAQ,CAACnB,GAAI;oBAClBqB,OAAO,EAAEF,QAAQ,CAACG,QAAS;oBAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;oBAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAIpF,KAAK,KAAK;kBAAE,GALrC,GAAE6E,QAAQ,CAACG,QAAS,IAAGhF,KAAM,EAAC;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,GACNrD,KAAK,CAACE,OAAO,gBACf9B,OAAA;kBACE0F,GAAG,EAAG,iCAAgC9D,KAAK,CAACE,OAAQ,mBAAmB;kBACvEmC,KAAK,EAAErC,KAAK,CAACqC,KAAM;kBACnB2D,WAAW,EAAC,GAAG;kBACfC,eAAe;kBACfd,KAAK,EAAE;oBAAEH,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE,MAAM;oBAAEiB,MAAM,EAAE;kBAAO;gBAAE;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,gBAEVjF,OAAA;kBAAKsE,SAAS,EAAC,aAAa;kBAAAO,QAAA,gBAC1B7E,OAAA;oBAAKsE,SAAS,EAAC,YAAY;oBAAAO,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpCjF,OAAA;oBAAA6E,QAAA,EAAI;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1BjF,OAAA;oBAAA6E,QAAA,EAAIrD,UAAU,IAAI;kBAA4C;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENjF,OAAA;gBAAKsE,SAAS,EAAC,oBAAoB;gBAAAO,QAAA,gBACjC7E,OAAA;kBAAIsE,SAAS,EAAC,qBAAqB;kBAAAO,QAAA,EAAEjD,KAAK,CAACqC;gBAAK;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtDjF,OAAA;kBAAKsE,SAAS,EAAC,oBAAoB;kBAAAO,QAAA,gBACjC7E,OAAA;oBAAA6E,QAAA,EAAO1C,cAAc,CAACP,KAAK,CAACQ,OAAO;kBAAC;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5CjF,OAAA;oBAAA6E,QAAA,EAAM;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACdjF,OAAA;oBAAA6E,QAAA,GAAM,QAAM,EAACjD,KAAK,CAAC0C,SAAS,IAAI1C,KAAK,CAACqB,KAAK;kBAAA;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACNjF,OAAA;kBAAKsE,SAAS,EAAC,uBAAuB;kBAAAO,QAAA,eACpC7E,OAAA;oBACEsE,SAAS,EAAC,oBAAoB;oBAC9BiB,OAAO,EAAEA,CAAA,KAAMlE,oBAAoB,CAAC,IAAI,CAAE;oBAAAwD,QAAA,gBAE1C7E,OAAA;sBAAA6E,QAAA,EAAM;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACdjF,OAAA;sBAAA6E,QAAA,EAAM;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA,GAhJO1C,KAAK;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiJV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENjF,OAAA;QAAKsE,SAAS,EAAC,aAAa;QAAAO,QAAA,gBAC1B7E,OAAA,CAACN,eAAe;UAAC4E,SAAS,EAAC;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CjF,OAAA;UAAA6E,QAAA,EAAKnD,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1EjF,OAAA;UAAA6E,QAAA,EAAInD,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJjF,OAAA;UAAGsE,SAAS,EAAC,YAAY;UAAAO,QAAA,EAAEnD,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/E,EAAA,CA3VID,YAAY;EAAA,QAECT,WAAW;AAAA;AAAAuI,EAAA,GAFxB9H,YAAY;AA6VlB,eAAeA,YAAY;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}