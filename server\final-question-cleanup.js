const mongoose = require('mongoose');
require('dotenv').config();

const Question = require('./models/questionModel');

async function finalQuestionCleanup() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');

    // Find the specific problematic question
    const problematicQuestion = await Question.findOne({
      name: { $regex: /Write your answer in Name the disease/i }
    });

    if (!problematicQuestion) {
      console.log('✅ No problematic questions found');
      return;
    }

    console.log('🔍 Found problematic question:');
    console.log(`ID: ${problematicQuestion._id}`);
    console.log(`Current text: ${problematicQuestion.name}`);

    // Fix the question by removing the malformed instruction
    let cleanedText = problematicQuestion.name
      .replace(/Write your answer in\s*/i, '')
      .replace(/^\s*,?\s*/, '') // Remove leading comma or spaces
      .trim();

    // Ensure proper capitalization
    if (cleanedText) {
      cleanedText = cleanedText.charAt(0).toUpperCase() + cleanedText.slice(1);
    }

    console.log(`\n🔧 Fixing question...`);
    console.log(`Before: ${problematicQuestion.name}`);
    console.log(`After:  ${cleanedText}`);

    // Update the question
    await Question.findByIdAndUpdate(problematicQuestion._id, {
      name: cleanedText
    });

    console.log('✅ Question fixed successfully!');

    // Final verification - check for any remaining instruction remnants
    console.log('\n🔍 Final verification across all questions...');
    
    const remainingIssues = await Question.find({
      $or: [
        { name: { $regex: /WRITE\s+YOUR?\s+ANSWER\s+IN/i } },
        { name: { $regex: /WRITE\s+IN\s+CAPITAL/i } },
        { name: { $regex: /USE\s+CAPITAL\s+LETTER/i } },
        { name: { $regex: /capital\s+letter.*fullstop/i } },
        { name: { $regex: /S\s+AND\s+FULLSTOP/i } },
        { name: { $regex: /Write your answer in\s+[A-Z]/i } }
      ]
    });

    console.log(`📊 Total questions with instruction remnants: ${remainingIssues.length}`);

    if (remainingIssues.length > 0) {
      console.log('\n⚠️ Questions that still contain instruction remnants:');
      remainingIssues.forEach((question, index) => {
        console.log(`${index + 1}. ID: ${question._id}`);
        console.log(`   Text: ${question.name.substring(0, 120)}...`);
        console.log('');
      });
    } else {
      console.log('\n🎉 SUCCESS! All unwanted instructions have been completely removed from all questions!');
      console.log('✅ The database is now clean of instruction remnants.');
    }

    // Summary statistics
    console.log('\n📊 CLEANUP SUMMARY:');
    console.log('✅ Phase 1: Found and fixed 24 questions with main instructions');
    console.log('✅ Phase 2: Cleaned 21 questions with instruction remnants');
    console.log('✅ Phase 3: Fixed final problematic question');
    console.log('🎯 Total questions processed and cleaned successfully!');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
}

// Run the script
finalQuestionCleanup();
