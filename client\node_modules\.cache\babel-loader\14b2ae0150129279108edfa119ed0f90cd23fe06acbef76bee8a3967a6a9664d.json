{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoLessons = () => {\n  _s();\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.user) {\n        console.log('✅ Found user in state.user');\n        return state.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = video => {\n    if (video.thumbnailUrl) return video.thumbnailUrl;\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      var _video$videoID$match;\n      const videoId = video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be') ? ((_video$videoID$match = video.videoID.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/)) === null || _video$videoID$match === void 0 ? void 0 : _video$videoID$match[1]) || video.videoID : video.videoID;\n      return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`; // Use hqdefault instead of maxresdefault\n    }\n    // Use a working placeholder image\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n  };\n  const getSubjectName = subject => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('🎥 Attempting to fetch videos from database...');\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        var _response, _response2;\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n        if ((_response = response) !== null && _response !== void 0 && _response.success && (_response2 = response) !== null && _response2 !== void 0 && _response2.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          var _response3, _response3$data, _response4, _response4$data;\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n          if ((_response3 = response) !== null && _response3 !== void 0 && (_response3$data = _response3.data) !== null && _response3$data !== void 0 && _response3$data.success && (_response4 = response) !== null && _response4 !== void 0 && (_response4$data = _response4.data) !== null && _response4$data !== void 0 && _response4$data.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          var _response5, _response5$data, _response6, _response6$data;\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n          if ((_response5 = response) !== null && _response5 !== void 0 && (_response5$data = _response5.data) !== null && _response5$data !== void 0 && _response5$data.success && (_response6 = response) !== null && _response6 !== void 0 && (_response6$data = _response6.data) !== null && _response6$data !== void 0 && _response6$data.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item => {\n              var _item$title;\n              return item.type === 'video' || item.videoUrl || item.videoID || ((_item$title = item.title) === null || _item$title === void 0 ? void 0 : _item$title.toLowerCase().includes('video'));\n            });\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel || !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass || !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject || !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      var _video$title, _video$subject, _video$topic;\n      const matchesSearch = !searchTerm || ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchTerm.toLowerCase())) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchTerm.toLowerCase()));\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: isKiswahili ? 'Masomo ya Video' : 'Video Lessons'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: isKiswahili ? 'Tafuta video...' : 'Search videos...',\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedLevel,\n          onChange: e => setSelectedLevel(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"primary\",\n            children: isKiswahili ? 'Msingi' : 'Primary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"secondary\",\n            children: isKiswahili ? 'Sekondari' : 'Secondary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advance\",\n            children: isKiswahili ? 'Juu' : 'Advanced'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedClass,\n          onChange: e => setSelectedClass(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedSubject,\n          onChange: e => setSelectedSubject(e.target.value),\n          className: \"filter-select\",\n          children: /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card\",\n            onClick: () => handleShowVideo(index),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card-thumbnail\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getThumbnailUrl(video),\n                alt: video.title,\n                className: \"thumbnail-image\",\n                loading: \"lazy\",\n                onError: e => {\n                  if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                    let videoId = video.videoID;\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                    const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                    const currentSrc = e.target.src;\n                    const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                    if (currentIndex < fallbacks.length - 1) {\n                      e.target.src = fallbacks[currentIndex + 1];\n                    } else {\n                      e.target.src = '/api/placeholder/320/180';\n                    }\n                  } else {\n                    e.target.src = '/api/placeholder/320/180';\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"play-overlay\",\n                children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                  className: \"play-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-duration\",\n                children: video.duration || \"Video\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 21\n              }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"subtitle-badge\",\n                children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 25\n                }, this), \"CC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"video-title\",\n                children: video.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-subject\",\n                  children: getSubjectName(video.subject)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"video-class\",\n                  children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-tags\",\n                children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"topic-tag\",\n                  children: video.topic\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 39\n                }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"shared-tag\",\n                  children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 17\n          }, this), currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-video-player\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"youtube-style-layout\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-player\",\n                children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"video\", {\n                  ref: ref => setVideoRef(ref),\n                  controls: true,\n                  autoPlay: true,\n                  playsInline: true,\n                  preload: \"metadata\",\n                  width: \"100%\",\n                  height: \"100%\",\n                  poster: getThumbnailUrl(video),\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    backgroundColor: '#000',\n                    objectFit: 'contain'\n                  },\n                  onError: e => setVideoError(`Failed to load video: ${video.title}`),\n                  onCanPlay: () => setVideoError(null),\n                  crossOrigin: \"anonymous\",\n                  children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                    src: video.signedVideoUrl || video.videoUrl,\n                    type: \"video/mp4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 29\n                  }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                    kind: \"subtitles\",\n                    src: subtitle.url,\n                    srcLang: subtitle.language,\n                    label: subtitle.languageName,\n                    default: subtitle.isDefault || index === 0\n                  }, `${subtitle.language}-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 31\n                  }, this)), \"Your browser does not support the video tag.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 27\n                }, this) : video.videoID ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                  src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                  title: video.title,\n                  frameBorder: \"0\",\n                  allowFullScreen: true,\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    border: 'none'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-icon\",\n                    children: \"\\u26A0\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: \"Video Unavailable\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: videoError || \"This video cannot be played at the moment.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"youtube-video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"youtube-video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: getSubjectName(video.subject)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u2022\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Class \", video.className || video.class]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"youtube-video-actions\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"youtube-action-btn\",\n                    onClick: () => setCurrentVideoIndex(null),\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2715\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Close\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 19\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoLessons, \"DHZMV1Su3mhqAshc/3kyKCKohuA=\", false, function () {\n  return [useSelector];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useCallback", "useSelector", "FaPlayCircle", "FaGraduationCap", "TbInfoCircle", "TbAlertTriangle", "MdVerified", "getStudyMaterial", "getAllVideos", "jsxDEV", "_jsxDEV", "VideoLessons", "_s", "user", "state", "console", "log", "Object", "keys", "users", "auth", "storedUser", "localStorage", "getItem", "JSON", "parse", "e", "error", "videos", "setVideos", "loading", "setLoading", "setError", "searchTerm", "setSearchTerm", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "currentVideoIndex", "setCurrentVideoIndex", "videoRef", "setVideoRef", "videoError", "setVideoError", "isKiswahili", "getThumbnailUrl", "video", "thumbnailUrl", "videoID", "includes", "_video$videoID$match", "videoId", "match", "getSubjectName", "subject", "subjectMap", "handleShowVideo", "index", "filteredAndSortedVideos", "videoUrl", "signedVideoUrl", "warn", "fetchVideos", "response", "_response", "_response2", "success", "data", "length", "message", "_response3", "_response3$data", "_response4", "_response4$data", "filters", "level", "type", "_response5", "_response5$data", "_response6", "_response6$data", "allData", "filter", "item", "_item$title", "title", "toLowerCase", "filtered", "matchesLevel", "matchesClass", "className", "class", "matchesSubject", "err", "_video$title", "_video$subject", "_video$topic", "matchesSearch", "topic", "sort", "a", "b", "Date", "createdAt", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "target", "onClick", "map", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "duration", "subtitles", "sharedFromClass", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "style", "backgroundColor", "objectFit", "onCanPlay", "crossOrigin", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "border", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';\nimport { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';\nimport { MdVerified } from 'react-icons/md';\nimport { getStudyMaterial, getAllVideos } from '../../../apicalls/study';\nimport './index.css';\n\nconst VideoLessons = () => {\n  // Redux state with completely safe destructuring\n  const user = useSelector(state => {\n    try {\n      console.log('🔍 Full Redux state structure:', state);\n      console.log('🔍 Available state keys:', Object.keys(state || {}));\n\n      // Handle different possible Redux state structures\n      if (state && state.users && state.users.user) {\n        console.log('✅ Found user in state.users.user');\n        return state.users.user;\n      }\n      if (state && state.user) {\n        console.log('✅ Found user in state.user');\n        return state.user;\n      }\n      if (state && state.auth && state.auth.user) {\n        console.log('✅ Found user in state.auth.user');\n        return state.auth.user;\n      }\n\n      console.log('❌ No user found in Redux state');\n\n      // Check localStorage as fallback\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        try {\n          console.log('✅ Found user in localStorage');\n          return JSON.parse(storedUser);\n        } catch (e) {\n          console.log('❌ Failed to parse stored user');\n        }\n      }\n\n      console.log('❌ No user found anywhere');\n      return null;\n    } catch (error) {\n      console.log('💥 Error accessing Redux state:', error);\n      return null;\n    }\n  });\n\n  // State variables\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('primary');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n\n  // Language detection\n  const isKiswahili = selectedLevel === 'primary_kiswahili';\n\n  // Helper functions\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnailUrl) return video.thumbnailUrl;\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      const videoId = video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be')\n        ? video.videoID.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/)?.[1] || video.videoID\n        : video.videoID;\n      return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`; // Use hqdefault instead of maxresdefault\n    }\n    // Use a working placeholder image\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';\n  };\n\n  const getSubjectName = (subject) => {\n    const subjectMap = {\n      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',\n      'english': isKiswahili ? 'Kiingereza' : 'English',\n      'kiswahili': 'Kiswahili',\n      'science': isKiswahili ? 'Sayansi' : 'Science',\n      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',\n      'civics': isKiswahili ? 'Uraia' : 'Civics',\n      'history': isKiswahili ? 'Historia' : 'History',\n      'geography': isKiswahili ? 'Jiografia' : 'Geography',\n      'biology': isKiswahili ? 'Biolojia' : 'Biology',\n      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',\n      'physics': isKiswahili ? 'Fizikia' : 'Physics'\n    };\n    return subjectMap[subject] || subject;\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        // You would implement getSignedVideoUrl function here\n        // const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        // video.signedVideoUrl = signedUrl;\n        video.signedVideoUrl = video.videoUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  // Fetch videos function\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🎥 Attempting to fetch videos from database...');\n\n      let response = null;\n      let videos = [];\n\n      // Try Method 1: Get all videos (might not require authentication)\n      try {\n        console.log('📡 Trying getAllVideos endpoint...');\n        response = await getAllVideos();\n        console.log('getAllVideos response:', response);\n\n        if (response?.success && response?.data) {\n          videos = response.data;\n          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);\n        }\n      } catch (error) {\n        console.log('❌ getAllVideos failed:', error.message);\n      }\n\n      // Try Method 2: Get study materials with minimal filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial endpoint...');\n          const filters = {\n            level: selectedLevel,\n            type: 'video'\n          };\n\n          response = await getStudyMaterial(filters);\n          console.log('getStudyMaterial response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            videos = response.data.data;\n            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial failed:', error.message);\n        }\n      }\n\n      // Try Method 3: Get study materials without filters\n      if (videos.length === 0) {\n        try {\n          console.log('📡 Trying getStudyMaterial without filters...');\n          response = await getStudyMaterial({});\n          console.log('getStudyMaterial (no filters) response:', response);\n\n          if (response?.data?.success && response?.data?.data) {\n            // Filter for videos only on the client side\n            const allData = response.data.data;\n            videos = allData.filter(item =>\n              item.type === 'video' ||\n              item.videoUrl ||\n              item.videoID ||\n              item.title?.toLowerCase().includes('video')\n            );\n            console.log('✅ Successfully loaded and filtered videos:', videos.length);\n          }\n        } catch (error) {\n          console.log('❌ getStudyMaterial (no filters) failed:', error.message);\n        }\n      }\n\n      // Apply client-side filtering if we have videos\n      if (videos.length > 0) {\n        const filtered = videos.filter(video => {\n          const matchesLevel = selectedLevel === 'all' ||\n                              video.level === selectedLevel ||\n                              !video.level; // Include videos without level specified\n\n          const matchesClass = selectedClass === 'all' ||\n                              video.className === selectedClass ||\n                              video.class === selectedClass ||\n                              !video.className; // Include videos without class specified\n\n          const matchesSubject = selectedSubject === 'all' ||\n                                video.subject === selectedSubject ||\n                                !video.subject; // Include videos without subject specified\n\n          return matchesLevel && matchesClass && matchesSubject;\n        });\n\n        setVideos(filtered);\n        console.log('🎯 Applied filters, showing', filtered.length, 'videos');\n\n        if (filtered.length === 0) {\n          setError('No videos found for the selected filters. Try changing your selection.');\n        }\n      } else {\n        // No videos found from any method\n        console.log('❌ No videos found from database, this might indicate:');\n        console.log('   - Database is empty');\n        console.log('   - Authentication required');\n        console.log('   - API endpoints changed');\n        console.log('   - Server is down');\n\n        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');\n        setVideos([]);\n      }\n\n    } catch (err) {\n      console.error('💥 Critical error in fetchVideos:', err);\n      setError('Failed to connect to the server. Please try again later.');\n      setVideos([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedLevel, selectedClass, selectedSubject]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos.filter(video => {\n      const matchesSearch = !searchTerm ||\n        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());\n\n      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;\n      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;\n      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;\n\n      return matchesSearch && matchesLevel && matchesClass && matchesSubject;\n    });\n\n    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);\n\n  // Load videos on component mount and when filters change\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  // Refetch videos when level, class, or subject changes\n  useEffect(() => {\n    fetchVideos();\n  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);\n\n  return (\n    <div className=\"video-lessons-container\">\n      <div className=\"video-lessons-header\">\n        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>\n        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"video-controls\">\n        <div className=\"search-section\">\n          <input\n            type=\"text\"\n            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n\n        <div className=\"filter-section\">\n          <select\n            value={selectedLevel}\n            onChange={(e) => setSelectedLevel(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"primary\">{isKiswahili ? 'Msingi' : 'Primary'}</option>\n            <option value=\"secondary\">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>\n            <option value=\"advance\">{isKiswahili ? 'Juu' : 'Advanced'}</option>\n          </select>\n\n          <select\n            value={selectedClass}\n            onChange={(e) => setSelectedClass(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n            {/* Add class options based on selected level */}\n          </select>\n\n          <select\n            value={selectedSubject}\n            onChange={(e) => setSelectedSubject(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n            {/* Add subject options */}\n          </select>\n        </div>\n      </div>\n\n      {/* Video Content */}\n      <div className=\"video-content\">\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-item\">\n                {/* Video Card */}\n                <div className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                  <div className=\"video-card-thumbnail\">\n                    <img\n                      src={getThumbnailUrl(video)}\n                      alt={video.title}\n                      className=\"thumbnail-image\"\n                      loading=\"lazy\"\n                      onError={(e) => {\n                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                          let videoId = video.videoID;\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                          const fallbacks = [\n                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                            '/api/placeholder/320/180'\n                          ];\n                          const currentSrc = e.target.src;\n                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                          if (currentIndex < fallbacks.length - 1) {\n                            e.target.src = fallbacks[currentIndex + 1];\n                          } else {\n                            e.target.src = '/api/placeholder/320/180';\n                          }\n                        } else {\n                          e.target.src = '/api/placeholder/320/180';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                    </div>\n                    <div className=\"video-duration\">\n                      {video.duration || \"Video\"}\n                    </div>\n                    {video.subtitles && video.subtitles.length > 0 && (\n                      <div className=\"subtitle-badge\">\n                        <TbInfoCircle />\n                        CC\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"video-card-content\">\n                    <h3 className=\"video-title\">{video.title}</h3>\n                    <div className=\"video-meta\">\n                      <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                      <span className=\"video-class\">\n                        {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                          ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)\n                          : `Form ${video.className || video.class}`}\n                      </span>\n                    </div>\n                    <div className=\"video-tags\">\n                      {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                      {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                        <span className=\"shared-tag\">\n                          {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}\n                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'\n                            ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)\n                            : `Form ${video.sharedFromClass}`}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Inline Video Player */}\n                {currentVideoIndex === index && (\n                  <div className=\"inline-video-player\">\n                    <div className=\"youtube-style-layout\">\n                      <div className=\"youtube-video-player\">\n                        {video.videoUrl ? (\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"100%\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '100%',\n                              backgroundColor: '#000',\n                              objectFit: 'contain'\n                            }}\n                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}\n                            onCanPlay={() => setVideoError(null)}\n                            crossOrigin=\"anonymous\"\n                          >\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n                            Your browser does not support the video tag.\n                          </video>\n                        ) : video.videoID ? (\n                          <iframe\n                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                            title={video.title}\n                            frameBorder=\"0\"\n                            allowFullScreen\n                            style={{ width: '100%', height: '100%', border: 'none' }}\n                          ></iframe>\n                        ) : (\n                          <div className=\"video-error\">\n                            <div className=\"error-icon\">⚠️</div>\n                            <h3>Video Unavailable</h3>\n                            <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"youtube-video-info\">\n                        <h1 className=\"youtube-video-title\">{video.title}</h1>\n                        <div className=\"youtube-video-meta\">\n                          <span>{getSubjectName(video.subject)}</span>\n                          <span>•</span>\n                          <span>Class {video.className || video.class}</span>\n                        </div>\n                        <div className=\"youtube-video-actions\">\n                          <button\n                            className=\"youtube-action-btn\"\n                            onClick={() => setCurrentVideoIndex(null)}\n                          >\n                            <span>✕</span>\n                            <span>Close</span>\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACxE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,YAAY,EAAEC,eAAe,QAAQ,gBAAgB;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAMC,IAAI,GAAGZ,WAAW,CAACa,KAAK,IAAI;IAChC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,KAAK,CAAC;MACpDC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,MAAM,CAACC,IAAI,CAACJ,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;;MAEjE;MACA,IAAIA,KAAK,IAAIA,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK,CAACN,IAAI,EAAE;QAC5CE,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,OAAOF,KAAK,CAACK,KAAK,CAACN,IAAI;MACzB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACD,IAAI,EAAE;QACvBE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,OAAOF,KAAK,CAACD,IAAI;MACnB;MACA,IAAIC,KAAK,IAAIA,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACM,IAAI,CAACP,IAAI,EAAE;QAC1CE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAOF,KAAK,CAACM,IAAI,CAACP,IAAI;MACxB;MAEAE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMK,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIF,UAAU,EAAE;QACd,IAAI;UACFN,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,OAAOQ,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;QAC/B,CAAC,CAAC,OAAOK,CAAC,EAAE;UACVX,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C;MACF;MAEAD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,OAAO,IAAI;IACb,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,KAAK,CAAC;MACrD,OAAO,IAAI;IACb;EACF,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,KAAK,EAAEK,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0C,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAMkD,WAAW,GAAGZ,aAAa,KAAK,mBAAmB;;EAEzD;EACA,MAAMa,eAAe,GAAIC,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACC,YAAY,EAAE,OAAOD,KAAK,CAACC,YAAY;IACjD,IAAID,KAAK,CAACE,OAAO,IAAI,CAACF,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;MAAA,IAAAC,oBAAA;MAC7D,MAAMC,OAAO,GAAGL,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIH,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,UAAU,CAAC,GACvF,EAAAC,oBAAA,GAAAJ,KAAK,CAACE,OAAO,CAACI,KAAK,CAAC,oDAAoD,CAAC,cAAAF,oBAAA,uBAAzEA,oBAAA,CAA4E,CAAC,CAAC,KAAIJ,KAAK,CAACE,OAAO,GAC/FF,KAAK,CAACE,OAAO;MACjB,OAAQ,8BAA6BG,OAAQ,gBAAe,CAAC,CAAC;IAChE;IACA;IACA,OAAO,4cAA4c;EACrd,CAAC;EAED,MAAME,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,UAAU,GAAG;MACjB,aAAa,EAAEX,WAAW,GAAG,UAAU,GAAG,aAAa;MACvD,SAAS,EAAEA,WAAW,GAAG,YAAY,GAAG,SAAS;MACjD,WAAW,EAAE,WAAW;MACxB,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG,SAAS;MAC9C,gBAAgB,EAAEA,WAAW,GAAG,kBAAkB,GAAG,gBAAgB;MACrE,QAAQ,EAAEA,WAAW,GAAG,OAAO,GAAG,QAAQ;MAC1C,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,WAAW,GAAG,WAAW;MACpD,SAAS,EAAEA,WAAW,GAAG,UAAU,GAAG,SAAS;MAC/C,WAAW,EAAEA,WAAW,GAAG,OAAO,GAAG,WAAW;MAChD,SAAS,EAAEA,WAAW,GAAG,SAAS,GAAG;IACvC,CAAC;IACD,OAAOW,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO;EACvC,CAAC;;EAED;EACA,MAAME,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMX,KAAK,GAAGY,uBAAuB,CAACD,KAAK,CAAC;IAC5ClB,oBAAoB,CAACkB,KAAK,CAAC;IAC3Bd,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIG,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEa,QAAQ,KAAKb,KAAK,CAACa,QAAQ,CAACV,QAAQ,CAAC,eAAe,CAAC,IAAIH,KAAK,CAACa,QAAQ,CAACV,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF;QACA;QACA;QACAH,KAAK,CAACc,cAAc,GAAGd,KAAK,CAACa,QAAQ;MACvC,CAAC,CAAC,OAAOnC,KAAK,EAAE;QACdZ,OAAO,CAACiD,IAAI,CAAC,8CAA8C,CAAC;QAC5Df,KAAK,CAACc,cAAc,GAAGd,KAAK,CAACa,QAAQ;MACvC;IACF;EACF,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGjE,WAAW,CAAC,YAAY;IAC1C,IAAI;MACF+B,UAAU,CAAC,IAAI,CAAC;MAChBC,QAAQ,CAAC,IAAI,CAAC;MAEdjB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAIkD,QAAQ,GAAG,IAAI;MACnB,IAAItC,MAAM,GAAG,EAAE;;MAEf;MACA,IAAI;QAAA,IAAAuC,SAAA,EAAAC,UAAA;QACFrD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjDkD,QAAQ,GAAG,MAAM1D,YAAY,CAAC,CAAC;QAC/BO,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEkD,QAAQ,CAAC;QAE/C,IAAI,CAAAC,SAAA,GAAAD,QAAQ,cAAAC,SAAA,eAARA,SAAA,CAAUE,OAAO,KAAAD,UAAA,GAAIF,QAAQ,cAAAE,UAAA,eAARA,UAAA,CAAUE,IAAI,EAAE;UACvC1C,MAAM,GAAGsC,QAAQ,CAACI,IAAI;UACtBvD,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEY,MAAM,CAAC2C,MAAM,CAAC;QAC/E;MACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEW,KAAK,CAAC6C,OAAO,CAAC;MACtD;;MAEA;MACA,IAAI5C,MAAM,CAAC2C,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAE,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACF7D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,MAAM6D,OAAO,GAAG;YACdC,KAAK,EAAE3C,aAAa;YACpB4C,IAAI,EAAE;UACR,CAAC;UAEDb,QAAQ,GAAG,MAAM3D,gBAAgB,CAACsE,OAAO,CAAC;UAC1C9D,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkD,QAAQ,CAAC;UAEnD,IAAI,CAAAO,UAAA,GAAAP,QAAQ,cAAAO,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUH,IAAI,cAAAI,eAAA,eAAdA,eAAA,CAAgBL,OAAO,KAAAM,UAAA,GAAIT,QAAQ,cAAAS,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUL,IAAI,cAAAM,eAAA,eAAdA,eAAA,CAAgBN,IAAI,EAAE;YACnD1C,MAAM,GAAGsC,QAAQ,CAACI,IAAI,CAACA,IAAI;YAC3BvD,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEY,MAAM,CAAC2C,MAAM,CAAC;UACnF;QACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,KAAK,CAAC6C,OAAO,CAAC;QAC1D;MACF;;MAEA;MACA,IAAI5C,MAAM,CAAC2C,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI;UAAA,IAAAS,UAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,eAAA;UACFpE,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5DkD,QAAQ,GAAG,MAAM3D,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACrCQ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEkD,QAAQ,CAAC;UAEhE,IAAI,CAAAc,UAAA,GAAAd,QAAQ,cAAAc,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUV,IAAI,cAAAW,eAAA,eAAdA,eAAA,CAAgBZ,OAAO,KAAAa,UAAA,GAAIhB,QAAQ,cAAAgB,UAAA,gBAAAC,eAAA,GAARD,UAAA,CAAUZ,IAAI,cAAAa,eAAA,eAAdA,eAAA,CAAgBb,IAAI,EAAE;YACnD;YACA,MAAMc,OAAO,GAAGlB,QAAQ,CAACI,IAAI,CAACA,IAAI;YAClC1C,MAAM,GAAGwD,OAAO,CAACC,MAAM,CAACC,IAAI;cAAA,IAAAC,WAAA;cAAA,OAC1BD,IAAI,CAACP,IAAI,KAAK,OAAO,IACrBO,IAAI,CAACxB,QAAQ,IACbwB,IAAI,CAACnC,OAAO,MAAAoC,WAAA,GACZD,IAAI,CAACE,KAAK,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,WAAW,CAAC,CAAC,CAACrC,QAAQ,CAAC,OAAO,CAAC;YAAA,CAC7C,CAAC;YACDrC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,MAAM,CAAC2C,MAAM,CAAC;UAC1E;QACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;UACdZ,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEW,KAAK,CAAC6C,OAAO,CAAC;QACvE;MACF;;MAEA;MACA,IAAI5C,MAAM,CAAC2C,MAAM,GAAG,CAAC,EAAE;QACrB,MAAMmB,QAAQ,GAAG9D,MAAM,CAACyD,MAAM,CAACpC,KAAK,IAAI;UACtC,MAAM0C,YAAY,GAAGxD,aAAa,KAAK,KAAK,IACxBc,KAAK,CAAC6B,KAAK,KAAK3C,aAAa,IAC7B,CAACc,KAAK,CAAC6B,KAAK,CAAC,CAAC;;UAElC,MAAMc,YAAY,GAAGvD,aAAa,KAAK,KAAK,IACxBY,KAAK,CAAC4C,SAAS,KAAKxD,aAAa,IACjCY,KAAK,CAAC6C,KAAK,KAAKzD,aAAa,IAC7B,CAACY,KAAK,CAAC4C,SAAS,CAAC,CAAC;;UAEtC,MAAME,cAAc,GAAGxD,eAAe,KAAK,KAAK,IAC1BU,KAAK,CAACQ,OAAO,KAAKlB,eAAe,IACjC,CAACU,KAAK,CAACQ,OAAO,CAAC,CAAC;;UAEtC,OAAOkC,YAAY,IAAIC,YAAY,IAAIG,cAAc;QACvD,CAAC,CAAC;QAEFlE,SAAS,CAAC6D,QAAQ,CAAC;QACnB3E,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE0E,QAAQ,CAACnB,MAAM,EAAE,QAAQ,CAAC;QAErE,IAAImB,QAAQ,CAACnB,MAAM,KAAK,CAAC,EAAE;UACzBvC,QAAQ,CAAC,wEAAwE,CAAC;QACpF;MACF,CAAC,MAAM;QACL;QACAjB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpED,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrCD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAElCgB,QAAQ,CAAC,yFAAyF,CAAC;QACnGH,SAAS,CAAC,EAAE,CAAC;MACf;IAEF,CAAC,CAAC,OAAOmE,GAAG,EAAE;MACZjF,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEqE,GAAG,CAAC;MACvDhE,QAAQ,CAAC,0DAA0D,CAAC;MACpEH,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACI,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEnD;EACA,MAAMsB,uBAAuB,GAAG9D,OAAO,CAAC,MAAM;IAC5C,IAAI2F,QAAQ,GAAG9D,MAAM,CAACyD,MAAM,CAACpC,KAAK,IAAI;MAAA,IAAAgD,YAAA,EAAAC,cAAA,EAAAC,YAAA;MACpC,MAAMC,aAAa,GAAG,CAACnE,UAAU,MAAAgE,YAAA,GAC/BhD,KAAK,CAACuC,KAAK,cAAAS,YAAA,uBAAXA,YAAA,CAAaR,WAAW,CAAC,CAAC,CAACrC,QAAQ,CAACnB,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC,OAAAS,cAAA,GAC7DjD,KAAK,CAACQ,OAAO,cAAAyC,cAAA,uBAAbA,cAAA,CAAeT,WAAW,CAAC,CAAC,CAACrC,QAAQ,CAACnB,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC,OAAAU,YAAA,GAC/DlD,KAAK,CAACoD,KAAK,cAAAF,YAAA,uBAAXA,YAAA,CAAaV,WAAW,CAAC,CAAC,CAACrC,QAAQ,CAACnB,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC;MAE/D,MAAME,YAAY,GAAGxD,aAAa,KAAK,KAAK,IAAIc,KAAK,CAAC6B,KAAK,KAAK3C,aAAa;MAC7E,MAAMyD,YAAY,GAAGvD,aAAa,KAAK,KAAK,IAAIY,KAAK,CAAC4C,SAAS,KAAKxD,aAAa,IAAIY,KAAK,CAAC6C,KAAK,KAAKzD,aAAa;MAClH,MAAM0D,cAAc,GAAGxD,eAAe,KAAK,KAAK,IAAIU,KAAK,CAACQ,OAAO,KAAKlB,eAAe;MAErF,OAAO6D,aAAa,IAAIT,YAAY,IAAIC,YAAY,IAAIG,cAAc;IACxE,CAAC,CAAC;IAEF,OAAOL,QAAQ,CAACY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAAC;EAC/E,CAAC,EAAE,CAAC9E,MAAM,EAAEK,UAAU,EAAEE,aAAa,EAAEE,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAEvE;EACAzC,SAAS,CAAC,MAAM;IACdmE,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACAnE,SAAS,CAAC,MAAM;IACdmE,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAC9B,aAAa,EAAEE,aAAa,EAAEE,eAAe,EAAE0B,WAAW,CAAC,CAAC;EAEhE,oBACEvD,OAAA;IAAKmF,SAAS,EAAC,yBAAyB;IAAAc,QAAA,gBACtCjG,OAAA;MAAKmF,SAAS,EAAC,sBAAsB;MAAAc,QAAA,gBACnCjG,OAAA;QAAAiG,QAAA,EAAK5D,WAAW,GAAG,iBAAiB,GAAG;MAAe;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5DrG,OAAA;QAAAiG,QAAA,EAAI5D,WAAW,GAAG,iCAAiC,GAAG;MAAoC;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,eAGNrG,OAAA;MAAKmF,SAAS,EAAC,gBAAgB;MAAAc,QAAA,gBAC7BjG,OAAA;QAAKmF,SAAS,EAAC,gBAAgB;QAAAc,QAAA,eAC7BjG,OAAA;UACEqE,IAAI,EAAC,MAAM;UACXiC,WAAW,EAAEjE,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;UAClEkE,KAAK,EAAEhF,UAAW;UAClBiF,QAAQ,EAAGxF,CAAC,IAAKQ,aAAa,CAACR,CAAC,CAACyF,MAAM,CAACF,KAAK,CAAE;UAC/CpB,SAAS,EAAC;QAAc;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENrG,OAAA;QAAKmF,SAAS,EAAC,gBAAgB;QAAAc,QAAA,gBAC7BjG,OAAA;UACEuG,KAAK,EAAE9E,aAAc;UACrB+E,QAAQ,EAAGxF,CAAC,IAAKU,gBAAgB,CAACV,CAAC,CAACyF,MAAM,CAACF,KAAK,CAAE;UAClDpB,SAAS,EAAC,eAAe;UAAAc,QAAA,gBAEzBjG,OAAA;YAAQuG,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAE5D,WAAW,GAAG,QAAQ,GAAG;UAAS;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrErG,OAAA;YAAQuG,KAAK,EAAC,WAAW;YAAAN,QAAA,EAAE5D,WAAW,GAAG,WAAW,GAAG;UAAW;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC5ErG,OAAA;YAAQuG,KAAK,EAAC,SAAS;YAAAN,QAAA,EAAE5D,WAAW,GAAG,KAAK,GAAG;UAAU;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAETrG,OAAA;UACEuG,KAAK,EAAE5E,aAAc;UACrB6E,QAAQ,EAAGxF,CAAC,IAAKY,gBAAgB,CAACZ,CAAC,CAACyF,MAAM,CAACF,KAAK,CAAE;UAClDpB,SAAS,EAAC,eAAe;UAAAc,QAAA,eAEzBjG,OAAA;YAAQuG,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAE5D,WAAW,GAAG,eAAe,GAAG;UAAa;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtE,CAAC,eAETrG,OAAA;UACEuG,KAAK,EAAE1E,eAAgB;UACvB2E,QAAQ,EAAGxF,CAAC,IAAKc,kBAAkB,CAACd,CAAC,CAACyF,MAAM,CAACF,KAAK,CAAE;UACpDpB,SAAS,EAAC,eAAe;UAAAc,QAAA,eAEzBjG,OAAA;YAAQuG,KAAK,EAAC,KAAK;YAAAN,QAAA,EAAE5D,WAAW,GAAG,aAAa,GAAG;UAAc;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrG,OAAA;MAAKmF,SAAS,EAAC,eAAe;MAAAc,QAAA,EAC3B7E,OAAO,gBACNpB,OAAA;QAAKmF,SAAS,EAAC,eAAe;QAAAc,QAAA,gBAC5BjG,OAAA;UAAKmF,SAAS,EAAC;QAAiB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCrG,OAAA;UAAAiG,QAAA,EAAI5D,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJpF,KAAK,gBACPjB,OAAA;QAAKmF,SAAS,EAAC,aAAa;QAAAc,QAAA,gBAC1BjG,OAAA,CAACL,eAAe;UAACwF,SAAS,EAAC;QAAY;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CrG,OAAA;UAAAiG,QAAA,EAAK5D,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7ErG,OAAA;UAAAiG,QAAA,EAAIhF;QAAK;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdrG,OAAA;UAAQ0G,OAAO,EAAEnD,WAAY;UAAC4B,SAAS,EAAC,WAAW;UAAAc,QAAA,EAChD5D,WAAW,GAAG,aAAa,GAAG;QAAW;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJlD,uBAAuB,CAACU,MAAM,GAAG,CAAC,gBACpC7D,OAAA;QAAKmF,SAAS,EAAC,aAAa;QAAAc,QAAA,EACzB9C,uBAAuB,CAACwD,GAAG,CAAC,CAACpE,KAAK,EAAEW,KAAK,kBACxClD,OAAA;UAAiBmF,SAAS,EAAC,YAAY;UAAAc,QAAA,gBAErCjG,OAAA;YAAKmF,SAAS,EAAC,YAAY;YAACuB,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAACC,KAAK,CAAE;YAAA+C,QAAA,gBAChEjG,OAAA;cAAKmF,SAAS,EAAC,sBAAsB;cAAAc,QAAA,gBACnCjG,OAAA;gBACE4G,GAAG,EAAEtE,eAAe,CAACC,KAAK,CAAE;gBAC5BsE,GAAG,EAAEtE,KAAK,CAACuC,KAAM;gBACjBK,SAAS,EAAC,iBAAiB;gBAC3B/D,OAAO,EAAC,MAAM;gBACd0F,OAAO,EAAG9F,CAAC,IAAK;kBACd,IAAIuB,KAAK,CAACE,OAAO,IAAI,CAACF,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;oBAC7D,IAAIE,OAAO,GAAGL,KAAK,CAACE,OAAO;oBAC3B,MAAMI,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;oBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;oBACpC,MAAMmE,SAAS,GAAG,CACf,8BAA6BnE,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;oBACD,MAAMoE,UAAU,GAAGhG,CAAC,CAACyF,MAAM,CAACG,GAAG;oBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACC,GAAG,IAAIH,UAAU,CAACtE,QAAQ,CAACyE,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC1F,IAAIJ,YAAY,GAAGF,SAAS,CAAClD,MAAM,GAAG,CAAC,EAAE;sBACvC7C,CAAC,CAACyF,MAAM,CAACG,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;oBAC5C,CAAC,MAAM;sBACLjG,CAAC,CAACyF,MAAM,CAACG,GAAG,GAAG,0BAA0B;oBAC3C;kBACF,CAAC,MAAM;oBACL5F,CAAC,CAACyF,MAAM,CAACG,GAAG,GAAG,0BAA0B;kBAC3C;gBACF;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFrG,OAAA;gBAAKmF,SAAS,EAAC,cAAc;gBAAAc,QAAA,eAC3BjG,OAAA,CAACR,YAAY;kBAAC2F,SAAS,EAAC;gBAAW;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACNrG,OAAA;gBAAKmF,SAAS,EAAC,gBAAgB;gBAAAc,QAAA,EAC5B1D,KAAK,CAAC+E,QAAQ,IAAI;cAAO;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,EACL9D,KAAK,CAACgF,SAAS,IAAIhF,KAAK,CAACgF,SAAS,CAAC1D,MAAM,GAAG,CAAC,iBAC5C7D,OAAA;gBAAKmF,SAAS,EAAC,gBAAgB;gBAAAc,QAAA,gBAC7BjG,OAAA,CAACN,YAAY;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,MAElB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENrG,OAAA;cAAKmF,SAAS,EAAC,oBAAoB;cAAAc,QAAA,gBACjCjG,OAAA;gBAAImF,SAAS,EAAC,aAAa;gBAAAc,QAAA,EAAE1D,KAAK,CAACuC;cAAK;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9CrG,OAAA;gBAAKmF,SAAS,EAAC,YAAY;gBAAAc,QAAA,gBACzBjG,OAAA;kBAAMmF,SAAS,EAAC,eAAe;kBAAAc,QAAA,EAAEnD,cAAc,CAACP,KAAK,CAACQ,OAAO;gBAAC;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtErG,OAAA;kBAAMmF,SAAS,EAAC,aAAa;kBAAAc,QAAA,EAC1BxE,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEY,WAAW,GAAI,aAAYE,KAAK,CAAC4C,SAAS,IAAI5C,KAAK,CAAC6C,KAAM,EAAC,GAAI,SAAQ7C,KAAK,CAAC4C,SAAS,IAAI5C,KAAK,CAAC6C,KAAM,EAAC,GACvG,QAAO7C,KAAK,CAAC4C,SAAS,IAAI5C,KAAK,CAAC6C,KAAM;gBAAC;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNrG,OAAA;gBAAKmF,SAAS,EAAC,YAAY;gBAAAc,QAAA,GACxB1D,KAAK,CAACoD,KAAK,iBAAI3F,OAAA;kBAAMmF,SAAS,EAAC,WAAW;kBAAAc,QAAA,EAAE1D,KAAK,CAACoD;gBAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/D9D,KAAK,CAACiF,eAAe,IAAIjF,KAAK,CAACiF,eAAe,MAAMjF,KAAK,CAAC4C,SAAS,IAAI5C,KAAK,CAAC6C,KAAK,CAAC,iBAClFpF,OAAA;kBAAMmF,SAAS,EAAC,YAAY;kBAAAc,QAAA,GACzB5D,WAAW,GAAG,qBAAqB,GAAG,cAAc,EACpDZ,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAChEY,WAAW,GAAI,aAAYE,KAAK,CAACiF,eAAgB,EAAC,GAAI,SAAQjF,KAAK,CAACiF,eAAgB,EAAC,GACrF,QAAOjF,KAAK,CAACiF,eAAgB,EAAC;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLtE,iBAAiB,KAAKmB,KAAK,iBAC1BlD,OAAA;YAAKmF,SAAS,EAAC,qBAAqB;YAAAc,QAAA,eAClCjG,OAAA;cAAKmF,SAAS,EAAC,sBAAsB;cAAAc,QAAA,gBACnCjG,OAAA;gBAAKmF,SAAS,EAAC,sBAAsB;gBAAAc,QAAA,EAClC1D,KAAK,CAACa,QAAQ,gBACbpD,OAAA;kBACEyH,GAAG,EAAGA,GAAG,IAAKvF,WAAW,CAACuF,GAAG,CAAE;kBAC/BC,QAAQ;kBACRC,QAAQ;kBACRC,WAAW;kBACXC,OAAO,EAAC,UAAU;kBAClBC,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAC,MAAM;kBACbC,MAAM,EAAE1F,eAAe,CAACC,KAAK,CAAE;kBAC/B0F,KAAK,EAAE;oBACLH,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdG,eAAe,EAAE,MAAM;oBACvBC,SAAS,EAAE;kBACb,CAAE;kBACFrB,OAAO,EAAG9F,CAAC,IAAKoB,aAAa,CAAE,yBAAwBG,KAAK,CAACuC,KAAM,EAAC,CAAE;kBACtEsD,SAAS,EAAEA,CAAA,KAAMhG,aAAa,CAAC,IAAI,CAAE;kBACrCiG,WAAW,EAAC,WAAW;kBAAApC,QAAA,gBAEvBjG,OAAA;oBAAQ4G,GAAG,EAAErE,KAAK,CAACc,cAAc,IAAId,KAAK,CAACa,QAAS;oBAACiB,IAAI,EAAC;kBAAW;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACvE9D,KAAK,CAACgF,SAAS,IAAIhF,KAAK,CAACgF,SAAS,CAAC1D,MAAM,GAAG,CAAC,IAAItB,KAAK,CAACgF,SAAS,CAACZ,GAAG,CAAC,CAAC2B,QAAQ,EAAEpF,KAAK,kBACpFlD,OAAA;oBAEEuI,IAAI,EAAC,WAAW;oBAChB3B,GAAG,EAAE0B,QAAQ,CAACnB,GAAI;oBAClBqB,OAAO,EAAEF,QAAQ,CAACG,QAAS;oBAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;oBAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAI3F,KAAK,KAAK;kBAAE,GALrC,GAAEoF,QAAQ,CAACG,QAAS,IAAGvF,KAAM,EAAC;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMrC,CACF,CAAC,EAAC,8CAEL;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,GACN9D,KAAK,CAACE,OAAO,gBACfzC,OAAA;kBACE4G,GAAG,EAAG,iCAAgCrE,KAAK,CAACE,OAAQ,mBAAmB;kBACvEqC,KAAK,EAAEvC,KAAK,CAACuC,KAAM;kBACnBgE,WAAW,EAAC,GAAG;kBACfC,eAAe;kBACfd,KAAK,EAAE;oBAAEH,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE,MAAM;oBAAEiB,MAAM,EAAE;kBAAO;gBAAE;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,gBAEVrG,OAAA;kBAAKmF,SAAS,EAAC,aAAa;kBAAAc,QAAA,gBAC1BjG,OAAA;oBAAKmF,SAAS,EAAC,YAAY;oBAAAc,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpCrG,OAAA;oBAAAiG,QAAA,EAAI;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1BrG,OAAA;oBAAAiG,QAAA,EAAI9D,UAAU,IAAI;kBAA4C;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENrG,OAAA;gBAAKmF,SAAS,EAAC,oBAAoB;gBAAAc,QAAA,gBACjCjG,OAAA;kBAAImF,SAAS,EAAC,qBAAqB;kBAAAc,QAAA,EAAE1D,KAAK,CAACuC;gBAAK;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtDrG,OAAA;kBAAKmF,SAAS,EAAC,oBAAoB;kBAAAc,QAAA,gBACjCjG,OAAA;oBAAAiG,QAAA,EAAOnD,cAAc,CAACP,KAAK,CAACQ,OAAO;kBAAC;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5CrG,OAAA;oBAAAiG,QAAA,EAAM;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACdrG,OAAA;oBAAAiG,QAAA,GAAM,QAAM,EAAC1D,KAAK,CAAC4C,SAAS,IAAI5C,KAAK,CAAC6C,KAAK;kBAAA;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACNrG,OAAA;kBAAKmF,SAAS,EAAC,uBAAuB;kBAAAc,QAAA,eACpCjG,OAAA;oBACEmF,SAAS,EAAC,oBAAoB;oBAC9BuB,OAAO,EAAEA,CAAA,KAAM1E,oBAAoB,CAAC,IAAI,CAAE;oBAAAiE,QAAA,gBAE1CjG,OAAA;sBAAAiG,QAAA,EAAM;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACdrG,OAAA;sBAAAiG,QAAA,EAAM;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA,GAhJOnD,KAAK;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiJV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENrG,OAAA;QAAKmF,SAAS,EAAC,aAAa;QAAAc,QAAA,gBAC1BjG,OAAA,CAACP,eAAe;UAAC0F,SAAS,EAAC;QAAY;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CrG,OAAA;UAAAiG,QAAA,EAAK5D,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1ErG,OAAA;UAAAiG,QAAA,EAAI5D,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJrG,OAAA;UAAGmF,SAAS,EAAC,YAAY;UAAAc,QAAA,EAAE5D,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnG,EAAA,CA9dID,YAAY;EAAA,QAEHV,WAAW;AAAA;AAAA0J,EAAA,GAFpBhJ,YAAY;AAgelB,eAAeA,YAAY;AAAC,IAAAgJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}