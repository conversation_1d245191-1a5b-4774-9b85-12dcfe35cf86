// ✅ Fixed block around line 1066 with React.Fragment wrapper
<div className="videos-grid">
  {filteredAndSortedVideos.map((video, index) => (
    <React.Fragment key={index}>
      <div className="video-item">
        {/* Video Card */}
        <div className="video-card" onClick={() => handleShowVideo(index)}>
          <div className="video-card-thumbnail">
            <img
              src={getThumbnailUrl(video)}
              alt={video.title}
              className="thumbnail-image"
              loading="lazy"
              onError={(e) => {
                if (video.videoID && !video.videoID.includes('amazonaws.com')) {
                  let videoId = video.videoID;
                  const match = videoId.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
                  videoId = match ? match[1] : videoId;
                  const fallbacks = [
                    `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
                    `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,
                    `https://img.youtube.com/vi/${videoId}/default.jpg`,
                    '/api/placeholder/320/180'
                  ];
                  const currentSrc = e.target.src;
                  const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));
                  if (currentIndex < fallbacks.length - 1) {
                    e.target.src = fallbacks[currentIndex + 1];
                  } else {
                    e.target.src = '/api/placeholder/320/180';
                  }
                } else {
                  e.target.src = '/api/placeholder/320/180';
                }
              }}
            />
            <div className="play-overlay">
              <FaPlayCircle className="play-icon" />
            </div>
            <div className="video-duration">
              {video.duration || "Video"}
            </div>
            {video.subtitles && video.subtitles.length > 0 && (
              <div className="subtitle-badge">
                <TbInfoCircle />
                CC
              </div>
            )}
          </div>

          <div className="video-card-content">
            <h3 className="video-title">{video.title}</h3>
            <div className="video-meta">
              <span className="video-subject">{getSubjectName(video.subject)}</span>
              <span className="video-class">
                {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'
                  ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)
                  : `Form ${video.className || video.class}`}
              </span>
            </div>
            <div className="video-tags">
              {video.topic && <span className="topic-tag">{video.topic}</span>}
              {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (
                <span className="shared-tag">
                  {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}
                  {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'
                    ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)
                    : `Form ${video.sharedFromClass}`}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Inline Video Player */}
        {currentVideoIndex === index && (
          <div className="inline-video-player">
            <video
              ref={(ref) => setVideoRef(ref)}
              controls
              autoPlay
              playsInline
              preload="metadata"
              width="100%"
              height="100%"
              poster={getThumbnailUrl(video)}
              style={{
                width: '100%',
                height: '100%',
                backgroundColor: '#000',
                objectFit: 'contain'
              }}
              onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}
              onCanPlay={() => setVideoError(null)}
              crossOrigin="anonymous"
            >
              <source src={video.signedVideoUrl || video.videoUrl} type="video/mp4" />
              {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (
                <track
                  key={`${subtitle.language}-${index}`}
                  kind="subtitles"
                  src={subtitle.url}
                  srcLang={subtitle.language}
                  label={subtitle.languageName}
                  default={subtitle.isDefault || index === 0}
                />
              ))}
              Your browser does not support the video tag.
            </video>
          </div>
        )}
      </div>
    </React.Fragment>
  ))}
</div>
