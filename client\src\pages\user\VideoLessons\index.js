import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';
import { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';
import { MdVerified } from 'react-icons/md';
import { getStudyMaterial, getAllVideos } from '../../../apicalls/study';
import './index.css';

const VideoLessons = () => {
  // Redux state with completely safe destructuring
  const user = useSelector(state => {
    try {
      console.log('🔍 Full Redux state structure:', state);
      console.log('🔍 Available state keys:', Object.keys(state || {}));

      // Handle different possible Redux state structures
      if (state && state.users && state.users.user) {
        console.log('✅ Found user in state.users.user');
        return state.users.user;
      }
      if (state && state.user) {
        console.log('✅ Found user in state.user');
        return state.user;
      }
      if (state && state.auth && state.auth.user) {
        console.log('✅ Found user in state.auth.user');
        return state.auth.user;
      }

      console.log('❌ No user found in Redux state');

      // Check localStorage as fallback
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          console.log('✅ Found user in localStorage');
          return JSON.parse(storedUser);
        } catch (e) {
          console.log('❌ Failed to parse stored user');
        }
      }

      console.log('❌ No user found anywhere');
      return null;
    } catch (error) {
      console.log('💥 Error accessing Redux state:', error);
      return null;
    }
  });

  // State variables
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('primary');
  const [selectedClass, setSelectedClass] = useState('all');
  const [selectedSubject, setSelectedSubject] = useState('all');
  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);
  const [videoRef, setVideoRef] = useState(null);
  const [videoError, setVideoError] = useState(null);

  // Comments state
  const [comments, setComments] = useState({});
  const [newComment, setNewComment] = useState('');
  const [commentsExpanded, setCommentsExpanded] = useState(false);
  const [replyingTo, setReplyingTo] = useState(null);

  // Language detection
  const isKiswahili = selectedLevel === 'primary_kiswahili';

  // Helper functions
  const getThumbnailUrl = (video) => {
    // Priority 1: Use actual thumbnail from database
    if (video.thumbnailUrl && !video.thumbnailUrl.includes('youtube.com')) {
      return video.thumbnailUrl;
    }

    // Priority 2: Use video poster/preview image from database
    if (video.posterUrl) {
      return video.posterUrl;
    }

    // Priority 3: Use image field from database
    if (video.image) {
      return video.image;
    }

    // Priority 4: For YouTube videos only, use YouTube thumbnail as fallback
    if (video.videoID && !video.videoID.includes('amazonaws.com') && !video.videoUrl) {
      let videoId = video.videoID;
      const match = videoId.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
      videoId = match ? match[1] : videoId;
      return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
    }

    // Priority 5: Use a clean placeholder for uploaded videos
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjNDA3QkZGIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiPkVkdWNhdGlvbmFsIFZpZGVvPC90ZXh0Pjwvc3ZnPg==';
  };

  const getSubjectName = (subject) => {
    const subjectMap = {
      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',
      'english': isKiswahili ? 'Kiingereza' : 'English',
      'kiswahili': 'Kiswahili',
      'science': isKiswahili ? 'Sayansi' : 'Science',
      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',
      'civics': isKiswahili ? 'Uraia' : 'Civics',
      'history': isKiswahili ? 'Historia' : 'History',
      'geography': isKiswahili ? 'Jiografia' : 'Geography',
      'biology': isKiswahili ? 'Biolojia' : 'Biology',
      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',
      'physics': isKiswahili ? 'Fizikia' : 'Physics'
    };
    return subjectMap[subject] || subject;
  };

  // Comment helper functions
  const getCurrentVideoComments = () => {
    if (currentVideoIndex === null) return [];
    const video = filteredAndSortedVideos[currentVideoIndex];
    return comments[video?.id] || [];
  };

  const formatTimeAgo = (timestamp) => {
    if (!timestamp) return 'Just now';
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  };

  // Comment handlers
  const handleAddComment = async () => {
    if (!newComment.trim() || currentVideoIndex === null) return;

    const video = filteredAndSortedVideos[currentVideoIndex];

    // Get user name properly
    const userName = user?.name || user?.firstName || user?.username || 'Student';
    const fullName = user?.firstName && user?.lastName
      ? `${user.firstName} ${user.lastName}`
      : userName;

    const comment = {
      id: Date.now().toString(),
      text: newComment.trim(),
      author: fullName,
      user: user?._id,
      userRole: user?.role,
      isAdmin: user?.role === 'admin',
      timestamp: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      likes: 0,
      likedBy: []
    };

    setComments(prev => ({
      ...prev,
      [video.id]: [...(prev[video.id] || []), comment]
    }));

    setNewComment('');
  };

  const handleLikeComment = (commentId) => {
    if (!user?._id || currentVideoIndex === null) return;

    const video = filteredAndSortedVideos[currentVideoIndex];
    setComments(prev => ({
      ...prev,
      [video.id]: (prev[video.id] || []).map(comment => {
        if (comment.id === commentId) {
          const isLiked = comment.likedBy?.includes(user._id);
          return {
            ...comment,
            likes: isLiked ? comment.likes - 1 : comment.likes + 1,
            likedBy: isLiked
              ? comment.likedBy.filter(id => id !== user._id)
              : [...(comment.likedBy || []), user._id]
          };
        }
        return comment;
      })
    }));
  };

  const handleDeleteComment = (commentId) => {
    if (currentVideoIndex === null) return;

    const video = filteredAndSortedVideos[currentVideoIndex];
    setComments(prev => ({
      ...prev,
      [video.id]: (prev[video.id] || []).filter(comment => comment.id !== commentId)
    }));
  };

  // Video handlers
  const handleShowVideo = async (index) => {
    const video = filteredAndSortedVideos[index];
    setCurrentVideoIndex(index);
    setVideoError(null);

    // Get signed URL for S3 videos if needed
    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {
      try {
        // You would implement getSignedVideoUrl function here
        // const signedUrl = await getSignedVideoUrl(video.videoUrl);
        // video.signedVideoUrl = signedUrl;
        video.signedVideoUrl = video.videoUrl;
      } catch (error) {
        console.warn('Failed to get signed URL, using original URL');
        video.signedVideoUrl = video.videoUrl;
      }
    }
  };

  // Fetch videos function
  const fetchVideos = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🎥 Attempting to fetch videos from database...');

      let response = null;
      let videos = [];

      // Try Method 1: Get all videos (might not require authentication)
      try {
        console.log('📡 Trying getAllVideos endpoint...');
        response = await getAllVideos();
        console.log('getAllVideos response:', response);

        if (response?.success && response?.data) {
          videos = response.data;
          console.log('✅ Successfully loaded videos from getAllVideos:', videos.length);
        }
      } catch (error) {
        console.log('❌ getAllVideos failed:', error.message);
      }

      // Try Method 2: Get study materials with minimal filters
      if (videos.length === 0) {
        try {
          console.log('📡 Trying getStudyMaterial endpoint...');
          const filters = {
            level: selectedLevel,
            type: 'video'
          };

          response = await getStudyMaterial(filters);
          console.log('getStudyMaterial response:', response);

          if (response?.data?.success && response?.data?.data) {
            videos = response.data.data;
            console.log('✅ Successfully loaded videos from getStudyMaterial:', videos.length);
          }
        } catch (error) {
          console.log('❌ getStudyMaterial failed:', error.message);
        }
      }

      // Try Method 3: Get study materials without filters
      if (videos.length === 0) {
        try {
          console.log('📡 Trying getStudyMaterial without filters...');
          response = await getStudyMaterial({});
          console.log('getStudyMaterial (no filters) response:', response);

          if (response?.data?.success && response?.data?.data) {
            // Filter for videos only on the client side
            const allData = response.data.data;
            videos = allData.filter(item =>
              item.type === 'video' ||
              item.videoUrl ||
              item.videoID ||
              item.title?.toLowerCase().includes('video')
            );
            console.log('✅ Successfully loaded and filtered videos:', videos.length);
          }
        } catch (error) {
          console.log('❌ getStudyMaterial (no filters) failed:', error.message);
        }
      }

      // Apply client-side filtering if we have videos
      if (videos.length > 0) {
        const filtered = videos.filter(video => {
          const matchesLevel = selectedLevel === 'all' ||
                              video.level === selectedLevel ||
                              !video.level; // Include videos without level specified

          const matchesClass = selectedClass === 'all' ||
                              video.className === selectedClass ||
                              video.class === selectedClass ||
                              !video.className; // Include videos without class specified

          const matchesSubject = selectedSubject === 'all' ||
                                video.subject === selectedSubject ||
                                !video.subject; // Include videos without subject specified

          return matchesLevel && matchesClass && matchesSubject;
        });

        setVideos(filtered);
        console.log('🎯 Applied filters, showing', filtered.length, 'videos');

        if (filtered.length === 0) {
          setError('No videos found for the selected filters. Try changing your selection.');
        }
      } else {
        // No videos found from any method
        console.log('❌ No videos found from database, this might indicate:');
        console.log('   - Database is empty');
        console.log('   - Authentication required');
        console.log('   - API endpoints changed');
        console.log('   - Server is down');

        setError('Unable to load videos from database. Please check if videos are uploaded to the system.');
        setVideos([]);
      }

    } catch (err) {
      console.error('💥 Critical error in fetchVideos:', err);
      setError('Failed to connect to the server. Please try again later.');
      setVideos([]);
    } finally {
      setLoading(false);
    }
  }, [selectedLevel, selectedClass, selectedSubject]);

  // Filter and sort videos
  const filteredAndSortedVideos = useMemo(() => {
    let filtered = videos.filter(video => {
      const matchesSearch = !searchTerm ||
        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;
      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;
      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;

      return matchesSearch && matchesLevel && matchesClass && matchesSubject;
    });

    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);

  // Load videos on component mount and when filters change
  useEffect(() => {
    fetchVideos();
  }, [fetchVideos]);

  // Refetch videos when level, class, or subject changes
  useEffect(() => {
    fetchVideos();
  }, [selectedLevel, selectedClass, selectedSubject, fetchVideos]);

  return (
    <div className="video-lessons-container">
      <div className="video-lessons-header">
        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>
        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>
      </div>

      {/* Search and Filters */}
      <div className="video-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-section">
          <select
            value={selectedLevel}
            onChange={(e) => setSelectedLevel(e.target.value)}
            className="filter-select"
          >
            <option value="primary">{isKiswahili ? 'Msingi' : 'Primary'}</option>
            <option value="secondary">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>
            <option value="advance">{isKiswahili ? 'Juu' : 'Advanced'}</option>
          </select>

          <select
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
            className="filter-select"
          >
            <option value="all">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>
            {/* Add class options based on selected level */}
          </select>

          <select
            value={selectedSubject}
            onChange={(e) => setSelectedSubject(e.target.value)}
            className="filter-select"
          >
            <option value="all">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>
            {/* Add subject options */}
          </select>
        </div>
      </div>

      {/* Video Content */}
      <div className="video-content">
        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>
          </div>
        ) : error ? (
          <div className="error-state">
            <TbAlertTriangle className="error-icon" />
            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>
            <p>{error}</p>
            <button onClick={fetchVideos} className="retry-btn">
              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}
            </button>
          </div>
        ) : filteredAndSortedVideos.length > 0 ? (
          <div className="videos-grid">
            {filteredAndSortedVideos.map((video, index) => (
              <div key={index} className="video-item">
                {/* Video Card */}
                <div className="video-card" onClick={() => handleShowVideo(index)}>
                  <div className="video-card-thumbnail">
                    <img
                      src={getThumbnailUrl(video)}
                      alt={video.title}
                      className="thumbnail-image"
                      loading="lazy"
                      onError={(e) => {
                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {
                          let videoId = video.videoID;
                          const match = videoId.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
                          videoId = match ? match[1] : videoId;
                          const fallbacks = [
                            `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,
                            `https://img.youtube.com/vi/${videoId}/default.jpg`,
                            'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4='
                          ];
                          const currentSrc = e.target.src;
                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop().split('.')[0]));
                          if (currentIndex < fallbacks.length - 1) {
                            e.target.src = fallbacks[currentIndex + 1];
                          }
                        } else {
                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNDQgNzJMMTc2IDkwTDE0NCAxMDhWNzJaIiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjE2MCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlZpZGVvIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4=';
                        }
                      }}
                    />
                    <div className="play-overlay">
                      <FaPlayCircle className="play-icon" />
                    </div>
                    <div className="video-duration">
                      {video.duration || "Video"}
                    </div>
                    {video.subtitles && video.subtitles.length > 0 && (
                      <div className="subtitle-badge">
                        <TbInfoCircle />
                        CC
                      </div>
                    )}
                  </div>

                  <div className="video-card-content">
                    <h3 className="video-title">{video.title}</h3>
                    <div className="video-meta">
                      <span className="video-subject">{getSubjectName(video.subject)}</span>
                      <span className="video-class">
                        {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'
                          ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)
                          : `Form ${video.className || video.class}`}
                      </span>
                    </div>
                    <div className="video-tags">
                      {video.topic && <span className="topic-tag">{video.topic}</span>}
                      {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (
                        <span className="shared-tag">
                          {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}
                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'
                            ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)
                            : `Form ${video.sharedFromClass}`}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Inline Video Player */}
                {currentVideoIndex === index && (
                  <div className="inline-video-player">
                    <div className="youtube-style-layout">
                      <div className="youtube-video-player">
                        {video.videoUrl ? (
                          <video
                            ref={(ref) => setVideoRef(ref)}
                            controls
                            autoPlay
                            playsInline
                            preload="metadata"
                            width="100%"
                            height="100%"
                            poster={getThumbnailUrl(video)}
                            style={{
                              width: '100%',
                              height: '100%',
                              backgroundColor: '#000',
                              objectFit: 'contain'
                            }}
                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}
                            onCanPlay={() => setVideoError(null)}
                            crossOrigin="anonymous"
                          >
                            <source src={video.signedVideoUrl || video.videoUrl} type="video/mp4" />
                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (
                              <track
                                key={`${subtitle.language}-${index}`}
                                kind="subtitles"
                                src={subtitle.url}
                                srcLang={subtitle.language}
                                label={subtitle.languageName}
                                default={subtitle.isDefault || index === 0}
                              />
                            ))}
                            Your browser does not support the video tag.
                          </video>
                        ) : video.videoID ? (
                          <iframe
                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}
                            title={video.title}
                            frameBorder="0"
                            allowFullScreen
                            style={{ width: '100%', height: '100%', border: 'none' }}
                          ></iframe>
                        ) : (
                          <div className="video-error">
                            <div className="error-icon">⚠️</div>
                            <h3>Video Unavailable</h3>
                            <p>{videoError || "This video cannot be played at the moment."}</p>
                          </div>
                        )}
                      </div>

                      <div className="youtube-video-info">
                        <h1 className="youtube-video-title">{video.title}</h1>
                        <div className="youtube-video-meta">
                          <span>{getSubjectName(video.subject)}</span>
                          <span>•</span>
                          <span>Class {video.className || video.class}</span>
                          {video.level && (
                            <>
                              <span>•</span>
                              <span>{video.level}</span>
                            </>
                          )}
                        </div>
                        <div className="youtube-video-actions">
                          <button
                            className={`youtube-action-btn ${commentsExpanded ? 'active' : ''}`}
                            onClick={() => setCommentsExpanded(!commentsExpanded)}
                          >
                            <span>💬</span>
                            <span>Comments</span>
                          </button>
                          <button className="youtube-action-btn">
                            <span>👍</span>
                            <span>Like</span>
                          </button>
                          <button
                            className="youtube-action-btn"
                            onClick={() => setCurrentVideoIndex(null)}
                          >
                            <span>✕</span>
                            <span>Close</span>
                          </button>
                        </div>
                      </div>

                      {/* Comments Section */}
                      {commentsExpanded && (
                        <div className="youtube-comments-section">
                          <div className="youtube-comments-header">
                            <span>{getCurrentVideoComments().length} Comments</span>
                          </div>

                          {/* Add Comment */}
                          <div className="youtube-comment-input">
                            <div className="youtube-comment-avatar">
                              {(user?.name || user?.firstName || user?.username || 'Student')?.charAt(0)?.toUpperCase()}
                            </div>
                            <div style={{ flex: 1 }}>
                              <textarea
                                className="youtube-comment-input-field"
                                value={newComment}
                                onChange={(e) => setNewComment(e.target.value)}
                                placeholder="Add a comment..."
                                rows="1"
                                style={{
                                  minHeight: '20px',
                                  resize: 'none',
                                  overflow: 'hidden'
                                }}
                                onInput={(e) => {
                                  e.target.style.height = 'auto';
                                  e.target.style.height = e.target.scrollHeight + 'px';
                                }}
                              />
                              {newComment.trim() && (
                                <div className="youtube-comment-actions">
                                  <button
                                    className="youtube-comment-btn cancel"
                                    onClick={() => setNewComment('')}
                                  >
                                    Cancel
                                  </button>
                                  <button
                                    className="youtube-comment-btn submit"
                                    onClick={handleAddComment}
                                    disabled={!newComment.trim()}
                                  >
                                    Comment
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Comments List */}
                          <div className="youtube-comments-list">
                            {getCurrentVideoComments().length === 0 ? (
                              <div style={{ textAlign: 'center', padding: '40px 0', color: '#606060' }}>
                                <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>
                                <p>No comments yet. Be the first to share your thoughts!</p>
                              </div>
                            ) : (
                              getCurrentVideoComments().map((comment) => (
                                <div key={comment._id || comment.id} className="youtube-comment">
                                  <div className="youtube-comment-avatar">
                                    {comment.avatar || comment.author?.charAt(0)?.toUpperCase() || "A"}
                                  </div>
                                  <div className="youtube-comment-content">
                                    <div className="youtube-comment-header">
                                      <span className="youtube-comment-author">{comment.author}</span>
                                      {(comment.userRole === 'admin' || comment.isAdmin) && (
                                        <MdVerified style={{ color: '#1d9bf0', fontSize: '12px', marginLeft: '4px' }} title="Verified Admin" />
                                      )}
                                      <span className="youtube-comment-time">
                                        {formatTimeAgo(comment.createdAt || comment.timestamp)}
                                      </span>
                                    </div>
                                    <div className="youtube-comment-text">
                                      {comment.text}
                                    </div>
                                    <div className="youtube-comment-actions">
                                      <button
                                        onClick={() => handleLikeComment(comment._id || comment.id)}
                                        className={`youtube-comment-action ${comment.likedBy?.includes(user?._id) ? 'liked' : ''}`}
                                      >
                                        <span>{comment.likedBy?.includes(user?._id) ? '👍' : '👍'}</span>
                                        {comment.likes > 0 && <span>{comment.likes}</span>}
                                      </button>
                                      <button className="youtube-comment-action">
                                        <span>👎</span>
                                      </button>
                                      <button className="youtube-comment-action">
                                        Reply
                                      </button>
                                      {comment.user === user?._id && (
                                        <>
                                          <button className="youtube-comment-action">
                                            Edit
                                          </button>
                                          <button
                                            className="youtube-comment-action"
                                            onClick={() => {
                                              if (window.confirm('Are you sure you want to delete this comment?')) {
                                                handleDeleteComment(comment._id || comment.id);
                                              }
                                            }}
                                          >
                                            Delete
                                          </button>
                                        </>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="empty-state">
            <FaGraduationCap className="empty-icon" />
            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>
            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>
            <p className="suggestion">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoLessons;
