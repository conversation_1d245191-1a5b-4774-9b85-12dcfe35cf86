import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { FaPlayCircle, FaGraduationCap } from 'react-icons/fa';
import { TbInfoCircle, TbAlertTriangle } from 'react-icons/tb';
import { MdVerified } from 'react-icons/md';
import { getStudyMaterial } from '../../../apicalls/study';
import './index.css';

const VideoLessons = () => {
  // Redux state
  const { user } = useSelector(state => state.users);

  // State variables
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('primary');
  const [selectedClass, setSelectedClass] = useState('all');
  const [selectedSubject, setSelectedSubject] = useState('all');
  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);
  const [videoRef, setVideoRef] = useState(null);
  const [videoError, setVideoError] = useState(null);

  // Language detection
  const isKiswahili = selectedLevel === 'primary_kiswahili';

  // Helper functions
  const getThumbnailUrl = (video) => {
    if (video.thumbnailUrl) return video.thumbnailUrl;
    if (video.videoID && !video.videoID.includes('amazonaws.com')) {
      const videoId = video.videoID.includes('youtube.com') || video.videoID.includes('youtu.be')
        ? video.videoID.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)?.[1] || video.videoID
        : video.videoID;
      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
    }
    return '/api/placeholder/320/180';
  };

  const getSubjectName = (subject) => {
    const subjectMap = {
      'mathematics': isKiswahili ? 'Hisabati' : 'Mathematics',
      'english': isKiswahili ? 'Kiingereza' : 'English',
      'kiswahili': 'Kiswahili',
      'science': isKiswahili ? 'Sayansi' : 'Science',
      'social_studies': isKiswahili ? 'Maarifa ya Jamii' : 'Social Studies',
      'civics': isKiswahili ? 'Uraia' : 'Civics',
      'history': isKiswahili ? 'Historia' : 'History',
      'geography': isKiswahili ? 'Jiografia' : 'Geography',
      'biology': isKiswahili ? 'Biolojia' : 'Biology',
      'chemistry': isKiswahili ? 'Kemia' : 'Chemistry',
      'physics': isKiswahili ? 'Fizikia' : 'Physics'
    };
    return subjectMap[subject] || subject;
  };

  // Video handlers
  const handleShowVideo = async (index) => {
    const video = filteredAndSortedVideos[index];
    setCurrentVideoIndex(index);
    setVideoError(null);

    // Get signed URL for S3 videos if needed
    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {
      try {
        // You would implement getSignedVideoUrl function here
        // const signedUrl = await getSignedVideoUrl(video.videoUrl);
        // video.signedVideoUrl = signedUrl;
        video.signedVideoUrl = video.videoUrl;
      } catch (error) {
        console.warn('Failed to get signed URL, using original URL');
        video.signedVideoUrl = video.videoUrl;
      }
    }
  };

  // Fetch videos function
  const fetchVideos = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the proper API call for study materials (videos)
      const filters = {
        level: selectedLevel,
        class: selectedClass !== 'all' ? selectedClass : undefined,
        subject: selectedSubject !== 'all' ? selectedSubject : undefined,
        type: 'video' // Filter for videos only
      };

      const response = await getStudyMaterial(filters);

      if (response.data?.success) {
        setVideos(response.data.data || []);
      } else {
        throw new Error(response.data?.message || 'Failed to fetch videos');
      }
    } catch (err) {
      setError(err.message || 'Failed to load videos');
    } finally {
      setLoading(false);
    }
  }, [selectedLevel, selectedClass, selectedSubject]);

  // Filter and sort videos
  const filteredAndSortedVideos = useMemo(() => {
    let filtered = videos.filter(video => {
      const matchesSearch = !searchTerm ||
        video.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        video.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        video.topic?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesLevel = selectedLevel === 'all' || video.level === selectedLevel;
      const matchesClass = selectedClass === 'all' || video.className === selectedClass || video.class === selectedClass;
      const matchesSubject = selectedSubject === 'all' || video.subject === selectedSubject;

      return matchesSearch && matchesLevel && matchesClass && matchesSubject;
    });

    return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  }, [videos, searchTerm, selectedLevel, selectedClass, selectedSubject]);

  // Load videos on component mount
  useEffect(() => {
    fetchVideos();
  }, [fetchVideos]);

  return (
    <div className="video-lessons-container">
      <div className="video-lessons-header">
        <h1>{isKiswahili ? 'Masomo ya Video' : 'Video Lessons'}</h1>
        <p>{isKiswahili ? 'Jifunze kupitia video za kisasa' : 'Learn through modern video content'}</p>
      </div>

      {/* Search and Filters */}
      <div className="video-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder={isKiswahili ? 'Tafuta video...' : 'Search videos...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-section">
          <select
            value={selectedLevel}
            onChange={(e) => setSelectedLevel(e.target.value)}
            className="filter-select"
          >
            <option value="primary">{isKiswahili ? 'Msingi' : 'Primary'}</option>
            <option value="secondary">{isKiswahili ? 'Sekondari' : 'Secondary'}</option>
            <option value="advance">{isKiswahili ? 'Juu' : 'Advanced'}</option>
          </select>

          <select
            value={selectedClass}
            onChange={(e) => setSelectedClass(e.target.value)}
            className="filter-select"
          >
            <option value="all">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>
            {/* Add class options based on selected level */}
          </select>

          <select
            value={selectedSubject}
            onChange={(e) => setSelectedSubject(e.target.value)}
            className="filter-select"
          >
            <option value="all">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>
            {/* Add subject options */}
          </select>
        </div>
      </div>

      {/* Video Content */}
      <div className="video-content">
        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>
          </div>
        ) : error ? (
          <div className="error-state">
            <TbAlertTriangle className="error-icon" />
            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>
            <p>{error}</p>
            <button onClick={fetchVideos} className="retry-btn">
              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}
            </button>
          </div>
        ) : filteredAndSortedVideos.length > 0 ? (
          <div className="videos-grid">
            {filteredAndSortedVideos.map((video, index) => (
              <div key={index} className="video-item">
                {/* Video Card */}
                <div className="video-card" onClick={() => handleShowVideo(index)}>
                  <div className="video-card-thumbnail">
                    <img
                      src={getThumbnailUrl(video)}
                      alt={video.title}
                      className="thumbnail-image"
                      loading="lazy"
                      onError={(e) => {
                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {
                          let videoId = video.videoID;
                          const match = videoId.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
                          videoId = match ? match[1] : videoId;
                          const fallbacks = [
                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,
                            `https://img.youtube.com/vi/${videoId}/default.jpg`,
                            '/api/placeholder/320/180'
                          ];
                          const currentSrc = e.target.src;
                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));
                          if (currentIndex < fallbacks.length - 1) {
                            e.target.src = fallbacks[currentIndex + 1];
                          } else {
                            e.target.src = '/api/placeholder/320/180';
                          }
                        } else {
                          e.target.src = '/api/placeholder/320/180';
                        }
                      }}
                    />
                    <div className="play-overlay">
                      <FaPlayCircle className="play-icon" />
                    </div>
                    <div className="video-duration">
                      {video.duration || "Video"}
                    </div>
                    {video.subtitles && video.subtitles.length > 0 && (
                      <div className="subtitle-badge">
                        <TbInfoCircle />
                        CC
                      </div>
                    )}
                  </div>

                  <div className="video-card-content">
                    <h3 className="video-title">{video.title}</h3>
                    <div className="video-meta">
                      <span className="video-subject">{getSubjectName(video.subject)}</span>
                      <span className="video-class">
                        {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'
                          ? (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`)
                          : `Form ${video.className || video.class}`}
                      </span>
                    </div>
                    <div className="video-tags">
                      {video.topic && <span className="topic-tag">{video.topic}</span>}
                      {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (
                        <span className="shared-tag">
                          {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}
                          {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili'
                            ? (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`)
                            : `Form ${video.sharedFromClass}`}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Inline Video Player */}
                {currentVideoIndex === index && (
                  <div className="inline-video-player">
                    <div className="youtube-style-layout">
                      <div className="youtube-video-player">
                        {video.videoUrl ? (
                          <video
                            ref={(ref) => setVideoRef(ref)}
                            controls
                            autoPlay
                            playsInline
                            preload="metadata"
                            width="100%"
                            height="100%"
                            poster={getThumbnailUrl(video)}
                            style={{
                              width: '100%',
                              height: '100%',
                              backgroundColor: '#000',
                              objectFit: 'contain'
                            }}
                            onError={(e) => setVideoError(`Failed to load video: ${video.title}`)}
                            onCanPlay={() => setVideoError(null)}
                            crossOrigin="anonymous"
                          >
                            <source src={video.signedVideoUrl || video.videoUrl} type="video/mp4" />
                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (
                              <track
                                key={`${subtitle.language}-${index}`}
                                kind="subtitles"
                                src={subtitle.url}
                                srcLang={subtitle.language}
                                label={subtitle.languageName}
                                default={subtitle.isDefault || index === 0}
                              />
                            ))}
                            Your browser does not support the video tag.
                          </video>
                        ) : video.videoID ? (
                          <iframe
                            src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}
                            title={video.title}
                            frameBorder="0"
                            allowFullScreen
                            style={{ width: '100%', height: '100%', border: 'none' }}
                          ></iframe>
                        ) : (
                          <div className="video-error">
                            <div className="error-icon">⚠️</div>
                            <h3>Video Unavailable</h3>
                            <p>{videoError || "This video cannot be played at the moment."}</p>
                          </div>
                        )}
                      </div>

                      <div className="youtube-video-info">
                        <h1 className="youtube-video-title">{video.title}</h1>
                        <div className="youtube-video-meta">
                          <span>{getSubjectName(video.subject)}</span>
                          <span>•</span>
                          <span>Class {video.className || video.class}</span>
                        </div>
                        <div className="youtube-video-actions">
                          <button
                            className="youtube-action-btn"
                            onClick={() => setCurrentVideoIndex(null)}
                          >
                            <span>✕</span>
                            <span>Close</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="empty-state">
            <FaGraduationCap className="empty-icon" />
            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>
            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>
            <p className="suggestion">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoLessons;
